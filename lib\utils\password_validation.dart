import 'package:flutter/material.dart';
import 'package:dalti/base/color_data.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';

/// Password strength levels
enum PasswordStrength {
  weak,
  medium,
  strong,
  veryStrong,
}

/// Password validation result
class PasswordValidationResult {
  final bool isValid;
  final PasswordStrength strength;
  final List<String> failedRequirements;
  final List<String> passedRequirements;

  PasswordValidationResult({
    required this.isValid,
    required this.strength,
    required this.failedRequirements,
    required this.passedRequirements,
  });
}

/// Password validation utilities
class PasswordValidator {
  static const int minLength = 8;
  static const int maxLength = 128;

  /// Validate password and return detailed result
  static PasswordValidationResult validatePassword(String password) {
    List<String> failed = [];
    List<String> passed = [];

    // Check minimum length
    if (password.length >= minLength) {
      passed.add("At least $minLength characters");
    } else {
      failed.add("At least $minLength characters");
    }

    // Check maximum length
    if (password.length <= maxLength) {
      passed.add("No more than $maxLength characters");
    } else {
      failed.add("No more than $maxLength characters");
    }

    // Check for uppercase letter
    if (password.contains(RegExp(r'[A-Z]'))) {
      passed.add("At least one uppercase letter");
    } else {
      failed.add("At least one uppercase letter");
    }

    // Check for lowercase letter
    if (password.contains(RegExp(r'[a-z]'))) {
      passed.add("At least one lowercase letter");
    } else {
      failed.add("At least one lowercase letter");
    }

    // Check for number
    if (password.contains(RegExp(r'[0-9]'))) {
      passed.add("At least one number");
    } else {
      failed.add("At least one number");
    }

    // Check for special character
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      passed.add("At least one special character");
    } else {
      failed.add("At least one special character");
    }

    // Determine strength
    PasswordStrength strength = _calculateStrength(password, failed.isEmpty);

    return PasswordValidationResult(
      isValid: failed.isEmpty,
      strength: strength,
      failedRequirements: failed,
      passedRequirements: passed,
    );
  }

  /// Calculate password strength
  static PasswordStrength _calculateStrength(String password, bool meetsBasicRequirements) {
    if (!meetsBasicRequirements || password.length < minLength) {
      return PasswordStrength.weak;
    }

    int score = 0;

    // Length bonus
    if (password.length >= 12) score += 2;
    else if (password.length >= 10) score += 1;

    // Character variety
    if (password.contains(RegExp(r'[A-Z]'))) score += 1;
    if (password.contains(RegExp(r'[a-z]'))) score += 1;
    if (password.contains(RegExp(r'[0-9]'))) score += 1;
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) score += 1;

    // Additional complexity
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>].*[!@#$%^&*(),.?":{}|<>]'))) score += 1; // Multiple special chars
    if (password.contains(RegExp(r'[0-9].*[0-9].*[0-9]'))) score += 1; // Multiple numbers

    if (score >= 7) return PasswordStrength.veryStrong;
    if (score >= 5) return PasswordStrength.strong;
    if (score >= 3) return PasswordStrength.medium;
    return PasswordStrength.weak;
  }

  /// Get strength color
  static Color getStrengthColor(PasswordStrength strength) {
    switch (strength) {
      case PasswordStrength.weak:
        return daltiErrorRed;
      case PasswordStrength.medium:
        return daltiWarningYellow;
      case PasswordStrength.strong:
        return Colors.orange;
      case PasswordStrength.veryStrong:
        return daltiSuccessGreen;
    }
  }

  /// Get strength text
  static String getStrengthText(PasswordStrength strength) {
    switch (strength) {
      case PasswordStrength.weak:
        return "Weak";
      case PasswordStrength.medium:
        return "Medium";
      case PasswordStrength.strong:
        return "Strong";
      case PasswordStrength.veryStrong:
        return "Very Strong";
    }
  }

  /// Validate password confirmation
  static bool validatePasswordConfirmation(String password, String confirmation) {
    return password == confirmation && password.isNotEmpty;
  }
}

/// Widget to display password requirements
class PasswordRequirementsWidget extends StatelessWidget {
  final PasswordValidationResult validationResult;

  const PasswordRequirementsWidget({
    Key? key,
    required this.validationResult,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    List<String> allRequirements = [
      "At least ${PasswordValidator.minLength} characters",
      "At least one uppercase letter",
      "At least one lowercase letter",
      "At least one number",
      "At least one special character",
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        getCustomFont(
          "Password Requirements:",
          14,
          daltiTextBody,
          1,
          fontWeight: FontWeight.w600,
        ),
        getVerSpace(FetchPixels.getPixelHeight(8)),
        ...allRequirements.map((requirement) {
          bool isPassed = validationResult.passedRequirements.contains(requirement);
          return Padding(
            padding: EdgeInsets.only(bottom: FetchPixels.getPixelHeight(4)),
            child: Row(
              children: [
                Icon(
                  isPassed ? Icons.check_circle : Icons.radio_button_unchecked,
                  size: FetchPixels.getPixelHeight(16),
                  color: isPassed ? daltiSuccessGreen : daltiTextMuted,
                ),
                getHorSpace(FetchPixels.getPixelWidth(8)),
                Expanded(
                  child: getCustomFont(
                    requirement,
                    12,
                    isPassed ? daltiSuccessGreen : daltiTextMuted,
                    1,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ],
    );
  }
}

/// Widget to display password strength indicator
class PasswordStrengthIndicator extends StatelessWidget {
  final PasswordStrength strength;
  final String password;

  const PasswordStrengthIndicator({
    Key? key,
    required this.strength,
    required this.password,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    if (password.isEmpty) {
      return const SizedBox.shrink();
    }

    Color strengthColor = PasswordValidator.getStrengthColor(strength);
    String strengthText = PasswordValidator.getStrengthText(strength);
    double strengthValue = _getStrengthValue(strength);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            getCustomFont(
              "Password Strength:",
              12,
              daltiTextBody,
              1,
              fontWeight: FontWeight.w500,
            ),
            getCustomFont(
              strengthText,
              12,
              strengthColor,
              1,
              fontWeight: FontWeight.w600,
            ),
          ],
        ),
        getVerSpace(FetchPixels.getPixelHeight(4)),
        LinearProgressIndicator(
          value: strengthValue,
          backgroundColor: daltiDividerLine,
          valueColor: AlwaysStoppedAnimation<Color>(strengthColor),
          minHeight: FetchPixels.getPixelHeight(4),
        ),
      ],
    );
  }

  double _getStrengthValue(PasswordStrength strength) {
    switch (strength) {
      case PasswordStrength.weak:
        return 0.25;
      case PasswordStrength.medium:
        return 0.5;
      case PasswordStrength.strong:
        return 0.75;
      case PasswordStrength.veryStrong:
        return 1.0;
    }
  }
}
