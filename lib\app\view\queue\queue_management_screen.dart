import 'dart:async'; // Import for Timer
import 'dart:math'; // Import for max
import 'dart:convert'; // Import for jsonDecode
import 'package:qr_flutter/qr_flutter.dart';
import 'package:flutter/material.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:dalti/base/color_data.dart';
import 'package:dalti/base/constant.dart';
import 'package:web_socket_channel/web_socket_channel.dart'; // Added WebSocket import
import 'package:shared_preferences/shared_preferences.dart'; // Added for session ID
import 'package:intl/intl.dart'; // For date formatting
import 'package:dalti/services/queue_service.dart'
    hide prefsKeySessionId; // Corrected import
import 'package:collection/collection.dart'; // Import for firstWhereOrNull
import 'package:dalti/app/view/queue/swap_confirmation_dialog.dart'; // Import for SwapConfirmationDialog
import 'package:dalti/l10n/app_localizations.dart';

// START: Swap Request Data Models
class SwapRequestAppointment {
  final int id;
  final DateTime expectedAppointmentStartTime;
  final int customerFolderId; // Assuming this links to a user identifier

  SwapRequestAppointment({
    required this.id,
    required this.expectedAppointmentStartTime,
    required this.customerFolderId,
  });

  factory SwapRequestAppointment.fromJson(Map<String, dynamic> json) {
    return SwapRequestAppointment(
      id: json['id'] as int,
      expectedAppointmentStartTime: DateTime.parse(
        json['expectedAppointmentStartTime'] as String,
      ),
      customerFolderId: json['customerFolderId'] as int,
    );
  }
}

class SwapRequestUser {
  final String id; // UUID
  final String firstName;
  final String lastName;

  SwapRequestUser({
    required this.id,
    required this.firstName,
    required this.lastName,
  });

  factory SwapRequestUser.fromJson(Map<String, dynamic> json) {
    return SwapRequestUser(
      id: json['id'] as String,
      firstName: json['firstName'] as String? ?? 'Unknown',
      lastName: json['lastName'] as String? ?? 'User',
    );
  }

  String get fullName => '$firstName $lastName';
}

class SwapRequestInfo {
  final int id; // Swap Request ID
  final String status;
  final SwapRequestUser requestedBy;
  final SwapRequestAppointment appointment1; // Initiator's original appointment
  final SwapRequestAppointment appointment2; // Target's original appointment
  final DateTime createdAt;

  SwapRequestInfo({
    required this.id,
    required this.status,
    required this.requestedBy,
    required this.appointment1,
    required this.appointment2,
    required this.createdAt,
  });

  factory SwapRequestInfo.fromJson(Map<String, dynamic> json) {
    return SwapRequestInfo(
      id: json['id'] as int,
      status: json['status'] as String,
      requestedBy: SwapRequestUser.fromJson(
        json['requestedBy'] as Map<String, dynamic>,
      ),
      appointment1: SwapRequestAppointment.fromJson(
        json['appointment1'] as Map<String, dynamic>,
      ),
      appointment2: SwapRequestAppointment.fromJson(
        json['appointment2'] as Map<String, dynamic>,
      ),
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }
}
// END: Swap Request Data Models

// Enum for Queue Member Status
enum QueueStatus {
  Upcoming,
  CheckedIn,
  WaitingRoom,
  CalledIn,
  InProgress,
  Completed,
  Skipped,
  Requeued,
  Canceled,
}

Color getColorForStatus(QueueStatus status) {
  switch (status) {
    case QueueStatus.Upcoming:
      return Colors.grey;
    case QueueStatus.CheckedIn:
      return Colors.blue;
    case QueueStatus.WaitingRoom:
      return Colors.teal;
    case QueueStatus.CalledIn:
      return Colors.purple;
    case QueueStatus.InProgress:
      return Colors.orange;
    case QueueStatus.Completed:
      return Colors.green;
    case QueueStatus.Skipped:
      return Colors.red;
    case QueueStatus.Requeued:
      return Colors.indigo;
    case QueueStatus.Canceled:
      return Colors.grey.shade400;
    default:
      return Colors.grey;
  }
}

String getStatusTitle(BuildContext context, QueueStatus status) {
  final localizations = AppLocalizations.of(context)!;

  switch (status) {
    case QueueStatus.Upcoming:
      return localizations.queueStatusUpcoming;
    case QueueStatus.CheckedIn:
      return localizations.queueStatusCheckedIn;
    case QueueStatus.WaitingRoom:
      return localizations.queueStatusWaitingRoom;
    case QueueStatus.CalledIn:
      return localizations.queueStatusCalledIn;
    case QueueStatus.InProgress:
      return localizations.queueStatusInProgress;
    case QueueStatus.Completed:
      return localizations.queueStatusCompleted;
    case QueueStatus.Skipped:
      return localizations.queueStatusSkipped;
    case QueueStatus.Requeued:
      return localizations.queueStatusRequeued;
    case QueueStatus.Canceled:
      return localizations.queueStatusCanceled;
    default:
      return localizations.queueStatusUnknown;
  }
}

String getStatusMessage(
  BuildContext context,
  QueueStatus status,
  int position,
) {
  final localizations = AppLocalizations.of(context)!;

  switch (status) {
    case QueueStatus.Upcoming:
      return localizations.queueMessageUpcoming(position);
    case QueueStatus.CheckedIn:
      return localizations.queueMessageCheckedIn;
    case QueueStatus.WaitingRoom:
      return localizations.queueMessageWaitingRoom(position);
    case QueueStatus.CalledIn:
      return localizations.queueMessageCalledIn;
    case QueueStatus.InProgress:
      return localizations.queueMessageInProgress;
    case QueueStatus.Completed:
      return localizations.queueMessageCompleted;
    case QueueStatus.Skipped:
      return localizations.queueMessageSkipped;
    case QueueStatus.Requeued:
      return localizations.queueMessageRequeued;
    case QueueStatus.Canceled:
      return localizations.queueMessageCanceled;
    default:
      return "";
  }
}

// Data class for individual queue members
class QueueMember {
  final String id;
  String name;
  Duration serviceDuration;
  QueueStatus status;
  final int originalPosition; // 1-indexed original position in queue
  double serviceProgress; // 0.0 to 1.0

  QueueMember({
    required this.id,
    required this.name,
    required this.serviceDuration,
    this.status = QueueStatus.Upcoming,
    required this.originalPosition,
    this.serviceProgress = 0.0,
  });
}

// Placeholder data model - in a real app, this would come from your state/backend
class AppointmentInfo {
  String serviceName;
  String serviceIcon;
  String appointmentDate;
  String appointmentTime;
  int currentQueuePositionUserDisplay;
  int totalInQueueDisplay;
  Duration estimatedWaitTimeUser;
  String professionalName;
  final String professionalAvatar;
  final double professionalRating;
  String professionalStatus;
  String bookingId;

  AppointmentInfo({
    required this.serviceName,
    required this.serviceIcon,
    required this.appointmentDate,
    required this.appointmentTime,
    required this.currentQueuePositionUserDisplay,
    required this.totalInQueueDisplay,
    required this.estimatedWaitTimeUser,
    this.professionalName = 'Finding professional...',
    this.professionalAvatar = 'assets/images/placeholder_avatar.png',
    this.professionalRating = 0.0,
    this.professionalStatus = 'Not yet assigned',
    required this.bookingId,
  });
}

class QueueManagementScreen extends StatefulWidget {
  // In a real app, you'd pass initial queue data here
  const QueueManagementScreen({Key? key}) : super(key: key);

  @override
  State<QueueManagementScreen> createState() => _QueueManagementScreenState();
}

class _QueueManagementScreenState extends State<QueueManagementScreen>
    with SingleTickerProviderStateMixin {
  late AppointmentInfo currentAppointmentData;
  WebSocketChannel? _channel;
  StreamSubscription? _webSocketSubscription;
  bool _isWebSocketConnected = false;
  bool _socketIoHandshakeComplete = false;
  String? _appSessionId;
  int? _currentUserActualIdInQueue;
  bool _isInitialDependencies = true;

  List<QueueMember> _queueMembers = [];
  List<QueueMember> _displayedQueueMembers = [];
  String _currentUserId = "user_is_4";
  int? _currentlyServicedOriginalPosition;

  final Set<int> _pendingSwapRequests =
      {}; // For swaps initiated BY current user, maps to targetOriginalQueuePosition
  List<SwapRequestInfo> _initiatedSwapRequestsList =
      []; // List of swaps current user started
  List<SwapRequestInfo> _involvedSwapRequestsList =
      []; // List of swaps current user needs to respond to
  final QueueApiService _queueApiService = QueueApiService(); // Added instance

  final double _lineHeightPerMinute = FetchPixels.getPixelHeight(30);
  final DateFormat _displayDateFormat = DateFormat('MMMM dd, yyyy');
  final DateFormat _displayTimeFormat = DateFormat('hh:mm a');

  Timer? _liveWaitTimer;
  Duration _liveEstimatedWaitTime = Duration.zero;
  Timer? _timeUntilStartTimer; // New timer for counting down until start
  Duration _timeUntilStart = Duration.zero; // New duration for time until start
  bool _isFirstMemberInProgress = false;

  late AnimationController _pauseAnimationController;
  late Animation<double> _pauseOpacityAnimation;

  // WebSocket Connection Retry Logic
  int _connectionAttempt = 0;
  final int _maxConnectionAttempts = 5;
  Timer? _retryTimer;
  bool _isAttemptingConnection = false; // Prevents concurrent attempts

  @override
  void initState() {
    super.initState();
    // _initializeQueueState();
    _connectWebSocket();

    _pauseAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1200),
    );
    _pauseOpacityAnimation = Tween<double>(begin: 0.4, end: 1.0).animate(
      CurvedAnimation(
        parent: _pauseAnimationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (_isInitialDependencies) {
      final localizations = AppLocalizations.of(context)!;
      _initializeQueueState(localizations);
      _isInitialDependencies = false;
    }
  }

  QueueStatus _parseServerStatus(String? serverStatus) {
    switch (serverStatus?.toLowerCase()) {
      case 'inprogress':
        return QueueStatus.InProgress;
      case 'confirmed': // Assuming 'confirmed' means upcoming in the queue
        return QueueStatus.Upcoming;
      case 'completed':
        return QueueStatus.Completed;
      default:
        return QueueStatus.Upcoming; // Default for unknown or null statuses
    }
  }

  Future<void> _connectWebSocket() async {
    if (_isAttemptingConnection && _connectionAttempt > 0) {
      print("WebSocket: Connection attempt already in progress.");
      return;
    }
    print("WebSocket: Initializing connection sequence.");
    _connectionAttempt = 0; // Reset for a new sequence
    _retryTimer?.cancel(); // Cancel any pending retries
    _isWebSocketConnected =
        false; // Explicitly set to false at the start of new sequence
    _socketIoHandshakeComplete = false;
    _performConnectionAttempt();
  }

  Future<void> _performConnectionAttempt() async {
    if (_isAttemptingConnection && _connectionAttempt > 0) {
      // This check is mostly for direct calls, _connectWebSocket handles initial guard
      print("WebSocket: Recursive _performConnectionAttempt avoided.");
      return;
    }

    if (_connectionAttempt >= _maxConnectionAttempts) {
      print(
        "WebSocket Error: Maximum connection attempts ($_maxConnectionAttempts) reached. Stopping.",
      );
      setState(() {
        _isAttemptingConnection = false;
        // TODO: Update UI to show permanent failure, e.g., currentAppointmentData.serviceName = "Connection Failed";
      });
      return;
    }

    _isAttemptingConnection = true;
    _connectionAttempt++;

    // Clean up previous connection artifacts before new attempt
    _webSocketSubscription?.cancel();
    _channel?.sink.close();
    _isWebSocketConnected = false;
    _socketIoHandshakeComplete = false;

    print(
      "WebSocket: Attempting to connect (Attempt $_connectionAttempt/$_maxConnectionAttempts)...",
    );

    final prefs = await SharedPreferences.getInstance();
    _appSessionId = prefs.getString(prefsKeySessionId);

    if (_appSessionId == null || _appSessionId!.isEmpty) {
      print("WebSocket Error: Session ID is missing. Cannot connect.");
      // No retries if session ID is missing, this is a fatal config issue for this screen
      setState(() {
        _isAttemptingConnection = false;
        // TODO: Update UI or navigate away
      });
      return;
    }

    String baseUrl =
        'wss://dapi-test.adscloud.org:8443/socket.io/?EIO=4&transport=websocket';
    final wsUrl = Uri.parse('$baseUrl&sessionId=$_appSessionId');

    try {
      _channel = WebSocketChannel.connect(wsUrl);
      print("WebSocket: Channel initiated for $wsUrl");

      _webSocketSubscription = _channel!.stream
          .timeout(
            const Duration(
              seconds: 15,
            ), // Timeout for receiving the first message (e.g. Socket.IO '0')
            onTimeout: (sink) {
              print(
                "WebSocket Error: Connection attempt timed out after 15 seconds.",
              );
              // The sink for a timed-out stream is typically closed by the timeout mechanism.
              // We need to trigger our retry logic.
              // Directly calling _scheduleRetry might be complex due to async/await context of onTimeout.
              // Instead, we ensure the error handling path in listen()'s onError will catch this.
              // Forcing an error or relying on stream closure after timeout.
              // A simple way: the stream should close or error, which `listen` will pick up.
              // If it doesn't, this timeout acts as a safeguard before a more aggressive retry.
              if (!_socketIoHandshakeComplete) {
                // Manually trigger a close if timeout doesn't do it effectively for error handling
                _channel?.sink.close();
              }
            },
          )
          .listen(
            (message) {
              // Standard message processing (Open, Handshake, Ping, Data)
              // print("WebSocket Raw Message Received: $message");
              // print(message);
              if (message is String) {
                if (message.startsWith('0{')) {
                  print("Socket.IO server open packet received: $message");
                  // Only set _isWebSocketConnected true upon receiving the open packet
                  // as a sign that the underlying transport is working.
                  if (!_isWebSocketConnected) {
                    // Avoid redundant setState if already true
                    setState(() {
                      _isWebSocketConnected = true;
                    });
                  }

                  if (_appSessionId != null && _appSessionId!.isNotEmpty) {
                    final authPayload = jsonEncode({
                      "sessionId": _appSessionId,
                    });
                    final connectMessage = '40$authPayload';
                    print(
                      "Sending Socket.IO connect to namespace with auth: $connectMessage",
                    );
                    _channel!.sink.add(connectMessage);
                  } else {
                    print("Error: App session ID not available for 40-auth.");
                    _handleConnectionErrorDuringAttempt(); // Fatal, no session ID for handshake
                  }
                } else if (message.startsWith('40')) {
                  print(
                    "Socket.IO namespace connection confirmed by server: $message",
                  );
                  if (!_socketIoHandshakeComplete) {
                    setState(() {
                      _socketIoHandshakeComplete = true;
                      _connectionAttempt = 0; // Reset on successful handshake
                      _isAttemptingConnection = false; // Connection successful
                      print(
                        "WebSocket: Handshake complete. Connection successful.",
                      );
                    });
                    _requestQueueStatus();
                  }
                } else if (message == '2') {
                  print("Socket.IO Ping received. Sending Pong (3).");
                  _channel!.sink.add('3');
                } else if (message.startsWith('42[')) {
                  print("Socket.IO Data received 42");
                  // Ensure connection flags are set if data is received
                  if (!_isWebSocketConnected)
                    setState(() => _isWebSocketConnected = true);
                  if (!_socketIoHandshakeComplete)
                    setState(() => _socketIoHandshakeComplete = true);
                  if (_isAttemptingConnection) {
                    // If we were in a retry loop, we are now good.
                    _connectionAttempt = 0;
                    _isAttemptingConnection = false;
                    print(
                      "WebSocket: Data received, marking connection as successful after attempts.",
                    );
                  }

                  // print("Socket.IO Data Message: $message");
                  try {
                    String rawJsonPayload = message.substring(
                      message.indexOf('['),
                    );
                    List<dynamic> eventData = jsonDecode(rawJsonPayload);
                    if (eventData.isNotEmpty && eventData[0] == 'queueUpdate') {
                      if (eventData.length > 1 &&
                          eventData[1] is Map<String, dynamic>) {
                        Map<String, dynamic> payload = eventData[1];
                        print(payload["initiatedSwapRequests"]);
                        setState(() {
                          _currentUserActualIdInQueue =
                              payload['currentUserAppointmentId'] as int?;
                          currentAppointmentData.serviceName =
                              payload['serviceName'] as String? ??
                              currentAppointmentData.serviceName;
                          if (_currentUserActualIdInQueue != null) {
                            currentAppointmentData.bookingId =
                                _currentUserActualIdInQueue.toString();
                          }
                          try {
                            if (payload['appointmentDate'] != null) {
                              DateTime parsedDate = DateTime.parse(
                                payload['appointmentDate'] as String,
                              );
                              currentAppointmentData
                                  .appointmentDate = _displayDateFormat.format(
                                parsedDate.toLocal(),
                              );
                            }
                            if (payload['appointmentTime'] != null) {
                              DateTime parsedTime = DateTime.parse(
                                payload['appointmentTime'] as String,
                              );
                              currentAppointmentData
                                  .appointmentTime = _displayTimeFormat.format(
                                parsedTime.toLocal(),
                              );
                            }
                          } catch (e) {
                            print("Error parsing date/time from payload: $e");
                          }
                          currentAppointmentData
                                  .currentQueuePositionUserDisplay =
                              payload['currentUserPosition'] as int? ??
                              currentAppointmentData
                                  .currentQueuePositionUserDisplay;
                          currentAppointmentData.totalInQueueDisplay =
                              payload['totalActiveInQueue'] as int? ??
                              currentAppointmentData.totalInQueueDisplay;
                          String? currentUserOverallStatus =
                              payload['status'] as String?;
                          if (currentUserOverallStatus != null) {
                            if (currentUserOverallStatus.toLowerCase() ==
                                'inprogress') {
                              currentAppointmentData.professionalStatus =
                                  "Service In Progress";
                            } else if (currentUserOverallStatus.toLowerCase() ==
                                'confirmed') {
                              currentAppointmentData.professionalStatus =
                                  "Waiting for your turn";
                            } else {
                              currentAppointmentData.professionalStatus =
                                  currentUserOverallStatus;
                            }
                          } else {
                            currentAppointmentData.professionalStatus =
                                "Status Unknown";
                          }
                          if (payload['queueMembers'] is List) {
                            List<dynamic> membersPayload =
                                payload['queueMembers'];
                            _queueMembers.clear();
                            for (int i = 0; i < membersPayload.length; i++) {
                              var memberData =
                                  membersPayload[i] as Map<String, dynamic>;
                              int memberId = memberData['id'] as int;
                              QueueStatus status = _parseServerStatus(
                                memberData['status'] as String?,
                              );
                              double progress = 0.0;
                              if (status == QueueStatus.Completed)
                                progress = 1.0;
                              else if (status == QueueStatus.InProgress)
                                progress = 0.1;
                              _queueMembers.add(
                                QueueMember(
                                  id: memberId.toString(),
                                  name:
                                      (memberId == _currentUserActualIdInQueue)
                                          ? "You"
                                          : memberData['displayName']
                                                  as String? ??
                                              'Guest',
                                  serviceDuration: Duration(
                                    minutes:
                                        memberData['serviceDurationMinutes']
                                            as int? ??
                                        30,
                                  ),
                                  status: status,
                                  serviceProgress: progress,
                                  originalPosition: i + 1,
                                ),
                              );
                            }
                          }
                          if (_queueMembers.isNotEmpty) {
                            _isFirstMemberInProgress =
                                _queueMembers.first.status ==
                                QueueStatus.InProgress;
                          } else {
                            _isFirstMemberInProgress = false;
                          }
                          _updateDisplayedQueueMembers();
                          final timeUntilStartSeconds =
                              payload['timeUntilStartSeconds'] as int?;
                          if (timeUntilStartSeconds != null) {
                            _startOrUpdateTimeUntilStartTimer(
                              Duration(seconds: timeUntilStartSeconds),
                            );
                          }
                          final waitSeconds =
                              payload['currentUserEstimatedWaitSeconds']
                                  as int?;
                          Duration newWaitDuration = Duration.zero;
                          if (waitSeconds != null)
                            newWaitDuration = Duration(seconds: waitSeconds);
                          currentAppointmentData.estimatedWaitTimeUser =
                              newWaitDuration;
                          _startOrUpdateLiveWaitTimer(newWaitDuration);
                          print(
                            "UI updated. Full members: ${_queueMembers.length}, Displayed: ${_displayedQueueMembers.length}, LiveWait: ${_liveEstimatedWaitTime.inSeconds}s",
                          );

                          // --- Swap Request Parsing and Logging ---
                          print(
                            "Raw initiatedSwapRequests from payload: ${payload['initiatedSwapRequests']}",
                          );
                          List<SwapRequestInfo> oldInitiated = List.from(
                            _initiatedSwapRequestsList,
                          );
                          if (payload['initiatedSwapRequests'] is List) {
                            _initiatedSwapRequestsList =
                                (payload['initiatedSwapRequests'] as List)
                                    .map(
                                      (item) => SwapRequestInfo.fromJson(
                                        item as Map<String, dynamic>,
                                      ),
                                    )
                                    .toList();
                          } else {
                            _initiatedSwapRequestsList.clear();
                          }
                          print(
                            "Initiated Swaps. Old count: ${oldInitiated.length}, New count: ${_initiatedSwapRequestsList.length}",
                          );
                          if (_initiatedSwapRequestsList.isNotEmpty &&
                              (_initiatedSwapRequestsList.length !=
                                      oldInitiated.length ||
                                  _initiatedSwapRequestsList.first.id !=
                                      oldInitiated.firstOrNull?.id ||
                                  _initiatedSwapRequestsList.first.status !=
                                      oldInitiated.firstOrNull?.status)) {
                            // Log more selectively
                            print(
                              "First new initiated swap: ID ${_initiatedSwapRequestsList.first.id}, Status ${_initiatedSwapRequestsList.first.status}",
                            );
                          }

                          print(
                            "Raw involvedSwapRequests from payload: ${payload['involvedSwapRequests']}",
                          );
                          List<SwapRequestInfo> oldInvolved = List.from(
                            _involvedSwapRequestsList,
                          );
                          if (payload['involvedSwapRequests'] is List) {
                            _involvedSwapRequestsList =
                                (payload['involvedSwapRequests'] as List)
                                    .map(
                                      (item) => SwapRequestInfo.fromJson(
                                        item as Map<String, dynamic>,
                                      ),
                                    )
                                    .toList();
                          } else {
                            _involvedSwapRequestsList.clear();
                          }
                          print(
                            "Involved Swaps. Old count: ${oldInvolved.length}, New count: ${_involvedSwapRequestsList.length}",
                          );
                          if (_involvedSwapRequestsList.isNotEmpty &&
                              (_involvedSwapRequestsList.length !=
                                      oldInvolved.length ||
                                  _involvedSwapRequestsList.first.id !=
                                      oldInvolved.firstOrNull?.id ||
                                  _involvedSwapRequestsList.first.status !=
                                      oldInvolved.firstOrNull?.status)) {
                            // Log more selectively
                            print(
                              "First new involved swap: ID ${_involvedSwapRequestsList.first.id}, Status ${_involvedSwapRequestsList.first.status}, Requester: ${_involvedSwapRequestsList.first.requestedBy.fullName}",
                            );
                          }
                          print("--- setState finished for queueUpdate ---");
                        });
                      }
                    }
                  } catch (e) {
                    print(
                      "Error parsing Socket.IO event data: $e. Message: $message",
                    );
                  }
                } else if (message == '3') {
                  print("Socket.IO Pong (either sent by us or an echo).");
                } else {
                  print("Received other WebSocket message: $message");
                }
              }
            },
            onError: (error) {
              print("WebSocket Error in stream: $error");
              if (!_socketIoHandshakeComplete) {
                // Error during initial connection/handshake phase
                _handleConnectionErrorDuringAttempt();
              } else {
                // Error after connection was established
                print(
                  "WebSocket: Connection lost after successful handshake. Re-initializing sequence.",
                );
                _isAttemptingConnection =
                    false; // Allow _connectWebSocket to run fully
                _connectWebSocket(); // Re-initialize the whole connection process
              }
            },
            onDone: () {
              print("WebSocket: Stream closed (onDone).");
              if (!_socketIoHandshakeComplete && _isAttemptingConnection) {
                // Closed during an attempt, before handshake
                _handleConnectionErrorDuringAttempt();
              } else if (_socketIoHandshakeComplete) {
                // Connection lost after it was up
                print(
                  "WebSocket: Connection closed after successful handshake. Re-initializing sequence.",
                );
                _isAttemptingConnection =
                    false; // Allow _connectWebSocket to run fully
                _connectWebSocket(); // Re-initialize the whole connection process
              } else {
                // Stream closed, handshake not complete, but not actively attempting (e.g. manual close or already handled max retries)
                _isAttemptingConnection = false;
                setState(() {
                  _isWebSocketConnected = false;
                  _socketIoHandshakeComplete = false;
                  _displayedQueueMembers.clear();
                });
              }
            },
            cancelOnError: false, // We handle errors to decide on retries
          );
    } catch (e) {
      print(
        "WebSocket Error: Failed to establish WebSocketChannel.connect: $e",
      );
      _handleConnectionErrorDuringAttempt(); // Treat as a failed attempt
    }
  }

  void _handleConnectionErrorDuringAttempt() {
    print(
      "WebSocket: Handling connection error during attempt $_connectionAttempt.",
    );
    _webSocketSubscription?.cancel();
    _channel?.sink.close();
    _isWebSocketConnected = false;
    _socketIoHandshakeComplete = false;
    _isAttemptingConnection = false; // Allow next attempt or scheduling

    if (_connectionAttempt < _maxConnectionAttempts) {
      _scheduleRetry();
    } else {
      print(
        "WebSocket Error: Max connection attempts ($_maxConnectionAttempts) reached after failure. Stopping.",
      );
      setState(() {
        // TODO: Update UI for permanent failure
      });
    }
  }

  void _scheduleRetry() {
    _retryTimer?.cancel(); // Cancel any existing timer just in case
    final retryDelay = Duration(
      seconds: 5 + (_connectionAttempt * 2),
    ); // Exponential backoff, e.g. 5, 7, 9...
    print(
      "WebSocket: Scheduling connection retry attempt ${_connectionAttempt + 1} in ${retryDelay.inSeconds} seconds...",
    );
    _retryTimer = Timer(retryDelay, () {
      _performConnectionAttempt();
    });
  }

  void _requestQueueStatus() {
    if (_socketIoHandshakeComplete && _channel?.sink != null) {
      final message = '42["requestQueueStatus"]';
      print('Sending event to WebSocket: $message');
      _channel!.sink.add(message);
    } else {
      print(
        "Cannot send 'requestQueueStatus': Handshake not complete or sink unavailable.",
      );
    }
  }

  void _initializeQueueState(AppLocalizations localizations) {
    // Keep this minimal or as loading state, real data comes from WebSocket
    currentAppointmentData = AppointmentInfo(
      serviceName: localizations.queueLoadingService,
      serviceIcon: 'ac_unit', // Default icon
      appointmentDate: localizations.queueLoadingDate,
      appointmentTime: localizations.queueLoadingTime,
      currentQueuePositionUserDisplay: 0,
      totalInQueueDisplay: 0,
      estimatedWaitTimeUser: Duration.zero,
      professionalName: localizations.queueFindingProfessional,
      bookingId: 'N/A',
    );
    _queueMembers = []; // Initialize as empty, will be populated by WebSocket
    _displayedQueueMembers = []; // Initialize displayed list as empty
    _liveEstimatedWaitTime =
        currentAppointmentData
            .estimatedWaitTimeUser; // Initialize live timer with this
    // _currentUserId should now be managed by _currentUserActualIdInQueue from payload
  }

  void _startOrUpdateLiveWaitTimer(Duration newServerWaitTime) {
    _liveWaitTimer?.cancel();

    setState(() {
      _liveEstimatedWaitTime = newServerWaitTime;
    });

    if (_liveEstimatedWaitTime.inSeconds > 0 && _isFirstMemberInProgress) {
      _liveWaitTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
        if (!mounted) {
          timer.cancel();
          return;
        }

        if (!_isFirstMemberInProgress) {
          timer.cancel();
          return;
        }

        setState(() {
          if (_liveEstimatedWaitTime.inSeconds > 0) {
            _liveEstimatedWaitTime =
                _liveEstimatedWaitTime - const Duration(seconds: 1);
          } else {
            _liveEstimatedWaitTime = Duration.zero;
            timer.cancel();
          }
        });
      });
    }
  }

  // New method to handle the time until start timer
  void _startOrUpdateTimeUntilStartTimer(Duration timeUntilStart) {
    _timeUntilStartTimer?.cancel();

    setState(() {
      _timeUntilStart = timeUntilStart;
    });

    if (_timeUntilStart.inSeconds > 0 && !_isFirstMemberInProgress) {
      _timeUntilStartTimer = Timer.periodic(const Duration(seconds: 1), (
        timer,
      ) {
        if (!mounted) {
          timer.cancel();
          return;
        }

        if (_isFirstMemberInProgress) {
          timer.cancel();
          return;
        }

        setState(() {
          if (_timeUntilStart.inSeconds > 0) {
            _timeUntilStart = _timeUntilStart - const Duration(seconds: 1);
          } else {
            _timeUntilStart = Duration.zero;
            timer.cancel();
          }
        });
      });
    }
  }

  void _updateDisplayedQueueMembers() {
    // Extracted filtering logic into a method
    _displayedQueueMembers.clear();
    if (_queueMembers.isNotEmpty && _currentUserActualIdInQueue != null) {
      int currentUserIndexInFullList = _queueMembers.indexWhere(
        (m) => m.id == _currentUserActualIdInQueue.toString(),
      );
      if (currentUserIndexInFullList != -1) {
        if (_queueMembers.length <= 5) {
          _displayedQueueMembers.addAll(_queueMembers);
        } else {
          int desiredBefore = 2;
          int desiredAfter = 2;
          int maxDisplay = 5;
          int startIndex = currentUserIndexInFullList - desiredBefore;
          int endIndex = currentUserIndexInFullList + desiredAfter;
          if (startIndex < 0) {
            endIndex += (0 - startIndex);
            startIndex = 0;
          }
          if (endIndex >= _queueMembers.length) {
            startIndex -= (endIndex - (_queueMembers.length - 1));
            endIndex = _queueMembers.length - 1;
          }
          startIndex = max(0, startIndex);
          endIndex = min(_queueMembers.length - 1, endIndex);
          if (endIndex - startIndex + 1 > maxDisplay) {
            if (currentUserIndexInFullList - startIndex >= desiredBefore) {
              endIndex = startIndex + maxDisplay - 1;
            } else {
              startIndex = endIndex - maxDisplay + 1;
            }
          }
          if (currentUserIndexInFullList < startIndex && startIndex > 0) {
            startIndex = currentUserIndexInFullList;
            endIndex = min(
              _queueMembers.length - 1,
              startIndex + maxDisplay - 1,
            );
          }
          if (currentUserIndexInFullList > endIndex &&
              endIndex < _queueMembers.length - 1) {
            endIndex = currentUserIndexInFullList;
            startIndex = max(0, endIndex - maxDisplay + 1);
          }
          _displayedQueueMembers.addAll(
            _queueMembers.sublist(startIndex, endIndex + 1),
          );
        }
      } else {
        _displayedQueueMembers.addAll(_queueMembers.take(5));
      }
    } else if (_queueMembers.isNotEmpty) {
      _displayedQueueMembers.addAll(_queueMembers.take(5));
    }
  }

  @override
  void dispose() {
    _liveWaitTimer?.cancel();
    _timeUntilStartTimer?.cancel(); // Cancel the new timer
    _webSocketSubscription?.cancel();
    _channel?.sink.close();
    _pauseAnimationController.dispose();
    _retryTimer?.cancel();
    super.dispose();
  }

  bool get isProfessionalAssigned =>
      currentAppointmentData.professionalName != 'Finding professional...' &&
      currentAppointmentData.professionalName !=
          'Loading professional...'; // Adjusted for initial state

  String _formatDuration(Duration duration, bool isTimerEffectivelyPaused) {
    if (isTimerEffectivelyPaused) {
      return 'Paused';
    }
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    String twoDigitMinutes = twoDigits(duration.inMinutes.remainder(60));
    String twoDigitSeconds = twoDigits(duration.inSeconds.remainder(60));
    if (duration.inHours > 0) {
      return "${twoDigits(duration.inHours)}:$twoDigitMinutes:$twoDigitSeconds";
    }
    return "$twoDigitMinutes:$twoDigitSeconds";
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    FetchPixels(context);
    // If _queueMembers is empty and not connected yet, could show a loading indicator
    // For now, it relies on _initializeQueueState's defaults until first WebSocket message.
    return WillPopScope(
      onWillPop: () async {
        Constant.backToPrev(context);
        return false;
      },
      child: Scaffold(
        backgroundColor: backGroundColor,
        appBar: AppBar(
          elevation: 0,
          toolbarHeight: FetchPixels.getPixelHeight(70),
          backgroundColor: backGroundColor,
          leading: getPaddingWidget(
            EdgeInsets.only(left: FetchPixels.getPixelWidth(20)),
            // EdgeInsets.symmetric(horizontal: FetchPixels.getPixelWidth(20)),
            gettoolbarMenu(context, "back.svg", () {
              Constant.backToPrev(context);
            }),
          ),
          title: getCustomFont(
            localizations.queueLiveStatusTitle,
            22,
            Colors.black,
            1,
            fontWeight: FontWeight.w700,
          ),
          centerTitle: true,
        ),
        body: SingleChildScrollView(
          padding: EdgeInsets.all(FetchPixels.getPixelHeight(20)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              _buildServiceTimeCard(currentAppointmentData),
              getVerSpace(FetchPixels.getPixelHeight(25)),
              _buildVerticalQueueProgressCard(), // This will use the new _displayedQueueMembers
              getVerSpace(FetchPixels.getPixelHeight(25)),
              _buildSwapRequestsSection(), // Moved here
              // getVerSpace(FetchPixels.getPixelHeight(25)),
              // LiveQueueStatusCard(
              //   status: _getCurrentUserQueueStatus(),
              //   position: _getCurrentUserQueuePosition(),
              //   canSwap: _canCurrentUserSwap(),
              //   onSwap: _handleCurrentUserSwap,
              //   onLeave: _handleCurrentUserLeave,
              //   onCheckIn: _handleCurrentUserCheckIn,
              // ),
              getVerSpace(FetchPixels.getPixelHeight(25)),
              _buildBookingInfoCard(currentAppointmentData),
              getVerSpace(FetchPixels.getPixelHeight(30)),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildServiceTimeCard(AppointmentInfo appointment) {
    return Container(
      padding: EdgeInsets.all(FetchPixels.getPixelHeight(18)),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withOpacity(0.15),
            spreadRadius: 2,
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Row(
        children: [
          Icon(
            Icons.ac_unit,
            size: FetchPixels.getPixelHeight(40),
            color: blueColor,
          ),
          getHorSpace(FetchPixels.getPixelWidth(15)),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                getCustomFont(
                  appointment.serviceName,
                  18,
                  Colors.black,
                  1,
                  fontWeight: FontWeight.w600,
                ),
                getVerSpace(FetchPixels.getPixelHeight(5)),
                getCustomFont(
                  '${appointment.appointmentDate} at ${appointment.appointmentTime}',
                  14,
                  textColor,
                  2,
                  fontWeight: FontWeight.w400,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String displayPosition(int position) {
    // if (position == 1) {
    //   return '1st';
    // } else if (position == 2) {
    //   return '2nd';
    // } else if (position == 3) {
    //   return '3rd';
    // } else {
    //   return '${position}th';
    // }

    return position.toString();
  }

  Widget _buildVerticalQueueProgressCard() {
    final localizations = AppLocalizations.of(context)!;
    if (_displayedQueueMembers.isEmpty && !_isWebSocketConnected) {
      return const Center(child: CircularProgressIndicator());
    }
    if (_displayedQueueMembers.isEmpty && _isWebSocketConnected) {
      return Center(child: Text(localizations.queueEmptyOrNotListed));
    }

    Color timerColor;
    Duration displayWaitTime = _liveEstimatedWaitTime;
    bool showTimeUntilStart =
        !_isFirstMemberInProgress && _timeUntilStart.inSeconds > 0;

    if (showTimeUntilStart) {
      timerColor = Colors.teal; // Different color for time until start
      displayWaitTime = _timeUntilStart;
    } else if (displayWaitTime.inSeconds > 0 && !_isFirstMemberInProgress) {
      timerColor = Colors.redAccent.shade400;
    } else {
      timerColor = blueColor;
      try {
        if (_currentUserActualIdInQueue != null) {
          final currentUserMember = _displayedQueueMembers.firstWhere(
            (m) => m.id == _currentUserActualIdInQueue.toString(),
          );
          if (currentUserMember.status == QueueStatus.InProgress) {
            timerColor = Colors.orange.shade700;
          } else if (currentUserMember.status == QueueStatus.Completed ||
              displayWaitTime.inSeconds <= 0) {
            timerColor = success;
          }
        }
      } catch (e) {
        print("User not found for specific timer color (non-paused): $e");
      }
      if (displayWaitTime.inSeconds <= 0 && timerColor != success) {
        timerColor = success;
      }
    }

    if (displayWaitTime.inSeconds <= 0) {
      displayWaitTime = Duration.zero;
      if (_isFirstMemberInProgress || _liveEstimatedWaitTime.inSeconds <= 0) {
        _liveEstimatedWaitTime = Duration.zero;
      }
    }

    bool isTimerEffectivelyPaused =
        !showTimeUntilStart &&
        displayWaitTime.inSeconds > 0 &&
        !_isFirstMemberInProgress;

    if (isTimerEffectivelyPaused) {
      if (!_pauseAnimationController.isAnimating) {
        _pauseAnimationController.repeat(reverse: true);
      }
    } else {
      if (_pauseAnimationController.isAnimating) {
        _pauseAnimationController.stop();
        _pauseAnimationController.reset();
      }
    }

    // Handle empty/loading states before main container
    // Ensure we don't show empty if the only thing to show is the paused notice.
    if (_displayedQueueMembers.isEmpty && !isTimerEffectivelyPaused) {
      if (!_isWebSocketConnected) {
        return const Center(child: CircularProgressIndicator());
      }
      return const Center(
        child: Text("Queue is currently empty or your spot is not listed."),
      );
    }

    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: FetchPixels.getPixelWidth(20),
        vertical: FetchPixels.getPixelHeight(20),
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.08),
            spreadRadius: 2,
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child:
                    currentAppointmentData.currentQueuePositionUserDisplay == 2
                        ? getCustomFont(
                          localizations.queueYouAreNext,
                          16,
                          Colors.orange,
                          1,
                          fontWeight: FontWeight.bold,
                        )
                        : currentAppointmentData
                                .currentQueuePositionUserDisplay ==
                            1
                        ? getCustomFont(
                          localizations.queueYourTurn,
                          16,
                          Colors.green,
                          1,
                          fontWeight: FontWeight.bold,
                        )
                        : getCustomFont(
                          localizations.queueYourPosition(
                            displayPosition(
                              currentAppointmentData
                                  .currentQueuePositionUserDisplay,
                            ),
                          ),
                          16,
                          Colors.black,
                          1,
                          fontWeight: FontWeight.bold,
                        ),
              ),
              Row(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Icon(
                    Icons.timer_outlined,
                    color: timerColor,
                    size: FetchPixels.getPixelHeight(24),
                  ),
                  getHorSpace(FetchPixels.getPixelWidth(6)),
                  getCustomFont(
                    _formatDuration(displayWaitTime, isTimerEffectivelyPaused),
                    18,
                    timerColor,
                    1,
                    fontWeight: FontWeight.bold,
                  ),
                ],
              ),
            ],
          ),

          // Conditional space *only if notice is NOT shown* by ListView
          if (!isTimerEffectivelyPaused)
            getVerSpace(FetchPixels.getPixelHeight(25)),

          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount:
                _displayedQueueMembers.length +
                (isTimerEffectivelyPaused ? 1 : 0),
            itemBuilder: (context, index) {
              if (isTimerEffectivelyPaused && index == 0) {
                // Build the animated notice as the first list item
                return _buildAlertItem(
                  message: localizations.queueTimerPausedMessage,
                );
              }

              // Determine actual member index
              final memberIndex = isTimerEffectivelyPaused ? index - 1 : index;

              // Guard against index out of bounds (should be rare with correct itemCount)
              if (memberIndex < 0 ||
                  memberIndex >= _displayedQueueMembers.length) {
                return const SizedBox.shrink();
              }

              final member = _displayedQueueMembers[memberIndex];

              bool isCurrentUser =
                  member.id == _currentUserActualIdInQueue?.toString();
              bool isStartingSoon =
                  member.originalPosition == 1 &&
                  member.status == QueueStatus.Upcoming;

              String label;
              // Get the display position from currentAppointmentData for the current user
              // and from member.originalPosition for others.
              String displayCurrentUserPosition =
                  currentAppointmentData.currentQueuePositionUserDisplay
                      .toString();
              String displayMemberPosition = member.originalPosition.toString();

              if (isCurrentUser) {
                if (member.status == QueueStatus.InProgress) {
                  label = localizations.queueMemberLabelYouInProgress(
                    displayCurrentUserPosition,
                  );
                } else if (member.status == QueueStatus.Completed) {
                  label = localizations.queueMemberLabelYouCompleted(
                    displayCurrentUserPosition,
                  );
                } else if (isStartingSoon) {
                  label = localizations.queueMemberLabelYouStartingSoon(
                    displayCurrentUserPosition,
                  );
                } else {
                  label = localizations.queueMemberLabelYouPosition(
                    displayCurrentUserPosition,
                  );
                }
              } else {
                if (isStartingSoon) {
                  label = localizations.queueMemberLabelOtherStartingSoon(
                    member.name,
                    displayMemberPosition,
                  );
                } else {
                  label = localizations.queueMemberLabelOtherPosition(
                    member.name,
                    displayMemberPosition,
                  );
                  if (member.status == QueueStatus.InProgress) {
                    label += localizations.queueMemberLabelSuffixInProgress;
                  }
                  if (member.status == QueueStatus.Completed) {
                    label += localizations.queueMemberLabelSuffixCompleted;
                  }
                }
              }

              bool isEligibleForSwap = false;
              if (_currentUserActualIdInQueue != null &&
                  !isCurrentUser &&
                  member.status == QueueStatus.Upcoming) {
                int currentUserListIndexInDisplayed = _displayedQueueMembers
                    .indexWhere(
                      (m) => m.id == _currentUserActualIdInQueue.toString(),
                    );
                if (currentUserListIndexInDisplayed != -1 &&
                    memberIndex > currentUserListIndexInDisplayed &&
                    (memberIndex - currentUserListIndexInDisplayed) <= 3) {
                  isEligibleForSwap = true;
                }
              }

              // Determine if a swap is pending with this member
              bool isApiCallPendingForThisMember = _pendingSwapRequests
                  .contains(member.originalPosition);
              bool
              isServerSideSwapPendingWithThisMember = _initiatedSwapRequestsList.any((
                sr,
              ) {
                // sr.appointment1 is the initiator (current user for initiated list)
                // sr.appointment2 is the target member
                return sr.appointment2.id.toString() == member.id &&
                    (sr.status == 'pending_customer2_approval' ||
                        sr.status == 'pending_approval' ||
                        sr.status ==
                            'pending'); // Add any other relevant pending statuses from your backend
              });

              bool finalIsSwapPending =
                  isApiCallPendingForThisMember ||
                  isServerSideSwapPendingWithThisMember;

              return _buildMilestoneItem(
                member: member,
                label: label,
                isCurrentUser: isCurrentUser,
                isLastItem: memberIndex == _displayedQueueMembers.length - 1,
                isEligibleForSwap: isEligibleForSwap,
                isSwapPending: finalIsSwapPending, // Updated logic
                onSwapRequested:
                    () => _handleSwapRequest(member.originalPosition),
                isStartingSoon: isStartingSoon,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAlertItem({required String message}) {
    IconData iconData = Icons.info_outline_rounded;
    Color iconColor = Colors.red.shade700;
    Color iconBackgroundColor = Colors.red.shade100;
    FontWeight labelWeight = FontWeight.w600;
    Color labelColor = Colors.red.shade600;
    double avatarRadius = FetchPixels.getPixelHeight(16);
    double iconSize = FetchPixels.getPixelHeight(16);
    final double fixedLineHeight = FetchPixels.getPixelHeight(40.0);
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        getVerSpace(FetchPixels.getPixelHeight(15)),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CircleAvatar(
              radius: avatarRadius,
              backgroundColor: iconBackgroundColor,
              child: Icon(iconData, color: iconColor, size: iconSize),
            ),
            getHorSpace(FetchPixels.getPixelWidth(12)),
            Expanded(
              child: getCustomFont(
                message,
                15,
                labelColor,
                1,
                fontWeight: labelWeight,
              ),
            ),
          ],
        ),
        Padding(
          padding: EdgeInsets.only(
            left: avatarRadius - 1.5,
            top: FetchPixels.getPixelHeight(4),
            bottom: FetchPixels.getPixelHeight(4),
          ),
          child: SizedBox(
            height: fixedLineHeight, // Use the fixed line height here
            width: 3.0,
            child: Stack(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: Colors.grey.shade200,
                    borderRadius: BorderRadius.circular(1.5),
                  ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMilestoneItem({
    required QueueMember member,
    required String label,
    required bool isCurrentUser,
    required bool isLastItem,
    required bool isEligibleForSwap,
    required bool isSwapPending,
    required VoidCallback onSwapRequested,
    required bool isStartingSoon, // Added new parameter
  }) {
    IconData iconData;
    Color iconColor;
    Color iconBackgroundColor;
    FontWeight labelWeight = FontWeight.w400;
    Color labelColor = Colors.grey.shade600;
    double avatarRadius = FetchPixels.getPixelHeight(13);
    double iconSize = FetchPixels.getPixelHeight(16);

    if (isStartingSoon) {
      iconData = Icons.hourglass_top_rounded; // Icon for "Starting Soon"
      iconColor = Colors.white; // Icon color
      iconBackgroundColor = Colors.teal.shade400; // Background for the icon
      labelWeight = FontWeight.bold;
      labelColor = Colors.teal.shade700; // Label color
      avatarRadius = FetchPixels.getPixelHeight(15); // Slightly larger avatar
      iconSize = FetchPixels.getPixelHeight(18);
    } else if (member.status == QueueStatus.Completed) {
      iconData = Icons.check_circle_outline_rounded;
      iconColor = Colors.grey.shade500;
      iconBackgroundColor = Colors.grey.shade200;
      labelColor = Colors.grey.shade600;
    } else if (member.status == QueueStatus.InProgress) {
      iconData =
          isCurrentUser
              ? Icons.directions_run_rounded
              : Icons.hourglass_bottom_rounded;
      iconColor = Colors.white;
      iconBackgroundColor = isCurrentUser ? blueColor : Colors.orange.shade600;
      labelWeight = FontWeight.bold;
      labelColor = isCurrentUser ? blueColor : Colors.orange.shade700;
      avatarRadius = FetchPixels.getPixelHeight(15);
      iconSize = FetchPixels.getPixelHeight(18);
    } else {
      // Upcoming
      iconData =
          isCurrentUser ? Icons.person_search_rounded : Icons.circle_outlined;
      iconColor = isCurrentUser ? blueColor : Colors.grey.shade400;
      iconBackgroundColor =
          isCurrentUser ? blueColor.withOpacity(0.15) : Colors.grey.shade300;
      labelColor = isCurrentUser ? blueColor : textColor.withOpacity(0.9);
      if (isEligibleForSwap && !isCurrentUser) {
        iconColor = blueColor.withOpacity(0.7);
        iconBackgroundColor = blueColor.withOpacity(0.1);
      }
    }
    if (isCurrentUser) {
      labelWeight = FontWeight.bold;
      avatarRadius = FetchPixels.getPixelHeight(15);
      iconSize = FetchPixels.getPixelHeight(18);
      if (member.status != QueueStatus.InProgress) {
        // Keep blue if "You" and upcoming
        iconBackgroundColor = blueColor;
        iconColor = Colors.white;
      }
    }

    Widget swapButton = const SizedBox.shrink();
    if (isEligibleForSwap) {
      swapButton = IconButton(
        iconSize: FetchPixels.getPixelHeight(22),
        padding: EdgeInsets.zero,
        constraints: const BoxConstraints(),
        icon: AnimatedSwitcher(
          duration: const Duration(milliseconds: 300),
          transitionBuilder: (Widget child, Animation<double> animation) {
            return ScaleTransition(child: child, scale: animation);
          },
          child: Icon(
            isSwapPending
                ? Icons.hourglass_empty_rounded
                : Icons.swap_horiz_rounded,
            key: ValueKey<bool>(isSwapPending),
            color: isSwapPending ? Colors.orange.shade700 : blueColor,
          ),
        ),
        onPressed: isSwapPending ? null : onSwapRequested,
      );
    }

    // Use a fixed height for the line connecting milestones
    final double fixedLineHeight = FetchPixels.getPixelHeight(
      40.0,
    ); // Fixed height

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CircleAvatar(
              radius: avatarRadius,
              backgroundColor: iconBackgroundColor,
              child: Icon(iconData, color: iconColor, size: iconSize),
            ),
            getHorSpace(FetchPixels.getPixelWidth(12)),
            Expanded(
              child: getCustomFont(
                label,
                15,
                labelColor,
                1,
                fontWeight: labelWeight,
              ),
            ),
            swapButton,
          ],
        ),
        if (!isLastItem)
          Padding(
            padding: EdgeInsets.only(
              left: avatarRadius - 1.5,
              top: FetchPixels.getPixelHeight(4),
              bottom: FetchPixels.getPixelHeight(4),
            ),
            child: SizedBox(
              height: fixedLineHeight, // Use the fixed line height here
              width: 3.0,
              child: Stack(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(1.5),
                    ),
                  ),
                  if (member.serviceProgress > 0)
                    FractionallySizedBox(
                      heightFactor: member.serviceProgress,
                      alignment: Alignment.topCenter,
                      child: Container(
                        decoration: BoxDecoration(
                          color:
                              (member.status == QueueStatus.Completed)
                                  ? success
                                  : blueColor,
                          borderRadius: BorderRadius.circular(1.5),
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildBookingInfoCard(AppointmentInfo appointment) {
    final localizations = AppLocalizations.of(context)!;
    return InkWell(
      onTap: () {
        // Navigation to Live Queue page
        _showQrCheckInDialog(context, appointment);
      },
      borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(15)),
      child: Container(
        height: FetchPixels.getPixelHeight(60),
        padding: EdgeInsets.symmetric(
          horizontal: FetchPixels.getPixelWidth(20),
        ),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [daltiPrimary, daltiPrimary.withOpacity(0.5)],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(15)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.qr_code_2_outlined,
              color: Colors.white,
              size: FetchPixels.getPixelHeight(24),
            ),
            getHorSpace(FetchPixels.getPixelWidth(10)),
            getCustomFont(
              localizations.queueViewQRCode,
              18,
              Colors.white,
              1,
              fontWeight: FontWeight.bold,
            ),
          ],
        ),
      ),
    );
  }

  void _showQrCheckInDialog(BuildContext context, AppointmentInfo appointment) {
    final localizations = AppLocalizations.of(context)!;
    showDialog(
      context: context,
      useSafeArea: true,
      barrierDismissible: true,
      builder: (BuildContext dialogContext) {
        return Dialog.fullscreen(
          child: Scaffold(
            appBar: AppBar(
              backgroundColor: Colors.white,
              elevation: 0,
              leading: IconButton(
                icon: Icon(Icons.close, color: Colors.black),
                onPressed: () => Navigator.of(dialogContext).pop(),
              ),
              title: Row(
                children: [
                  Icon(
                    Icons.qr_code_scanner_rounded,
                    color: daltiPrimary,
                    size: FetchPixels.getPixelHeight(28),
                  ),
                  getHorSpace(FetchPixels.getPixelWidth(10)),
                  getCustomFont(
                    localizations.queueCheckInDetails,
                    20,
                    Colors.black,
                    1,
                    fontWeight: FontWeight.bold,
                  ),
                ],
              ),
            ),
            body: Container(
              width: double.infinity,
              padding: EdgeInsets.symmetric(
                horizontal: FetchPixels.getPixelWidth(20),
                vertical: FetchPixels.getPixelHeight(20),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  QrImageView(
                    data: "${appointment.bookingId}-${appointment.serviceName}",
                    version: QrVersions.auto,
                    size: 300.0,
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(40)),
                  getCustomFont(
                    "Booking ID: ${appointment.bookingId}",
                    18,
                    textColor,
                    1,
                    fontWeight: FontWeight.w500,
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(50)),
                  getButton(
                    dialogContext,
                    daltiPrimary,
                    localizations.queueDone,
                    Colors.white,
                    () {
                      Navigator.of(dialogContext).pop();
                    },
                    16,
                    weight: FontWeight.w600,
                    buttonHeight: FetchPixels.getPixelHeight(50),
                    buttonWidth: FetchPixels.getPixelWidth(160),
                    borderRadius: BorderRadius.circular(
                      FetchPixels.getPixelHeight(10),
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: FetchPixels.getPixelHeight(4)),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          getCustomFont(label, 14, textColor, 1, fontWeight: FontWeight.w400),
          getCustomFont(
            value,
            14,
            Colors.black87,
            1,
            fontWeight: FontWeight.w500,
          ),
        ],
      ),
    );
  }

  void _handleSwapRequest(int targetOriginalQueuePosition) async {
    if (_pendingSwapRequests.contains(targetOriginalQueuePosition) ||
        _currentUserActualIdInQueue == null) {
      return; // Already pending or user ID not available
    }

    final QueueMember? currentUserMember = _queueMembers.firstWhereOrNull(
      (m) => m.id == _currentUserActualIdInQueue.toString(),
    );

    final QueueMember? targetUserMember = _queueMembers.firstWhereOrNull(
      (m) => m.originalPosition == targetOriginalQueuePosition,
    );

    if (currentUserMember == null || targetUserMember == null) {
      print("Error: Could not find current or target user for swap.");
      return;
    }

    // Get the actual appointment times from the queue members
    DateTime? currentUserAppointmentTimeUTC;
    DateTime? targetUserAppointmentTimeUTC;

    try {
      // Find the appointment times from the swap requests list
      // First try to find in initiated requests
      final initiatedRequest = _initiatedSwapRequestsList.firstWhereOrNull(
        (req) =>
            req.appointment1.id.toString() == currentUserMember.id ||
            req.appointment2.id.toString() == currentUserMember.id,
      );

      if (initiatedRequest != null) {
        // If found in initiated requests
        if (initiatedRequest.appointment1.id.toString() ==
            currentUserMember.id) {
          currentUserAppointmentTimeUTC =
              initiatedRequest.appointment1.expectedAppointmentStartTime;
          // Try to find target user's time if they're part of this request
          if (initiatedRequest.appointment2.id.toString() ==
              targetUserMember.id) {
            targetUserAppointmentTimeUTC =
                initiatedRequest.appointment2.expectedAppointmentStartTime;
          }
        } else {
          currentUserAppointmentTimeUTC =
              initiatedRequest.appointment2.expectedAppointmentStartTime;
          // Try to find target user's time if they're part of this request
          if (initiatedRequest.appointment1.id.toString() ==
              targetUserMember.id) {
            targetUserAppointmentTimeUTC =
                initiatedRequest.appointment1.expectedAppointmentStartTime;
          }
        }
      }

      // If not found in initiated, try involved requests
      if (currentUserAppointmentTimeUTC == null) {
        final involvedRequest = _involvedSwapRequestsList.firstWhereOrNull(
          (req) =>
              req.appointment1.id.toString() == currentUserMember.id ||
              req.appointment2.id.toString() == currentUserMember.id,
        );

        if (involvedRequest != null) {
          if (involvedRequest.appointment1.id.toString() ==
              currentUserMember.id) {
            currentUserAppointmentTimeUTC =
                involvedRequest.appointment1.expectedAppointmentStartTime;
            if (involvedRequest.appointment2.id.toString() ==
                targetUserMember.id) {
              targetUserAppointmentTimeUTC =
                  involvedRequest.appointment2.expectedAppointmentStartTime;
            }
          } else {
            currentUserAppointmentTimeUTC =
                involvedRequest.appointment2.expectedAppointmentStartTime;
            if (involvedRequest.appointment1.id.toString() ==
                targetUserMember.id) {
              targetUserAppointmentTimeUTC =
                  involvedRequest.appointment1.expectedAppointmentStartTime;
            }
          }
        }
      }

      // If target time not found in the same request, search separately
      if (targetUserAppointmentTimeUTC == null) {
        final targetInitiatedRequest = _initiatedSwapRequestsList
            .firstWhereOrNull(
              (req) =>
                  req.appointment1.id.toString() == targetUserMember.id ||
                  req.appointment2.id.toString() == targetUserMember.id,
            );

        if (targetInitiatedRequest != null) {
          if (targetInitiatedRequest.appointment1.id.toString() ==
              targetUserMember.id) {
            targetUserAppointmentTimeUTC =
                targetInitiatedRequest
                    .appointment1
                    .expectedAppointmentStartTime;
          } else {
            targetUserAppointmentTimeUTC =
                targetInitiatedRequest
                    .appointment2
                    .expectedAppointmentStartTime;
          }
        } else {
          // Try involved requests for target user
          final targetInvolvedRequest = _involvedSwapRequestsList
              .firstWhereOrNull(
                (req) =>
                    req.appointment1.id.toString() == targetUserMember.id ||
                    req.appointment2.id.toString() == targetUserMember.id,
              );

          if (targetInvolvedRequest != null) {
            if (targetInvolvedRequest.appointment1.id.toString() ==
                targetUserMember.id) {
              targetUserAppointmentTimeUTC =
                  targetInvolvedRequest
                      .appointment1
                      .expectedAppointmentStartTime;
            } else {
              targetUserAppointmentTimeUTC =
                  targetInvolvedRequest
                      .appointment2
                      .expectedAppointmentStartTime;
            }
          }
        }
      }

      // If we still don't have the times, use the appointment time from currentAppointmentData
      if (currentUserAppointmentTimeUTC == null ||
          targetUserAppointmentTimeUTC == null) {
        // Parse the appointment date and time from currentAppointmentData
        final baseDate = DateFormat(
          'MMMM dd, yyyy',
        ).parse(currentAppointmentData.appointmentDate);
        final baseTime = DateFormat(
          'hh:mm a',
        ).parse(currentAppointmentData.appointmentTime);

        // Combine date and time
        final baseDateTime = DateTime(
          baseDate.year,
          baseDate.month,
          baseDate.day,
          baseTime.hour,
          baseTime.minute,
        );

        // Calculate times based on positions and average service duration
        final averageServiceDuration = Duration(
          minutes: 15,
        ); // You might want to adjust this
        if (currentUserAppointmentTimeUTC == null) {
          currentUserAppointmentTimeUTC = baseDateTime.add(
            averageServiceDuration * (currentUserMember.originalPosition - 1),
          );
        }
        if (targetUserAppointmentTimeUTC == null) {
          targetUserAppointmentTimeUTC = baseDateTime.add(
            averageServiceDuration * (targetUserMember.originalPosition - 1),
          );
        }
      }

      // Convert UTC times to local time
      final currentUserAppointmentTimeLocal =
          currentUserAppointmentTimeUTC!.toLocal();
      final targetUserAppointmentTimeLocal =
          targetUserAppointmentTimeUTC!.toLocal();

      // Show confirmation dialog
      showDialog(
        context: context,
        builder: (BuildContext context) {
          return SwapConfirmationDialog(
            currentPosition: currentUserMember.originalPosition.toString(),
            targetPosition: targetUserMember.originalPosition.toString(),
            currentAppointmentTime: currentUserAppointmentTimeLocal,
            targetAppointmentTime: targetUserAppointmentTimeLocal,
            targetUserName: targetUserMember.name,
            onCancel: () {
              Navigator.of(context).pop();
            },
            onConfirm: () async {
              Navigator.of(context).pop();
              // Proceed with the swap request
              setState(() {
                _pendingSwapRequests.add(targetOriginalQueuePosition);
              });

              try {
                print(
                  "Requesting queue swap between appointment ${currentUserMember.id} and ${targetUserMember.id}",
                );
                final response = await _queueApiService.requestQueueSwap(
                  appointment1Id: currentUserMember.id,
                  appointment2Id: targetUserMember.id,
                );
                print("Queue swap request successful: ${response.id}");
              } catch (e) {
                print("Failed to request queue swap: $e");
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text("Failed to request swap: ${e.toString()}"),
                    backgroundColor: Colors.red,
                  ),
                );
              } finally {
                if (mounted) {
                  setState(() {
                    _pendingSwapRequests.remove(targetOriginalQueuePosition);
                  });
                }
              }
            },
          );
        },
      );
    } catch (e) {
      print("Error calculating appointment times: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("Error preparing swap request: ${e.toString()}"),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // Helper to get the current user's QueueStatus
  QueueStatus _getCurrentUserQueueStatus() {
    final member = _queueMembers.firstWhere(
      (m) => m.id == _currentUserActualIdInQueue?.toString(),
      orElse:
          () => QueueMember(
            id: '',
            name: '',
            serviceDuration: Duration.zero,
            status: QueueStatus.Upcoming,
            originalPosition: 0,
          ),
    );
    return member.status;
  }

  // Helper to get the current user's position
  int _getCurrentUserQueuePosition() {
    final member = _queueMembers.firstWhere(
      (m) => m.id == _currentUserActualIdInQueue?.toString(),
      orElse:
          () => QueueMember(
            id: '',
            name: '',
            serviceDuration: Duration.zero,
            status: QueueStatus.Upcoming,
            originalPosition: 0,
          ),
    );
    return member.originalPosition;
  }

  // Helper to determine if the user can swap
  bool _canCurrentUserSwap() {
    // Example: allow swap if there is at least one eligible member
    final currentUserIndex = _queueMembers.indexWhere(
      (m) => m.id == _currentUserActualIdInQueue?.toString(),
    );
    if (currentUserIndex == -1) return false;
    for (
      int i = currentUserIndex + 1;
      i < _queueMembers.length && i <= currentUserIndex + 3;
      i++
    ) {
      if (_queueMembers[i].status == QueueStatus.Upcoming) return true;
    }
    return false;
  }

  // Action: handle swap
  void _handleCurrentUserSwap() {
    // Implement swap logic or show a dialog
    // For now, just print
    print('Swap requested by user');
  }

  // Action: handle leave
  void _handleCurrentUserLeave() {
    // Implement leave logic or show a dialog
    print('Leave queue requested by user');
  }

  // Action: handle check-in
  void _handleCurrentUserCheckIn() {
    // Implement check-in logic or show a dialog
    print('Check-in requested by user');
  }

  Future<void> _handleRespondToSwap({
    required String swapRequestId,
    required bool accept,
    String? notes,
  }) async {
    final localizations = AppLocalizations.of(context)!;
    if (!_isWebSocketConnected || _channel == null) {
      print("Cannot respond to swap: WebSocket not connected.");
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text("Not connected. Cannot respond to swap.")),
      );
      return;
    }

    // Optional: Add to a set of pending responses to prevent double-sends
    // if (_pendingSwapResponses.contains(swapRequestId)) return;
    // setState(() => _pendingSwapResponses.add(swapRequestId));

    try {
      print(
        "Responding to queue swap request $swapRequestId: ${accept ? 'Accept' : 'Reject'}",
      );
      final response = await _queueApiService.respondToQueueSwap(
        swapRequestId: swapRequestId,
        accept: accept,
        notes: notes,
      );
      print(
        "Successfully responded to queue swap request ${response.id}. Status should update via WebSocket.",
      );
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            localizations.queueSwapResponseSent(
              accept
                  ? localizations.queueSwapResponseAccepted
                  : localizations.queueSwapResponseRejected,
            ),
          ),
          backgroundColor: Colors.green,
        ),
      );
      // The queue should ideally update via WebSocket after this action.
      // If not, you might need to manually trigger a _requestQueueStatus() or update local state.
    } catch (e) {
      print("Failed to respond to queue swap: $e");
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("Failed to respond to swap: ${e.toString()}"),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      // Optional: remove from pending responses
      // if (mounted) {
      //   setState(() => _pendingSwapResponses.remove(swapRequestId));
      // }
    }
  }

  Widget _buildSwapRequestsSection() {
    // Filter involved requests to only show those pending current user's action
    List<SwapRequestInfo> actionableInvolvedRequests =
        _involvedSwapRequestsList.where((req) {
          // The current user is customer2 if their appointment ID matches appointment2.id in the SwapRequestInfo
          // And the status indicates it's customer2's turn to approve.
          bool isCurrentUserTargeted =
              req.appointment2.id == _currentUserActualIdInQueue;
          bool isPendingCurrentUserAction =
              (req.status == 'pending_customer2_approval' ||
                  req.status == 'pending_approval');

          // Additionally, let's consider if the user is `requestedBy` and status is `pending_customer1_approval`
          // This scenario means the other user rejected, and it's back to the initiator, but this shouldn't be in "Incoming" for action by others.
          // So, for "Incoming", we primarily care if the current user is NOT the one who requested AND it's pending their approval.
          // The `isIncoming` flag in `_buildSwapRequestItem` helps determine who is who.
          // Here, we just need to ensure it's an actionable status for the current user as the recipient.

          // If the current user is the one who _initiated_ the request (request.requestedBy.id matches current user's ID),
          // then it's not an "incoming" request for them to action, even if a status like 'pending_customer1_approval' might exist.
          // We need to compare request.requestedBy.id with the actual ID of the logged-in user.
          // Assuming _appSessionId relates to a user context that has a user ID, or we have another way to get it.
          // For now, the primary check is if the current user is appointment2 and status is pending_customer2_approval.

          // Let's get the current user's ID. We stored sessionID in _appSessionId.
          // The SwapRequestUser has a UUID string `id`. We need a way to get the current logged-in user's UUID.
          // Let's assume for now that if `_currentUserActualIdInQueue` is `appointment2.id`, it implies this request is for the current user to act upon.
          // And the status `pending_customer2_approval` means it's their turn.

          if (_currentUserActualIdInQueue == null)
            return false; // Cannot determine if actionable

          // The request is incoming TO ME if my appointment is appointment2 and the status requires my action.
          if (req.appointment2.id == _currentUserActualIdInQueue &&
              (req.status == 'pending_customer2_approval' ||
                  req.status == 'pending_approval')) {
            return true;
          }
          // It could also be that I am appointment1, and the other user (app2) initiated, and it's pending MY (app1's) approval.
          // This would depend on your backend's status flow. E.g. if app2 initiates, is status 'pending_customer1_approval'?
          // For now, sticking to the primary case: I am app2, status is pending_customer2_approval.

          return false; // Default to not actionable if conditions aren't met.
        }).toList();

    if (actionableInvolvedRequests.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildIndividualSwapList(
          title: "Incoming Swap Requests",
          requests: actionableInvolvedRequests, // Use the filtered list
          isIncoming: true,
        ),
      ],
    );
  }

  Widget _buildIndividualSwapList({
    required String title,
    required List<SwapRequestInfo> requests,
    required bool isIncoming,
  }) {
    final localizations = AppLocalizations.of(context)!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: EdgeInsets.symmetric(
            vertical: FetchPixels.getPixelHeight(10),
            horizontal: FetchPixels.getPixelWidth(5),
          ),
          child: getCustomFont(
            localizations.queueIncomingSwapRequests,
            18,
            Colors.black,
            1,
            fontWeight: FontWeight.w700,
          ),
        ),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: requests.length,
          itemBuilder: (context, index) {
            final request = requests[index];
            return _buildSwapRequestItem(request, isIncoming);
          },
        ),
        getVerSpace(FetchPixels.getPixelHeight(20)),
      ],
    );
  }

  Widget _buildSwapRequestItem(SwapRequestInfo request, bool isIncoming) {
    final localizations = AppLocalizations.of(context)!;
    // Determine which appointment is "theirs" and which is "yours"
    // based on whether it's an incoming or initiated request.
    SwapRequestAppointment theirAppointment;
    SwapRequestAppointment yourAppointment;
    String messagePrefix;

    final String currentUserId =
        _appSessionId ??
        ""; // Or however you get the current user's app-specific ID, not folderId
    // We need to compare against request.requestedBy.id if that's the actual logged-in user's ID.
    // For now, let's assume _currentUserActualIdInQueue is the appointment ID for the current user.

    bool currentUserIsAppointment1 =
        request.appointment1.id == _currentUserActualIdInQueue;
    bool currentUserIsAppointment2 =
        request.appointment2.id == _currentUserActualIdInQueue;

    if (isIncoming) {
      // If it's incoming, requestedBy is the other person.
      // appointment1 is theirs (initiator), appointment2 is yours (target of request)
      theirAppointment = request.appointment1;
      yourAppointment = request.appointment2;
      messagePrefix = "${request.requestedBy.fullName} wants to swap their";
    } else {
      // If it's initiated by you, requestedBy is you.
      // appointment1 is yours, appointment2 is the target's
      yourAppointment = request.appointment1;
      theirAppointment = request.appointment2;
      messagePrefix = "You requested to swap your";
    }

    // This logic might need refinement based on how your user/appointment IDs work
    // The goal is to correctly identify which appointment belongs to the current user.
    // Let's re-evaluate based on current user's actual appointment ID from the queue (_currentUserActualIdInQueue)

    if (_currentUserActualIdInQueue != null) {
      if (request.appointment1.id == _currentUserActualIdInQueue) {
        // Current user is appt1
        yourAppointment = request.appointment1;
        theirAppointment = request.appointment2;
        if (isIncoming) {
          // Someone else (reqBy) requested to swap their appt2 for your appt1
          messagePrefix = "${request.requestedBy.fullName} wants to swap their";
        } else {
          // You (reqBy) requested to swap your appt1 for their appt2
          messagePrefix = "You requested to swap your";
        }
      } else if (request.appointment2.id == _currentUserActualIdInQueue) {
        // Current user is appt2
        yourAppointment = request.appointment2;
        theirAppointment = request.appointment1;
        if (isIncoming) {
          // Someone else (reqBy) requested to swap their appt1 for your appt2
          messagePrefix = "${request.requestedBy.fullName} wants to swap their";
        } else {
          // You (reqBy) requested to swap your appt2 for their appt1
          // This case for initiated seems unlikely if appt1 is always initiator by convention
          messagePrefix = "You requested to swap your";
        }
      } else {
        // Fallback or error: current user doesn't seem to be part of this swap? This shouldn't happen.
        // For safety, using original logic if no match
        if (isIncoming) {
          theirAppointment = request.appointment1;
          yourAppointment = request.appointment2;
          messagePrefix = "${request.requestedBy.fullName} wants to swap their";
        } else {
          yourAppointment = request.appointment1;
          theirAppointment = request.appointment2;
          messagePrefix = "You requested to swap your";
        }
        print(
          "Warning: Current user ID (${_currentUserActualIdInQueue}) not found in swap request ${request.id}'s appointments.",
        );
      }
    } else {
      // Fallback if _currentUserActualIdInQueue is null
      if (isIncoming) {
        theirAppointment = request.appointment1;
        yourAppointment = request.appointment2;
        messagePrefix = "${request.requestedBy.fullName} wants to swap their";
      } else {
        yourAppointment = request.appointment1;
        theirAppointment = request.appointment2;
        messagePrefix = "You requested to swap your";
      }
    }

    return Card(
      elevation: 2,
      margin: EdgeInsets.symmetric(vertical: FetchPixels.getPixelHeight(8)),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(
          FetchPixels.getPixelHeight(12),
        ), // Slightly more rounded
      ),
      child: Padding(
        padding: EdgeInsets.all(
          FetchPixels.getPixelHeight(16),
        ), // Adjusted padding
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              messagePrefix, // Already includes their time
              style: TextStyle(
                fontSize: FetchPixels.getPixelHeight(16),
                fontWeight: FontWeight.w600, // Bolder
                color: Colors.black87,
              ),
            ),
            Text(
              localizations.queueSwapRequestsFor(
                DateFormat.jm().format(
                  yourAppointment.expectedAppointmentStartTime.toLocal(),
                ),
              ),
              style: TextStyle(
                fontSize: FetchPixels.getPixelHeight(16),
                fontWeight: FontWeight.w600, // Bolder
                color: Colors.black87,
              ),
            ),
            getVerSpace(FetchPixels.getPixelHeight(10)), // Increased space
            Text(
              localizations.queueSwapStatus(
                request.status.replaceAll('_', ' ').toLowerCase(),
              ),
              style: TextStyle(
                fontSize: FetchPixels.getPixelHeight(13),
                color: Colors.grey.shade600,
              ), // Lighter grey
            ),
            getVerSpace(FetchPixels.getPixelHeight(4)), // Reduced space here
            Text(
              localizations.queueSwapRequested(
                DateFormat.yMd().add_jm().format(request.createdAt.toLocal()),
              ),
              style: TextStyle(
                fontSize: FetchPixels.getPixelHeight(13),
                color: Colors.grey.shade600,
              ), // Lighter grey
            ),
            // Only show buttons for incoming requests that are pending the current user's approval
            if (isIncoming &&
                (request.status == 'pending_customer2_approval' ||
                    request.status == 'pending_approval'))
              Padding(
                padding: EdgeInsets.only(
                  top: FetchPixels.getPixelHeight(12),
                ), // Adjusted space
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    TextButton(
                      style: TextButton.styleFrom(
                        padding: EdgeInsets.symmetric(
                          horizontal: FetchPixels.getPixelWidth(12),
                          vertical: FetchPixels.getPixelHeight(8),
                        ),
                      ),
                      child: Text(
                        localizations.queueReject,
                        style: TextStyle(
                          color: Colors.red,
                          fontSize: FetchPixels.getPixelHeight(14),
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      onPressed: () {
                        _handleRespondToSwap(
                          swapRequestId: request.id.toString(),
                          accept: false,
                        );
                      },
                    ),
                    getHorSpace(FetchPixels.getPixelWidth(10)),
                    ElevatedButton(
                      style: ElevatedButton.styleFrom(
                        backgroundColor: daltiPrimary, // Explicitly green
                        padding: EdgeInsets.symmetric(
                          horizontal: FetchPixels.getPixelWidth(20),
                          vertical: FetchPixels.getPixelHeight(10),
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(
                            FetchPixels.getPixelHeight(8),
                          ),
                        ),
                      ),
                      child: Text(
                        localizations.queueAccept,
                        style: TextStyle(
                          fontSize: FetchPixels.getPixelHeight(14),
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      onPressed: () {
                        _handleRespondToSwap(
                          swapRequestId: request.id.toString(),
                          accept: true,
                        );
                      },
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }
}

class LiveQueueStatusCard extends StatelessWidget {
  final QueueStatus status;
  final int position;
  final bool canSwap;
  final VoidCallback onSwap;
  final VoidCallback onLeave;
  final VoidCallback onCheckIn;

  const LiveQueueStatusCard({
    Key? key,
    required this.status,
    required this.position,
    required this.canSwap,
    required this.onSwap,
    required this.onLeave,
    required this.onCheckIn,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    final Color statusColor = getColorForStatus(status);
    final String title = getStatusTitle(context, status);
    final String message = getStatusMessage(context, status, position);
    final bool showCheckIn = status == QueueStatus.Upcoming;
    final bool showImHere = status == QueueStatus.CheckedIn;
    final bool showLeave =
        status != QueueStatus.Completed && status != QueueStatus.Canceled;
    final double cardRadius = 18;

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(cardRadius),
        border: Border.all(color: Colors.grey.withOpacity(0.15)),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 18),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.w700,
                    fontSize: 18,
                    color: Colors.black,
                    letterSpacing: 0.2,
                  ),
                ),
                const SizedBox(width: 10),
                Chip(
                  label: Text(
                    title,
                    style: TextStyle(
                      color: statusColor,
                      fontWeight: FontWeight.w600,
                      fontSize: 13,
                    ),
                  ),
                  backgroundColor: statusColor.withOpacity(0.08),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  visualDensity: VisualDensity.compact,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 0,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Divider(
              height: 24,
              thickness: 1,
              color: Colors.grey.withOpacity(0.12),
            ),
            const SizedBox(height: 6),
            Row(
              children: [
                Icon(
                  Icons.confirmation_number_rounded,
                  color: statusColor,
                  size: 22,
                ),
                const SizedBox(width: 8),
                Text(
                  'Your Position: #$position',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 15,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 10),
            Text(
              message,
              style: TextStyle(
                fontSize: 15,
                color: Colors.black.withOpacity(0.85),
                fontWeight: FontWeight.w400,
                height: 1.4,
              ),
            ),
            const SizedBox(height: 18),
            if (showCheckIn || showImHere || showLeave || canSwap)
              Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  if (canSwap)
                    OutlinedButton.icon(
                      onPressed: onSwap,
                      icon: Icon(Icons.swap_horiz, color: statusColor),
                      label: Text('Swap', style: TextStyle(color: statusColor)),
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: statusColor.withOpacity(0.5)),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                    ),
                  if (showCheckIn)
                    Padding(
                      padding: const EdgeInsets.only(left: 8),
                      child: ElevatedButton(
                        onPressed: onCheckIn,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: statusColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 0,
                        ),
                        child: const Text(
                          "Check In",
                          style: TextStyle(fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                  if (showImHere)
                    Padding(
                      padding: const EdgeInsets.only(left: 8),
                      child: ElevatedButton(
                        onPressed: onCheckIn,
                        style: ElevatedButton.styleFrom(
                          backgroundColor: statusColor,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          elevation: 0,
                        ),
                        child: const Text(
                          "I'm Here",
                          style: TextStyle(fontWeight: FontWeight.w600),
                        ),
                      ),
                    ),
                  if (showLeave)
                    Padding(
                      padding: const EdgeInsets.only(left: 8),
                      child: TextButton(
                        onPressed: onLeave,
                        child: const Text(
                          "Leave Queue",
                          style: TextStyle(
                            color: Colors.red,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ),
                ],
              ),
          ],
        ),
      ),
    );
  }
}
