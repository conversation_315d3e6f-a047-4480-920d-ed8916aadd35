import 'dart:convert';
import 'package:dalti/base/auth_utils.dart';
import 'package:http/http.dart' as http;

class ReviewService {
  final String _baseUrl = "https://dapi-test.adscloud.org:8443";

  // Helper method to get auth token
  Future<String> _getAuthToken() async {
    String? sessionId = await getSessionId();
    if (sessionId == null || sessionId.isEmpty) {
      throw Exception('Authentication token not found. Please log in.');
    }
    return sessionId;
  }

  /// Create a new review
  /// Requires authentication
  Future<Map<String, dynamic>> createReview({
    required int providerId,
    required int rating,
    String? comment,
  }) async {
    final token = await _getAuthToken();
    final Uri url = Uri.parse('$_baseUrl/api/auth/reviews');

    final Map<String, dynamic> body = {
      'providerId': providerId,
      'rating': rating,
      if (comment != null && comment.isNotEmpty) 'comment': comment,
    };

    try {
      final response = await http.post(
        url,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $token',
        },
        body: jsonEncode(body),
      );

      if (response.statusCode == 201) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } else {
        final errorData = jsonDecode(response.body) as Map<String, dynamic>;
        throw Exception(errorData['message'] ?? 'Failed to create review');
      }
    } catch (e) {
      throw Exception('Error creating review: $e');
    }
  }

  /// Get reviews for a specific provider
  /// Public endpoint - no authentication required
  Future<List<Map<String, dynamic>>> getProviderReviews({
    required int providerId,
    int skip = 0,
    int take = 10,
  }) async {
    final Uri url = Uri.parse(
      '$_baseUrl/api/public/providers/$providerId/reviews',
    ).replace(
      queryParameters: {'skip': skip.toString(), 'take': take.toString()},
    );

    try {
      final response = await http.get(url);

      if (response.statusCode == 200) {
        final List<dynamic> data = jsonDecode(response.body);
        return data.cast<Map<String, dynamic>>();
      } else {
        final errorData = jsonDecode(response.body) as Map<String, dynamic>;
        throw Exception(errorData['message'] ?? 'Failed to fetch reviews');
      }
    } catch (e) {
      throw Exception('Error fetching provider reviews: $e');
    }
  }

  /// Get public provider details including reviews summary
  /// Public endpoint - no authentication required
  Future<Map<String, dynamic>> getPublicProviderDetails(int providerId) async {
    final Uri url = Uri.parse('$_baseUrl/api/public/provider/$providerId');

    try {
      final response = await http.get(url);

      if (response.statusCode == 200) {
        return jsonDecode(response.body) as Map<String, dynamic>;
      } else {
        final errorData = jsonDecode(response.body) as Map<String, dynamic>;
        throw Exception(
          errorData['message'] ?? 'Failed to fetch provider details',
        );
      }
    } catch (e) {
      throw Exception('Error fetching provider details: $e');
    }
  }
}
