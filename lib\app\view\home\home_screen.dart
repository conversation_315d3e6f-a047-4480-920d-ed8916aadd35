import 'package:dalti/app/view/home/<USER>/tab_bookings.dart';
import 'package:dalti/app/view/home/<USER>/tab_home.dart';
import 'package:dalti/app/view/home/<USER>/tab_profile.dart';
import 'package:dalti/app/view/home/<USER>/tab_messages.dart';
import 'package:dalti/base/auth_utils.dart';
import 'package:dalti/base/color_data.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:flutter/material.dart';
import 'package:dalti/services/fcm_service.dart';

import '../../../base/constant.dart';

class HomeScreen extends StatefulWidget {
  int index;

  HomeScreen(this.index, {Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int? index;
  List<Widget> tabList = [
    const TabHome(),
    const TabBookings(),
    const TabMessages(),
    const TabProfile(),
  ];

  // Updated icons to match the minimal design
  List<Map<String, String>> navigationItems = [
    {"icon": "home.svg"},
    {"icon": "calender.svg"},
    {"icon": "documnet.svg"},
    {"icon": "profile.svg"},
  ];

  int position = 0;
  bool _isLoading = true;
  Map<String, dynamic>? _userProfile;

  @override
  void initState() {
    super.initState();
    index = widget.index;
    position = index!;
    _initializeServices();
  }

  Future<void> _initializeServices() async {
    await _verifySession();
    // Only initialize FCM if user session is valid
    if (_userProfile != null) {
      try {
        await FcmService().init();
      } catch (e) {
        print('FCM initialization failed: $e');
        // Continue without FCM if it fails
      }
    }
  }

  Future<void> _verifySession() async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
    });
    final profile = await checkUserSessionAndFetchProfile(context);
    if (!mounted) return;
    if (profile != null) {
      setState(() {
        _userProfile = profile;
        _isLoading = false;
      });
    } else {}
  }

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);

    if (_isLoading) {
      return Scaffold(
        backgroundColor: appBackground,
        body: Center(child: CircularProgressIndicator(color: appPrimary)),
      );
    }

    return WillPopScope(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: appBackground,
        body: SafeArea(child: tabList[position]),
        bottomNavigationBar: bottomNavigationBar(),
      ),
      onWillPop: () async {
        Constant.closeApp();
        return false;
      },
    );
  }

  Container bottomNavigationBar() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: FetchPixels.getPixelWidth(40),
        vertical: FetchPixels.getPixelHeight(14),
      ),
      height: FetchPixels.getPixelHeight(70),
      decoration: BoxDecoration(
        color: appCard,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.04),
            blurRadius: 8,
            offset: Offset(0.0, -1.0),
          ),
        ],
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: List<Widget>.generate(navigationItems.length, (index) {
          final item = navigationItems[index];
          final isSelected = position == index;
          return GestureDetector(
            onTap: () {
              setState(() {
                position = index;
              });
            },
            child: Container(
              padding: EdgeInsets.all(FetchPixels.getPixelHeight(10)),
              decoration: BoxDecoration(
                color: isSelected ? daltiPrimary : Colors.transparent,
                borderRadius: BorderRadius.circular(
                  FetchPixels.getPixelHeight(50),
                ),
              ),
              child: getSvgImage(
                item["icon"]!,
                width: FetchPixels.getPixelHeight(24),
                height: FetchPixels.getPixelHeight(24),
                color: isSelected ? Colors.white : daltiIconDefault,
              ),
            ),
          );
        }),
      ),
    );
  }
}
