import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import '../../../base/color_data.dart';
import 'package:intl/intl.dart';
import 'package:dalti/app/services/review_service.dart';

class ProviderProfileScreen extends StatefulWidget {
  final String providerId;

  const ProviderProfileScreen({super.key, required this.providerId});

  @override
  State<ProviderProfileScreen> createState() => _ProviderProfileScreenState();
}

class _ProviderProfileScreenState extends State<ProviderProfileScreen> {
  bool _isLoading = true;
  String? _error;
  Map<String, dynamic>? _providerData;

  @override
  void initState() {
    super.initState();
    _fetchProviderData();
  }

  Future<void> _fetchProviderData() async {
    try {
      final response = await http.get(
        Uri.parse(
          'https://dapi-test.adscloud.org:8443/api/public/provider/${widget.providerId}',
        ),
      );

      if (!mounted) return;

      if (response.statusCode == 200) {
        setState(() {
          _providerData = json.decode(response.body);
          _isLoading = false;
        });
      } else if (response.statusCode == 404) {
        setState(() {
          _error = 'Provider not found';
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = 'Failed to load provider data';
          _isLoading = false;
        });
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _error = 'Error: $e';
        _isLoading = false;
      });
    }
  }

  String _getProviderName() {
    if (_providerData == null) return 'Loading...';

    final user = _providerData!['user'];
    if (user == null) return _providerData!['title'] ?? 'Unnamed Provider';

    final firstName = user['firstName'];
    final lastName = user['lastName'];

    if (firstName == null && lastName == null) {
      return _providerData!['title'] ?? 'Unnamed Provider';
    }

    return '${firstName ?? ''} ${lastName ?? ''}'.trim();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            if (Navigator.canPop(context)) {
              Navigator.pop(context);
            }
          },
        ),
        title: Text(_getProviderName()),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: Colors.black,
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(_error!, style: const TextStyle(color: Colors.red)),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _error = null;
                });
                _fetchProviderData();
              },
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_providerData == null) {
      return const Center(child: Text('No data available'));
    }

    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            // Profile Header
            Center(
              child: Column(
                children: <Widget>[
                  const CircleAvatar(
                    radius: 50,
                    backgroundColor: Colors.grey,
                    child: Icon(
                      Icons.person_outline,
                      size: 50,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    _getProviderName(),
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (_providerData!['category'] != null)
                    Text(
                      _providerData!['category']['title'] ?? '',
                      style: const TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                  if (_providerData!['presentation'] != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 8.0),
                      child: Text(
                        _providerData!['presentation'],
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.grey,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                  const SizedBox(height: 16),
                  if (_providerData!['isVerified'] == true)
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.green.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: const [
                          Icon(Icons.verified, color: Colors.green, size: 20),
                          SizedBox(width: 4),
                          Text(
                            'Verified Provider',
                            style: TextStyle(
                              color: Colors.green,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                    ),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: <Widget>[
                      if (_providerData!['phone'] != null)
                        Expanded(
                          child: ElevatedButton.icon(
                            icon: const Icon(Icons.phone),
                            label: const Text('Call'),
                            onPressed: () {
                              // TODO: Implement call functionality
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey[200],
                              foregroundColor: Colors.black,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                          ),
                        ),
                      if (_providerData!['phone'] != null)
                        const SizedBox(width: 16),
                      Expanded(
                        child: ElevatedButton.icon(
                          icon: const Icon(Icons.calendar_today),
                          label: const Text('Book'),
                          onPressed: () {
                            // TODO: Implement booking functionality
                          },
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Colors.teal[400],
                            foregroundColor: Colors.white,
                            padding: const EdgeInsets.symmetric(vertical: 16),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),

            // Tab Bar
            DefaultTabController(
              length: 4,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  const TabBar(
                    labelColor: Colors.teal,
                    unselectedLabelColor: Colors.grey,
                    indicatorColor: Colors.teal,
                    tabs: [
                      Tab(text: 'Details'),
                      Tab(text: 'Services'),
                      Tab(text: 'Staff'),
                      Tab(text: 'Reviews'),
                    ],
                  ),
                  SizedBox(
                    height: 500,
                    child: TabBarView(
                      children: [
                        _buildDetailsTab(),
                        _buildServicesTab(),
                        _buildStaffTab(),
                        _buildReviewsTab(),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailsTab() {
    final List<dynamic> providingPlaces =
        _providerData!['providingPlaces'] ?? [];

    return ListView(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      children: <Widget>[
        if (_providerData!['presentation'] != null) ...[
          const Text(
            'About',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          Text(
            _providerData!['presentation'],
            style: const TextStyle(fontSize: 16),
          ),
          const SizedBox(height: 24),
        ],
        if (providingPlaces.isNotEmpty) ...[
          const Text(
            'Locations',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          ...providingPlaces.map((place) => _buildLocationCard(place)),
          const SizedBox(height: 24),
        ],
      ],
    );
  }

  Widget _buildLocationCard(Map<String, dynamic> place) {
    final address = place['detailedAddress'];
    if (address == null) return const SizedBox.shrink();

    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(place['name'] ?? 'Unnamed Location'),
        subtitle: Text(
          '${address['address'] ?? ''}, ${address['city'] ?? ''}, ${address['country'] ?? ''}'
              .trim()
              .replaceAll(RegExp(r',\s*,'), ','),
        ),
        leading: const Icon(Icons.location_on_outlined),
      ),
    );
  }

  Widget _buildServicesTab() {
    final List<dynamic> services = _providerData!['services'] ?? [];

    if (services.isEmpty) {
      return const Center(
        child: Text(
          'No services available',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    return ListView(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      children: [
        const Text(
          'Services',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ...services.map((service) => _buildServiceCard(service)),
      ],
    );
  }

  Widget _buildStaffTab() {
    final List<dynamic> providingPlaces =
        _providerData!['providingPlaces'] ?? [];

    // Collect all queues from all providing places
    final List<Map<String, dynamic>> allStaff = [];
    for (var place in providingPlaces) {
      final List<dynamic> placeQueues = place['queues'] ?? [];
      for (var queue in placeQueues) {
        allStaff.add({
          ...queue as Map<String, dynamic>,
          'placeName': place['name'] ?? 'Unknown Location',
        });
      }
    }

    if (allStaff.isEmpty) {
      return const Center(
        child: Text(
          'No staff members available',
          style: TextStyle(color: Colors.grey),
        ),
      );
    }

    return ListView(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      children: [
        const Text(
          'Staff Members',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        ...allStaff.map((staff) => _buildStaffCard(staff)),
      ],
    );
  }

  Widget _buildServiceCard(Map<String, dynamic> service) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        title: Text(service['title'] ?? 'Unnamed Service'),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            if (service['category'] != null)
              Text(service['category']['title'] ?? 'No category'),
            Text('Duration: ${service['duration'] ?? 'N/A'} min'),
            if (service['pointsRequirements'] != null)
              Text('Points required: ${service['pointsRequirements']}'),
          ],
        ),
        isThreeLine: true,
      ),
    );
  }

  Widget _buildStaffCard(Map<String, dynamic> staff) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: Colors.grey[200],
          child: Icon(Icons.person_outline, color: Colors.grey[600]),
        ),
        title: Text(staff['title'] ?? 'Unnamed Staff Member'),
        subtitle: Text(
          'At ${staff['placeName']}',
          style: const TextStyle(color: Colors.grey),
        ),
        trailing:
            staff['isActive'] == true
                ? Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Text(
                    'Active',
                    style: TextStyle(
                      color: Colors.green,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                )
                : null,
      ),
    );
  }

  Widget _buildReviewsTab() {
    final List<dynamic> reviews = _providerData?['reviewsReceived'] ?? [];
    final double averageRating =
        _providerData?['averageRating']?.toDouble() ?? 0.0;
    final int totalReviews = _providerData?['totalReviews'] ?? 0;

    // Calculate rating distribution
    Map<int, int> ratingDistribution = {5: 0, 4: 0, 3: 0, 2: 0, 1: 0};

    for (var review in reviews) {
      int rating = review['rating'] ?? 0;
      if (rating >= 1 && rating <= 5) {
        ratingDistribution[rating] = (ratingDistribution[rating] ?? 0) + 1;
      }
    }

    // Calculate percentages
    Map<int, double> ratingPercentages = {};
    if (totalReviews > 0) {
      ratingDistribution.forEach((rating, count) {
        ratingPercentages[rating] = (count / totalReviews) * 100;
      });
    }

    return ListView(
      padding: const EdgeInsets.symmetric(vertical: 16.0, horizontal: 16.0),
      children: [
        // Write Review Button
        Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: ElevatedButton(
            onPressed: () => _showReviewDialog(),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.teal,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.rate_review_outlined, size: 20),
                SizedBox(width: 8),
                Text(
                  'Write a Review',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ],
            ),
          ),
        ),

        // Reviews Summary
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Average Rating Display
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  averageRating.toStringAsFixed(1),
                  style: const TextStyle(
                    fontSize: 48,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Row(
                  children: List.generate(5, (index) {
                    return Icon(
                      index < averageRating.floor()
                          ? Icons.star
                          : (index < averageRating
                              ? Icons.star_half
                              : Icons.star_border),
                      color: Colors.teal,
                      size: 20,
                    );
                  }),
                ),
                const SizedBox(height: 4),
                Text(
                  '$totalReviews reviews',
                  style: const TextStyle(color: Colors.grey, fontSize: 14),
                ),
              ],
            ),
            const SizedBox(width: 32),
            // Rating Distribution Bars
            Expanded(
              child: Column(
                children:
                    [5, 4, 3, 2, 1].map((rating) {
                      final percentage = ratingPercentages[rating] ?? 0.0;
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 2.0),
                        child: Row(
                          children: [
                            Text(
                              '$rating',
                              style: const TextStyle(
                                color: Colors.grey,
                                fontSize: 14,
                              ),
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Stack(
                                children: [
                                  Container(
                                    height: 8,
                                    decoration: BoxDecoration(
                                      color: Colors.grey[200],
                                      borderRadius: BorderRadius.circular(4),
                                    ),
                                  ),
                                  FractionallySizedBox(
                                    widthFactor: percentage / 100,
                                    child: Container(
                                      height: 8,
                                      decoration: BoxDecoration(
                                        color: Colors.teal,
                                        borderRadius: BorderRadius.circular(4),
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(width: 8),
                            Text(
                              '${percentage.toStringAsFixed(0)}%',
                              style: const TextStyle(
                                color: Colors.grey,
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
              ),
            ),
          ],
        ),
        const SizedBox(height: 24),
        const Divider(),
        const SizedBox(height: 16),

        // Individual Reviews
        if (reviews.isEmpty)
          const Center(
            child: Text('No reviews yet', style: TextStyle(color: Colors.grey)),
          )
        else
          ...reviews.map((review) => _buildReviewCard(review)),
      ],
    );
  }

  Widget _buildReviewCard(Map<String, dynamic> review) {
    final customer = review['customer'] ?? {};
    final String reviewerName =
        '${customer['firstName'] ?? ''} ${customer['lastName'] ?? ''}'.trim();
    final DateTime createdAt = DateTime.parse(review['createdAt']);
    final String timeAgo = _getTimeAgo(createdAt);
    final int rating = review['rating'] ?? 0;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor: Colors.grey[200],
              child: Text(
                reviewerName.isNotEmpty ? reviewerName[0].toUpperCase() : '?',
                style: TextStyle(
                  color: Colors.grey[800],
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    reviewerName.isNotEmpty ? reviewerName : 'Anonymous',
                    style: const TextStyle(
                      fontWeight: FontWeight.w600,
                      fontSize: 16,
                    ),
                  ),
                  Text(
                    timeAgo,
                    style: TextStyle(color: Colors.grey[600], fontSize: 14),
                  ),
                ],
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: List.generate(5, (index) {
            return Icon(
              index < rating ? Icons.star : Icons.star_border,
              color: Colors.teal,
              size: 18,
            );
          }),
        ),
        if (review['comment'] != null &&
            review['comment'].toString().isNotEmpty) ...[
          const SizedBox(height: 8),
          Text(
            review['comment'],
            style: const TextStyle(fontSize: 14, height: 1.4),
          ),
        ],
        const SizedBox(height: 8),
        Row(
          children: [
            _buildReactionButton(Icons.thumb_up_outlined, '12'),
            const SizedBox(width: 16),
            _buildReactionButton(Icons.thumb_down_outlined, '2'),
          ],
        ),
        const SizedBox(height: 16),
        const Divider(),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildReactionButton(IconData icon, String count) {
    return Row(
      children: [
        Icon(icon, size: 18, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(count, style: TextStyle(color: Colors.grey[600], fontSize: 14)),
      ],
    );
  }

  String _getTimeAgo(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 365) {
      return '${(difference.inDays / 365).floor()} years ago';
    } else if (difference.inDays > 30) {
      return '${(difference.inDays / 30).floor()} months ago';
    } else if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }

  Future<void> _showReviewDialog() async {
    int selectedRating = 0;
    final TextEditingController commentController = TextEditingController();
    bool isSubmitting = false;

    await showDialog(
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: const Text(
                'Write a Review',
                style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Rating',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: List.generate(5, (index) {
                        return IconButton(
                          onPressed: () {
                            setState(() {
                              selectedRating = index + 1;
                            });
                          },
                          icon: Icon(
                            index < selectedRating
                                ? Icons.star_rounded
                                : Icons.star_outline_rounded,
                            size: 32,
                            color: Colors.teal,
                          ),
                        );
                      }),
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Comment',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: commentController,
                      maxLines: 4,
                      decoration: InputDecoration(
                        hintText: 'Share your experience...',
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(8),
                          borderSide: const BorderSide(color: Colors.teal),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: Text(
                    'Cancel',
                    style: TextStyle(
                      color: Colors.grey[600],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                ElevatedButton(
                  onPressed:
                      isSubmitting
                          ? null
                          : () async {
                            if (selectedRating == 0) {
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('Please select a rating'),
                                  backgroundColor: Colors.orange,
                                ),
                              );
                              return;
                            }

                            setState(() {
                              isSubmitting = true;
                            });

                            try {
                              final reviewService = ReviewService();
                              final response = await reviewService.createReview(
                                providerId: int.parse(widget.providerId),
                                rating: selectedRating,
                                comment: commentController.text.trim(),
                              );

                              if (!mounted) return;
                              Navigator.of(context).pop();

                              // Show success message
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text(
                                    'Review submitted successfully',
                                  ),
                                  backgroundColor: Colors.green,
                                ),
                              );

                              // Refresh the provider data
                              setState(() {
                                // Add the new review to the existing reviews list
                                if (_providerData != null) {
                                  List<dynamic> currentReviews = List.from(
                                    _providerData!['reviewsReceived'] ?? [],
                                  );
                                  currentReviews.insert(0, {
                                    'rating': response['rating'],
                                    'comment': response['comment'],
                                    'createdAt': response['createdAt'],
                                    'customer': response['customer'],
                                  });

                                  // Update the reviews list
                                  _providerData!['reviewsReceived'] =
                                      currentReviews;

                                  // Update total reviews count
                                  _providerData!['totalReviews'] =
                                      (_providerData!['totalReviews'] ?? 0) + 1;

                                  // Recalculate average rating
                                  double totalRating = 0;
                                  for (var review in currentReviews) {
                                    totalRating +=
                                        (review['rating'] ?? 0).toDouble();
                                  }
                                  _providerData!['averageRating'] =
                                      totalRating / currentReviews.length;
                                }
                              });

                              // Also trigger a full refresh in the background
                              _fetchProviderData();
                            } catch (e) {
                              if (!mounted) return;
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Text('Failed to submit review: $e'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                            } finally {
                              if (mounted) {
                                setState(() {
                                  isSubmitting = false;
                                });
                              }
                            }
                          },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.teal,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                  ),
                  child:
                      isSubmitting
                          ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                          : const Text(
                            'Submit',
                            style: TextStyle(fontWeight: FontWeight.w600),
                          ),
                ),
              ],
            );
          },
        );
      },
    );
  }
}

// Notes:
// - TODOs for booking/queue actions still exist.
// - Main doctor avatar is a placeholder; replace with `Image.asset` or `Image.network`.
// - Review avatars are using initials; can be extended for images.
