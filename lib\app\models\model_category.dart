class ModelCategory {
  final int id;
  final String title;
  final int? parentId;
  List<ModelCategory> children;

  ModelCategory({
    required this.id,
    required this.title,
    this.parentId,
    this.children = const [],
  });

  factory ModelCategory.fromJson(Map<String, dynamic> json) {
    return ModelCategory(
      id: json['id'] as int,
      title: json['title'] as String,
      parentId: json['parentId'] as int?,
    );
  }

  static List<ModelCategory> buildHierarchy(List<ModelCategory> allCategories) {
    Map<int?, List<ModelCategory>> categoriesByParentId = {};
    for (var category in allCategories) {
      categoriesByParentId
          .putIfAbsent(category.parentId, () => [])
          .add(category);
    }

    void addChildren(ModelCategory parent) {
      List<ModelCategory>? childrenOfParent = categoriesByParentId[parent.id];
      if (childrenOfParent != null) {
        parent.children = childrenOfParent;
        for (var child in childrenOfParent) {
          addChildren(child);
        }
      }
    }

    List<ModelCategory> topLevelCategories = categoriesByParentId[null] ?? [];
    for (var category in topLevelCategories) {
      addChildren(category);
    }
    return topLevelCategories;
  }
}
