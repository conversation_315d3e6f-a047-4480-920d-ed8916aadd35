import 'dart:async';
import 'package:dalti/app/routes/app_routes.dart';
import 'package:dalti/base/color_data.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:dalti/services/password_reset_service.dart';
import 'package:flutter/material.dart';
import 'package:dalti/l10n/app_localizations.dart';

import '../../../base/constant.dart';

class PasswordResetRequestScreen extends StatefulWidget {
  const PasswordResetRequestScreen({Key? key}) : super(key: key);

  @override
  State<PasswordResetRequestScreen> createState() =>
      _PasswordResetRequestScreenState();
}

class _PasswordResetRequestScreenState
    extends State<PasswordResetRequestScreen> {
  final TextEditingController _emailController = TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  bool _isLoading = false;
  bool _otpSent = false;
  bool _isResendEnabled = false;
  Timer? _resendTimer;
  int _resendCountdown = 60;

  @override
  void dispose() {
    _emailController.dispose();
    _resendTimer?.cancel();
    super.dispose();
  }

  void _startResendTimer() {
    _resendCountdown = 60;
    _isResendEnabled = false;

    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _resendCountdown--;
          if (_resendCountdown <= 0) {
            _isResendEnabled = true;
            timer.cancel();
          }
        });
      } else {
        timer.cancel();
      }
    });
  }

  Future<void> _requestPasswordResetOtp() async {
    // Manual validation since text field doesn't support validators
    String email = _emailController.text.trim();

    if (email.isEmpty) {
      showCustomSnackBar(
        context,
        'Please enter your email address',
        CustomSnackBarType.warning,
        backgroundColor: daltiWarningYellow,
        textColor: daltiTextHeadline,
      );
      return;
    }

    // Basic email validation
    final emailRegex = RegExp(r'^[^@]+@[^@]+\.[^@]+$');
    if (!emailRegex.hasMatch(email)) {
      showCustomSnackBar(
        context,
        'Please enter a valid email address',
        CustomSnackBarType.warning,
        backgroundColor: daltiWarningYellow,
        textColor: daltiTextHeadline,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      String message = await PasswordResetService.requestPasswordResetOtp(
        email,
      );

      if (mounted) {
        setState(() {
          _otpSent = true;
          _isLoading = false;
        });

        _startResendTimer();

        showCustomSnackBar(
          context,
          message,
          CustomSnackBarType.success,
          backgroundColor: daltiSuccessGreen,
          textColor: daltiTextOnPrimary,
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        String errorMessage = e.toString();
        CustomSnackBarType errorType = CustomSnackBarType.error;
        Color backgroundColor = daltiErrorRed;

        if (e is PasswordResetException) {
          switch (e.type) {
            case PasswordResetErrorType.userNotFound:
              errorType = CustomSnackBarType.warning;
              backgroundColor = daltiWarningYellow;
              break;
            case PasswordResetErrorType.rateLimited:
              errorType = CustomSnackBarType.warning;
              backgroundColor = daltiWarningYellow;
              break;
            case PasswordResetErrorType.passwordLoginDisabled:
              errorType = CustomSnackBarType.info;
              backgroundColor = daltiInfoBlue;
              break;
            default:
              break;
          }
        }

        showCustomSnackBar(
          context,
          errorMessage,
          errorType,
          backgroundColor: backgroundColor,
          textColor: daltiTextOnPrimary,
        );
      }
    }
  }

  void _proceedToVerification() {
    Constant.sendToNext(
      context,
      Routes.verifyResetOtpRoute,
      arguments: {'email': _emailController.text.trim()},
    );
  }

  void _resendOtp() {
    if (_isResendEnabled) {
      _requestPasswordResetOtp();
    }
  }

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    final localizations = AppLocalizations.of(context)!;

    return WillPopScope(
      onWillPop: () async {
        Constant.backToPrev(context);
        return false;
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: daltiBackground,
        body: SafeArea(
          child: Container(
            width: double.infinity,
            height: double.infinity,
            padding: EdgeInsets.symmetric(
              horizontal: FetchPixels.getDefaultHorSpace(context),
            ),
            child: Form(
              key: _formKey,
              child: ListView(
                children: [
                  getVerSpace(FetchPixels.getPixelHeight(20)),
                  Align(
                    alignment: Alignment.topLeft,
                    child: IconButton(
                      icon: Icon(Icons.arrow_back, color: daltiIconDefault),
                      onPressed: () {
                        Constant.backToPrev(context);
                      },
                    ),
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(20)),
                  Align(
                    alignment: Alignment.topCenter,
                    child: getCustomFont(
                      "Reset Password",
                      24,
                      daltiTextHeadline,
                      1,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(10)),
                  Align(
                    alignment: Alignment.topCenter,
                    child: getMultilineCustomFont(
                      _otpSent
                          ? "We've sent a 6-digit verification code to your email address. Check your inbox and enter the code below."
                          : "Enter your email address and we'll send you a verification code to reset your password.",
                      16,
                      daltiTextBody,
                      fontWeight: FontWeight.w400,
                      txtHeight: 1.3,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(40)),

                  if (!_otpSent) ...[
                    // Email input section
                    getDefaultTextFiledWithLabel(
                      context,
                      "Email Address",
                      _emailController,
                      daltiTextMuted,
                      function: () {},
                      height: FetchPixels.getPixelHeight(60),
                      isEnable: false,
                      withprefix: true,
                      image: "message.svg",
                    ),
                    getVerSpace(FetchPixels.getPixelHeight(50)),

                    _isLoading
                        ? Center(
                          child: CircularProgressIndicator(color: daltiPrimary),
                        )
                        : getButton(
                          context,
                          daltiPrimary,
                          "Send Reset Code",
                          daltiTextOnPrimary,
                          _requestPasswordResetOtp,
                          18,
                          weight: FontWeight.w600,
                          buttonHeight: FetchPixels.getPixelHeight(60),
                          borderRadius: BorderRadius.circular(
                            FetchPixels.getPixelHeight(15),
                          ),
                        ),
                  ] else ...[
                    // OTP sent confirmation section
                    Container(
                      padding: EdgeInsets.all(FetchPixels.getPixelHeight(20)),
                      decoration: BoxDecoration(
                        color: daltiSuccessGreen.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(
                          FetchPixels.getPixelHeight(15),
                        ),
                        border: Border.all(
                          color: daltiSuccessGreen.withOpacity(0.3),
                        ),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            Icons.mark_email_read,
                            size: FetchPixels.getPixelHeight(48),
                            color: daltiSuccessGreen,
                          ),
                          getVerSpace(FetchPixels.getPixelHeight(16)),
                          getCustomFont(
                            "Code Sent!",
                            18,
                            daltiSuccessGreen,
                            1,
                            fontWeight: FontWeight.w700,
                          ),
                          getVerSpace(FetchPixels.getPixelHeight(8)),
                          getMultilineCustomFont(
                            "We've sent a verification code to:\n${_emailController.text.trim()}",
                            14,
                            daltiTextBody,
                            fontWeight: FontWeight.w400,
                            txtHeight: 1.3,
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                    getVerSpace(FetchPixels.getPixelHeight(30)),

                    getButton(
                      context,
                      daltiPrimary,
                      "Continue to Verification",
                      daltiTextOnPrimary,
                      _proceedToVerification,
                      18,
                      weight: FontWeight.w600,
                      buttonHeight: FetchPixels.getPixelHeight(60),
                      borderRadius: BorderRadius.circular(
                        FetchPixels.getPixelHeight(15),
                      ),
                    ),
                    getVerSpace(FetchPixels.getPixelHeight(20)),

                    // Resend section
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        getCustomFont(
                          "Didn't receive the code? ",
                          14,
                          daltiTextBody,
                          1,
                          fontWeight: FontWeight.w400,
                        ),
                        GestureDetector(
                          onTap: _isResendEnabled ? _resendOtp : null,
                          child: getCustomFont(
                            _isResendEnabled
                                ? "Resend"
                                : "Resend in ${_resendCountdown}s",
                            14,
                            _isResendEnabled ? daltiPrimary : daltiTextMuted,
                            1,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ],

                  getVerSpace(FetchPixels.getPixelHeight(30)),

                  // Back to login link
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      getCustomFont(
                        "Remember your password? ",
                        14,
                        daltiTextBody,
                        1,
                        fontWeight: FontWeight.w400,
                      ),
                      GestureDetector(
                        onTap: () {
                          Constant.sendToNext(context, Routes.loginRoute);
                        },
                        child: getCustomFont(
                          "Sign In",
                          14,
                          daltiPrimary,
                          1,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(20)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
