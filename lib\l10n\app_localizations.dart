import 'dart:async';

import 'package:flutter/foundation.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:intl/intl.dart' as intl;

import 'app_localizations_ar.dart';
import 'app_localizations_en.dart';
import 'app_localizations_fr.dart';

// ignore_for_file: type=lint

/// Callers can lookup localized strings with an instance of AppLocalizations
/// returned by `AppLocalizations.of(context)`.
///
/// Applications need to include `AppLocalizations.delegate()` in their app's
/// `localizationDelegates` list, and the locales they support in the app's
/// `supportedLocales` list. For example:
///
/// ```dart
/// import 'l10n/app_localizations.dart';
///
/// return MaterialApp(
///   localizationsDelegates: AppLocalizations.localizationsDelegates,
///   supportedLocales: AppLocalizations.supportedLocales,
///   home: MyApplicationHome(),
/// );
/// ```
///
/// ## Update pubspec.yaml
///
/// Please make sure to update your pubspec.yaml to include the following
/// packages:
///
/// ```yaml
/// dependencies:
///   # Internationalization support.
///   flutter_localizations:
///     sdk: flutter
///   intl: any # Use the pinned version from flutter_localizations
///
///   # Rest of dependencies
/// ```
///
/// ## iOS Applications
///
/// iOS applications define key application metadata, including supported
/// locales, in an Info.plist file that is built into the application bundle.
/// To configure the locales supported by your app, you’ll need to edit this
/// file.
///
/// First, open your project’s ios/Runner.xcworkspace Xcode workspace file.
/// Then, in the Project Navigator, open the Info.plist file under the Runner
/// project’s Runner folder.
///
/// Next, select the Information Property List item, select Add Item from the
/// Editor menu, then select Localizations from the pop-up menu.
///
/// Select and expand the newly-created Localizations item then, for each
/// locale your application supports, add a new item and select the locale
/// you wish to add from the pop-up menu in the Value field. This list should
/// be consistent with the languages listed in the AppLocalizations.supportedLocales
/// property.
abstract class AppLocalizations {
  AppLocalizations(String locale)
    : localeName = intl.Intl.canonicalizedLocale(locale.toString());

  final String localeName;

  static AppLocalizations of(BuildContext context) {
    return Localizations.of<AppLocalizations>(context, AppLocalizations)!;
  }

  static const LocalizationsDelegate<AppLocalizations> delegate =
      _AppLocalizationsDelegate();

  /// A list of this localizations delegate along with the default localizations
  /// delegates.
  ///
  /// Returns a list of localizations delegates containing this delegate along with
  /// GlobalMaterialLocalizations.delegate, GlobalCupertinoLocalizations.delegate,
  /// and GlobalWidgetsLocalizations.delegate.
  ///
  /// Additional delegates can be added by appending to this list in
  /// MaterialApp. This list does not have to be used at all if a custom list
  /// of delegates is preferred or required.
  static const List<LocalizationsDelegate<dynamic>> localizationsDelegates =
      <LocalizationsDelegate<dynamic>>[
        delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
      ];

  /// A list of this localizations delegate's supported locales.
  static const List<Locale> supportedLocales = <Locale>[
    Locale('ar'),
    Locale('en'),
    Locale('fr'),
  ];

  /// No description provided for @helloWorld.
  ///
  /// In en, this message translates to:
  /// **'Hello World!'**
  String get helloWorld;

  /// No description provided for @welcomeMessage.
  ///
  /// In en, this message translates to:
  /// **'Welcome to our App'**
  String get welcomeMessage;

  /// No description provided for @chooseLanguage.
  ///
  /// In en, this message translates to:
  /// **'Choose Your Language'**
  String get chooseLanguage;

  /// No description provided for @english.
  ///
  /// In en, this message translates to:
  /// **'English'**
  String get english;

  /// No description provided for @arabic.
  ///
  /// In en, this message translates to:
  /// **'Arabic'**
  String get arabic;

  /// No description provided for @french.
  ///
  /// In en, this message translates to:
  /// **'French'**
  String get french;

  /// No description provided for @nextButtonText.
  ///
  /// In en, this message translates to:
  /// **'Next'**
  String get nextButtonText;

  /// No description provided for @skipButtonText.
  ///
  /// In en, this message translates to:
  /// **'Skip'**
  String get skipButtonText;

  /// No description provided for @intro1Title.
  ///
  /// In en, this message translates to:
  /// **'Intro Slide 1 Title (EN)'**
  String get intro1Title;

  /// No description provided for @intro1Description.
  ///
  /// In en, this message translates to:
  /// **'Description for intro slide 1 (EN).'**
  String get intro1Description;

  /// No description provided for @intro2Title.
  ///
  /// In en, this message translates to:
  /// **'Intro Slide 2 Title (EN)'**
  String get intro2Title;

  /// No description provided for @intro2Description.
  ///
  /// In en, this message translates to:
  /// **'Description for intro slide 2 (EN).'**
  String get intro2Description;

  /// No description provided for @intro3Title.
  ///
  /// In en, this message translates to:
  /// **'Intro Slide 3 Title (EN)'**
  String get intro3Title;

  /// No description provided for @intro3Description.
  ///
  /// In en, this message translates to:
  /// **'Description for intro slide 3 (EN).'**
  String get intro3Description;

  /// No description provided for @preferredMethodTitle.
  ///
  /// In en, this message translates to:
  /// **'Choose Your Preferred Method'**
  String get preferredMethodTitle;

  /// No description provided for @preferredMethodSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Select how you\'d like to sign up or log in.'**
  String get preferredMethodSubtitle;

  /// No description provided for @useEmailButton.
  ///
  /// In en, this message translates to:
  /// **'Use Email'**
  String get useEmailButton;

  /// No description provided for @useMobileButton.
  ///
  /// In en, this message translates to:
  /// **'Use Mobile Number'**
  String get useMobileButton;

  /// No description provided for @goBackButton.
  ///
  /// In en, this message translates to:
  /// **'Go Back'**
  String get goBackButton;

  /// No description provided for @loginWithEmailTitle.
  ///
  /// In en, this message translates to:
  /// **'Login with Email'**
  String get loginWithEmailTitle;

  /// No description provided for @loginWithMobileTitle.
  ///
  /// In en, this message translates to:
  /// **'Login with Mobile'**
  String get loginWithMobileTitle;

  /// No description provided for @loginSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Glad to meet you again!'**
  String get loginSubtitle;

  /// No description provided for @emailLabel.
  ///
  /// In en, this message translates to:
  /// **'Email'**
  String get emailLabel;

  /// No description provided for @phoneNumberLabel.
  ///
  /// In en, this message translates to:
  /// **'Phone Number'**
  String get phoneNumberLabel;

  /// No description provided for @passwordLabel.
  ///
  /// In en, this message translates to:
  /// **'Password'**
  String get passwordLabel;

  /// No description provided for @forgotPasswordButton.
  ///
  /// In en, this message translates to:
  /// **'Forgot Password?'**
  String get forgotPasswordButton;

  /// No description provided for @loginButton.
  ///
  /// In en, this message translates to:
  /// **'Login'**
  String get loginButton;

  /// No description provided for @dontHaveAccountText.
  ///
  /// In en, this message translates to:
  /// **'Don\'t have an account?'**
  String get dontHaveAccountText;

  /// No description provided for @signUpButtonLoginScreen.
  ///
  /// In en, this message translates to:
  /// **' Sign Up'**
  String get signUpButtonLoginScreen;

  /// No description provided for @errorPhoneNumberEmpty.
  ///
  /// In en, this message translates to:
  /// **'Phone number cannot be empty.'**
  String get errorPhoneNumberEmpty;

  /// No description provided for @errorEmailPasswordEmpty.
  ///
  /// In en, this message translates to:
  /// **'Email and Password cannot be empty.'**
  String get errorEmailPasswordEmpty;

  /// No description provided for @errorPhonePasswordEmpty.
  ///
  /// In en, this message translates to:
  /// **'Phone number and Password cannot be empty.'**
  String get errorPhonePasswordEmpty;

  /// No description provided for @errorLoginInvalidResponse.
  ///
  /// In en, this message translates to:
  /// **'Login failed: Invalid server response. Status: {statusCode}'**
  String errorLoginInvalidResponse(Object statusCode);

  /// No description provided for @loginSuccess.
  ///
  /// In en, this message translates to:
  /// **'Login Successful!'**
  String get loginSuccess;

  /// No description provided for @errorLoginNoSessionId.
  ///
  /// In en, this message translates to:
  /// **'Login failed: Session ID not received.'**
  String get errorLoginNoSessionId;

  /// No description provided for @errorLoginWithMessageStatus.
  ///
  /// In en, this message translates to:
  /// **'Login failed: {message} (Status: {statusCode})'**
  String errorLoginWithMessageStatus(Object message, Object statusCode);

  /// No description provided for @errorLoginGeneric.
  ///
  /// In en, this message translates to:
  /// **'An error occurred during login: {errorDetails}'**
  String errorLoginGeneric(Object errorDetails);

  /// No description provided for @signUpTitle.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signUpTitle;

  /// No description provided for @signUpSubtitleEmail.
  ///
  /// In en, this message translates to:
  /// **'Sign up using your Email.'**
  String get signUpSubtitleEmail;

  /// No description provided for @signUpSubtitleMobile.
  ///
  /// In en, this message translates to:
  /// **'Sign up using your Mobile Number.'**
  String get signUpSubtitleMobile;

  /// No description provided for @signUpSubtitleGeneric.
  ///
  /// In en, this message translates to:
  /// **'Enter your details for sign up!'**
  String get signUpSubtitleGeneric;

  /// No description provided for @firstNameLabel.
  ///
  /// In en, this message translates to:
  /// **'First Name'**
  String get firstNameLabel;

  /// No description provided for @lastNameLabel.
  ///
  /// In en, this message translates to:
  /// **'Last Name'**
  String get lastNameLabel;

  /// No description provided for @agreeTermsPrivacyText.
  ///
  /// In en, this message translates to:
  /// **'I agree with Terms & Privacy'**
  String get agreeTermsPrivacyText;

  /// No description provided for @signUpButton.
  ///
  /// In en, this message translates to:
  /// **'Sign Up'**
  String get signUpButton;

  /// No description provided for @alreadyHaveAccountText.
  ///
  /// In en, this message translates to:
  /// **'Already have an account?'**
  String get alreadyHaveAccountText;

  /// No description provided for @loginButtonSignUpScreen.
  ///
  /// In en, this message translates to:
  /// **' Login'**
  String get loginButtonSignUpScreen;

  /// No description provided for @errorAgreeTerms.
  ///
  /// In en, this message translates to:
  /// **'Please agree to the Terms & Privacy.'**
  String get errorAgreeTerms;

  /// No description provided for @errorEmailEmptyPreferred.
  ///
  /// In en, this message translates to:
  /// **'Email cannot be empty as it\'s your preferred method.'**
  String get errorEmailEmptyPreferred;

  /// No description provided for @errorMobileEmptyPreferred.
  ///
  /// In en, this message translates to:
  /// **'Phone number cannot be empty as it\'s your preferred method.'**
  String get errorMobileEmptyPreferred;

  /// No description provided for @errorProvideEmailOrPhone.
  ///
  /// In en, this message translates to:
  /// **'Please provide either an Email or a Phone Number.'**
  String get errorProvideEmailOrPhone;

  /// No description provided for @errorProvideSignUpDetails.
  ///
  /// In en, this message translates to:
  /// **'Please provide details for sign up.'**
  String get errorProvideSignUpDetails;

  /// No description provided for @errorUserExists.
  ///
  /// In en, this message translates to:
  /// **'{fieldInUse} already in use.'**
  String errorUserExists(Object fieldInUse);

  /// No description provided for @errorUserExistsConflict.
  ///
  /// In en, this message translates to:
  /// **'{fieldInUse} already in use (conflict).'**
  String errorUserExistsConflict(Object fieldInUse);

  /// No description provided for @errorCheckingUser.
  ///
  /// In en, this message translates to:
  /// **'Error checking user: {statusCode} {serverMessage}'**
  String errorCheckingUser(Object serverMessage, Object statusCode);

  /// No description provided for @errorFailedCheckUserExists.
  ///
  /// In en, this message translates to:
  /// **'Failed to check user existence: {errorDetails}'**
  String errorFailedCheckUserExists(Object errorDetails);

  /// No description provided for @errorNoValidOtpIdentifier.
  ///
  /// In en, this message translates to:
  /// **'Cannot proceed without a valid email or phone for OTP.'**
  String get errorNoValidOtpIdentifier;

  /// No description provided for @forgotPasswordTitle.
  ///
  /// In en, this message translates to:
  /// **'Forgot Password?'**
  String get forgotPasswordTitle;

  /// No description provided for @forgotPasswordSubtitleEmail.
  ///
  /// In en, this message translates to:
  /// **'Enter your email to reset your password.'**
  String get forgotPasswordSubtitleEmail;

  /// No description provided for @forgotPasswordSubtitleMobile.
  ///
  /// In en, this message translates to:
  /// **'Enter your phone number to reset your password.'**
  String get forgotPasswordSubtitleMobile;

  /// No description provided for @phoneNumberWithCodeLabel.
  ///
  /// In en, this message translates to:
  /// **'Phone Number (Requires Country Code)'**
  String get phoneNumberWithCodeLabel;

  /// No description provided for @submitButton.
  ///
  /// In en, this message translates to:
  /// **'Submit'**
  String get submitButton;

  /// No description provided for @resetPasswordTitle.
  ///
  /// In en, this message translates to:
  /// **'Reset Password'**
  String get resetPasswordTitle;

  /// No description provided for @resetPasswordSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Enter your new password details below.'**
  String get resetPasswordSubtitle;

  /// No description provided for @oldPasswordLabel.
  ///
  /// In en, this message translates to:
  /// **'Old Password'**
  String get oldPasswordLabel;

  /// No description provided for @newPasswordLabel.
  ///
  /// In en, this message translates to:
  /// **'New Password'**
  String get newPasswordLabel;

  /// No description provided for @confirmPasswordLabel.
  ///
  /// In en, this message translates to:
  /// **'Confirm Password'**
  String get confirmPasswordLabel;

  /// No description provided for @setLocationDefault.
  ///
  /// In en, this message translates to:
  /// **'Set Location'**
  String get setLocationDefault;

  /// No description provided for @searchHintText.
  ///
  /// In en, this message translates to:
  /// **'Search...'**
  String get searchHintText;

  /// No description provided for @homeBannerTitle.
  ///
  /// In en, this message translates to:
  /// **'Wall Painting Service'**
  String get homeBannerTitle;

  /// No description provided for @homeBannerSubtitle.
  ///
  /// In en, this message translates to:
  /// **'Make your wall stylish'**
  String get homeBannerSubtitle;

  /// No description provided for @bookNowButton.
  ///
  /// In en, this message translates to:
  /// **'Book Now'**
  String get bookNowButton;

  /// No description provided for @categoriesTitle.
  ///
  /// In en, this message translates to:
  /// **'Categories'**
  String get categoriesTitle;

  /// No description provided for @seeAllButton.
  ///
  /// In en, this message translates to:
  /// **'See All'**
  String get seeAllButton;

  /// No description provided for @errorLoadingCategories.
  ///
  /// In en, this message translates to:
  /// **'Error: {errorMessage}'**
  String errorLoadingCategories(Object errorMessage);

  /// No description provided for @noCategoriesFound.
  ///
  /// In en, this message translates to:
  /// **'No categories found.'**
  String get noCategoriesFound;

  /// No description provided for @noParentCategoriesFound.
  ///
  /// In en, this message translates to:
  /// **'No parent categories found.'**
  String get noParentCategoriesFound;

  /// No description provided for @profileLoading.
  ///
  /// In en, this message translates to:
  /// **'Loading...'**
  String get profileLoading;

  /// No description provided for @profileUserFallback.
  ///
  /// In en, this message translates to:
  /// **'User'**
  String get profileUserFallback;

  /// No description provided for @profileErrorDisplayDetails.
  ///
  /// In en, this message translates to:
  /// **'Error displaying details'**
  String get profileErrorDisplayDetails;

  /// No description provided for @profileGuestUser.
  ///
  /// In en, this message translates to:
  /// **'Guest'**
  String get profileGuestUser;

  /// No description provided for @profilePleaseLogIn.
  ///
  /// In en, this message translates to:
  /// **'Please log in'**
  String get profilePleaseLogIn;

  /// No description provided for @profileTabTitle.
  ///
  /// In en, this message translates to:
  /// **'Profile'**
  String get profileTabTitle;

  /// No description provided for @profileLogoutButton.
  ///
  /// In en, this message translates to:
  /// **'Logout'**
  String get profileLogoutButton;

  /// No description provided for @profileSettingsButton.
  ///
  /// In en, this message translates to:
  /// **'Settings'**
  String get profileSettingsButton;

  /// No description provided for @profileMyAddressButton.
  ///
  /// In en, this message translates to:
  /// **'My Address'**
  String get profileMyAddressButton;

  /// No description provided for @profileMyCardsButton.
  ///
  /// In en, this message translates to:
  /// **'My Cards'**
  String get profileMyCardsButton;

  /// No description provided for @profileMyProfileButton.
  ///
  /// In en, this message translates to:
  /// **'My Profile'**
  String get profileMyProfileButton;

  /// No description provided for @bookingsTabTitle.
  ///
  /// In en, this message translates to:
  /// **'Bookings'**
  String get bookingsTabTitle;

  /// No description provided for @bookingsTabAll.
  ///
  /// In en, this message translates to:
  /// **'All'**
  String get bookingsTabAll;

  /// No description provided for @bookingsTabActive.
  ///
  /// In en, this message translates to:
  /// **'Active'**
  String get bookingsTabActive;

  /// No description provided for @bookingsTabCompleted.
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get bookingsTabCompleted;

  /// No description provided for @bookingsTabCancelled.
  ///
  /// In en, this message translates to:
  /// **'Cancelled'**
  String get bookingsTabCancelled;

  /// No description provided for @messagesTabTitle.
  ///
  /// In en, this message translates to:
  /// **'Messages'**
  String get messagesTabTitle;

  /// No description provided for @messagesSearchHint.
  ///
  /// In en, this message translates to:
  /// **'Search messages...'**
  String get messagesSearchHint;

  /// No description provided for @messagesEmptyTitle.
  ///
  /// In en, this message translates to:
  /// **'No Messages'**
  String get messagesEmptyTitle;

  /// No description provided for @messagesEmptySubtitle.
  ///
  /// In en, this message translates to:
  /// **'Your message list is currently empty.'**
  String get messagesEmptySubtitle;

  /// No description provided for @doctorPrefix.
  ///
  /// In en, this message translates to:
  /// **'Dr. '**
  String get doctorPrefix;

  /// No description provided for @unnamedProvider.
  ///
  /// In en, this message translates to:
  /// **'Unnamed Provider'**
  String get unnamedProvider;

  /// No description provided for @defaultSpecialization.
  ///
  /// In en, this message translates to:
  /// **'General'**
  String get defaultSpecialization;

  /// No description provided for @naHospital.
  ///
  /// In en, this message translates to:
  /// **'N/A Hospital'**
  String get naHospital;

  /// No description provided for @slotInfoUnavailable.
  ///
  /// In en, this message translates to:
  /// **'Slot information unavailable'**
  String get slotInfoUnavailable;

  /// No description provided for @errorFailedToFetchProviders.
  ///
  /// In en, this message translates to:
  /// **'Failed to fetch providers (Status: {statusCode})'**
  String errorFailedToFetchProviders(Object statusCode);

  /// No description provided for @errorFetchingData.
  ///
  /// In en, this message translates to:
  /// **'Error fetching data: {errorDetails}'**
  String errorFetchingData(Object errorDetails);

  /// No description provided for @searchResultCountForQuery.
  ///
  /// In en, this message translates to:
  /// **'{count,plural, =0{No results} =1{{count} result} other{{count} results}} for \'\'{query}\'\''**
  String searchResultCountForQuery(num count, Object query);

  /// No description provided for @searchResultCountForQueryInCategory.
  ///
  /// In en, this message translates to:
  /// **'{count,plural, =0{No results} =1{{count} result} other{{count} results}} for \'\'{query}\'\' in {category}'**
  String searchResultCountForQueryInCategory(
    Object category,
    num count,
    Object query,
  );

  /// No description provided for @searchResultCountForQueryInCity.
  ///
  /// In en, this message translates to:
  /// **'{count,plural, =0{No results} =1{{count} result} other{{count} results}} for \'\'{query}\'\' in {city}'**
  String searchResultCountForQueryInCity(Object city, num count, Object query);

  /// No description provided for @searchResultCountForQueryInCategoryInCity.
  ///
  /// In en, this message translates to:
  /// **'{count,plural, =0{No results} =1{{count} result} other{{count} results}} for \'\'{query}\'\' in {category} in {city}'**
  String searchResultCountForQueryInCategoryInCity(
    Object category,
    Object city,
    num count,
    Object query,
  );

  /// No description provided for @noProvidersFoundForQuery.
  ///
  /// In en, this message translates to:
  /// **'No providers found for \'\'{query}\'\'.'**
  String noProvidersFoundForQuery(Object query);

  /// No description provided for @noProvidersFoundForQueryWithCategory.
  ///
  /// In en, this message translates to:
  /// **'No providers found for \'\'{query}\'\'\nCategory: {category}'**
  String noProvidersFoundForQueryWithCategory(Object category, Object query);

  /// No description provided for @noProvidersFoundForQueryWithCity.
  ///
  /// In en, this message translates to:
  /// **'No providers found for \'\'{query}\'\'\nCity: {city}'**
  String noProvidersFoundForQueryWithCity(Object city, Object query);

  /// No description provided for @noProvidersFoundForQueryWithCategoryAndCity.
  ///
  /// In en, this message translates to:
  /// **'No providers found for \'\'{query}\'\'\nCategory: {category}\nCity: {city}'**
  String noProvidersFoundForQueryWithCategoryAndCity(
    Object category,
    Object city,
    Object query,
  );

  /// No description provided for @searchScreenTitle.
  ///
  /// In en, this message translates to:
  /// **'Search'**
  String get searchScreenTitle;

  /// No description provided for @searchByKeywordHint.
  ///
  /// In en, this message translates to:
  /// **'Search by keyword'**
  String get searchByKeywordHint;

  /// No description provided for @hospitalPrefix.
  ///
  /// In en, this message translates to:
  /// **'at '**
  String get hospitalPrefix;

  /// No description provided for @experienceInYears.
  ///
  /// In en, this message translates to:
  /// **'Exp. {years} years'**
  String experienceInYears(Object years);

  /// No description provided for @feesDisplay.
  ///
  /// In en, this message translates to:
  /// **'Fees {currencySymbol}{amount}'**
  String feesDisplay(Object amount, Object currencySymbol);

  /// No description provided for @reviewCountDisplay.
  ///
  /// In en, this message translates to:
  /// **'({count} reviews)'**
  String reviewCountDisplay(Object count);

  /// No description provided for @nextAvailableSlotTitle.
  ///
  /// In en, this message translates to:
  /// **'Next Available Slot'**
  String get nextAvailableSlotTitle;

  /// No description provided for @filtersModalTitle.
  ///
  /// In en, this message translates to:
  /// **'Filters'**
  String get filtersModalTitle;

  /// No description provided for @filtersCategorySectionTitle.
  ///
  /// In en, this message translates to:
  /// **'Service Type (Category)'**
  String get filtersCategorySectionTitle;

  /// No description provided for @filtersSelectCategoryPlaceholder.
  ///
  /// In en, this message translates to:
  /// **'Select Category'**
  String get filtersSelectCategoryPlaceholder;

  /// No description provided for @filtersCitySectionTitle.
  ///
  /// In en, this message translates to:
  /// **'City (Wilaya)'**
  String get filtersCitySectionTitle;

  /// No description provided for @filtersEnterCityHint.
  ///
  /// In en, this message translates to:
  /// **'Enter city or Wilaya'**
  String get filtersEnterCityHint;

  /// No description provided for @filtersClearButton.
  ///
  /// In en, this message translates to:
  /// **'Clear Filters'**
  String get filtersClearButton;

  /// No description provided for @filtersApplyButton.
  ///
  /// In en, this message translates to:
  /// **'Apply Filters'**
  String get filtersApplyButton;

  /// No description provided for @filtersAllCategoriesTitle.
  ///
  /// In en, this message translates to:
  /// **'All Categories'**
  String get filtersAllCategoriesTitle;

  /// No description provided for @filtersNoSubcategories.
  ///
  /// In en, this message translates to:
  /// **'No sub-categories.'**
  String get filtersNoSubcategories;

  /// No description provided for @filtersNoCategoriesAvailable.
  ///
  /// In en, this message translates to:
  /// **'No categories available.'**
  String get filtersNoCategoriesAvailable;

  /// No description provided for @errorFetchingCategories.
  ///
  /// In en, this message translates to:
  /// **'Error fetching categories: {errorDetails}'**
  String errorFetchingCategories(Object errorDetails);

  /// No description provided for @detailScreenCannotSelectPastDate.
  ///
  /// In en, this message translates to:
  /// **'Cannot select a past date.'**
  String get detailScreenCannotSelectPastDate;

  /// No description provided for @detailScreenBookAppointment.
  ///
  /// In en, this message translates to:
  /// **'Book an Appointment'**
  String get detailScreenBookAppointment;

  /// No description provided for @detailScreenServiceLabel.
  ///
  /// In en, this message translates to:
  /// **'Service:'**
  String get detailScreenServiceLabel;

  /// No description provided for @detailScreenNoServices.
  ///
  /// In en, this message translates to:
  /// **'No services available.'**
  String get detailScreenNoServices;

  /// No description provided for @detailScreenQueueLabel.
  ///
  /// In en, this message translates to:
  /// **'Queue:'**
  String get detailScreenQueueLabel;

  /// No description provided for @detailScreenGeneralAvailability.
  ///
  /// In en, this message translates to:
  /// **'General availability for this service.'**
  String get detailScreenGeneralAvailability;

  /// No description provided for @detailScreenSelectDate.
  ///
  /// In en, this message translates to:
  /// **'Select Date'**
  String get detailScreenSelectDate;

  /// No description provided for @detailScreenSelectTime.
  ///
  /// In en, this message translates to:
  /// **'Select Time'**
  String get detailScreenSelectTime;

  /// No description provided for @detailScreenPleaseSelectDate.
  ///
  /// In en, this message translates to:
  /// **'Please select a date.'**
  String get detailScreenPleaseSelectDate;

  /// No description provided for @detailScreenNoSlotsFound.
  ///
  /// In en, this message translates to:
  /// **'No slots found for {date}.'**
  String detailScreenNoSlotsFound(Object date);

  /// No description provided for @detailScreenAllSlotsBooked.
  ///
  /// In en, this message translates to:
  /// **'All slots are booked for {date}.'**
  String detailScreenAllSlotsBooked(Object date);

  /// No description provided for @detailScreenConfirmBooking.
  ///
  /// In en, this message translates to:
  /// **'Confirm Booking'**
  String get detailScreenConfirmBooking;

  /// No description provided for @detailScreenCancel.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get detailScreenCancel;

  /// No description provided for @detailScreenConfirm.
  ///
  /// In en, this message translates to:
  /// **'Confirm'**
  String get detailScreenConfirm;

  /// No description provided for @detailScreenIncompleteDetails.
  ///
  /// In en, this message translates to:
  /// **'Booking details are incomplete.'**
  String get detailScreenIncompleteDetails;

  /// No description provided for @detailScreenPleaseLogin.
  ///
  /// In en, this message translates to:
  /// **'Please log in to make a booking.'**
  String get detailScreenPleaseLogin;

  /// No description provided for @detailScreenBookingSuccess.
  ///
  /// In en, this message translates to:
  /// **'Booking Successful!'**
  String get detailScreenBookingSuccess;

  /// No description provided for @detailScreenBookingFailed.
  ///
  /// In en, this message translates to:
  /// **'Booking failed. Please try again.'**
  String get detailScreenBookingFailed;

  /// No description provided for @detailScreenErrorOccurred.
  ///
  /// In en, this message translates to:
  /// **'An error occurred: {error}'**
  String detailScreenErrorOccurred(Object error);

  /// No description provided for @detailScreenPointsRequired.
  ///
  /// In en, this message translates to:
  /// **'This service requires {points} points to book.'**
  String detailScreenPointsRequired(Object points);

  /// No description provided for @detailScreenNoPhoneNumber.
  ///
  /// In en, this message translates to:
  /// **'Phone number is not available.'**
  String get detailScreenNoPhoneNumber;

  /// No description provided for @detailScreenCannotLaunchCall.
  ///
  /// In en, this message translates to:
  /// **'Could not launch phone call.'**
  String get detailScreenCannotLaunchCall;

  /// No description provided for @detailScreenServiceDetail.
  ///
  /// In en, this message translates to:
  /// **'Service:'**
  String get detailScreenServiceDetail;

  /// No description provided for @detailScreenQueueDetail.
  ///
  /// In en, this message translates to:
  /// **'Queue:'**
  String get detailScreenQueueDetail;

  /// No description provided for @detailScreenDateDetail.
  ///
  /// In en, this message translates to:
  /// **'Date:'**
  String get detailScreenDateDetail;

  /// No description provided for @detailScreenTimeDetail.
  ///
  /// In en, this message translates to:
  /// **'Time:'**
  String get detailScreenTimeDetail;

  /// No description provided for @detailScreenBook.
  ///
  /// In en, this message translates to:
  /// **'Book: {time} on {date}'**
  String detailScreenBook(Object date, Object time);

  /// No description provided for @settingsSecurityTitle.
  ///
  /// In en, this message translates to:
  /// **'Security'**
  String get settingsSecurityTitle;

  /// No description provided for @settingsHelpTitle.
  ///
  /// In en, this message translates to:
  /// **'Help & Support'**
  String get settingsHelpTitle;

  /// No description provided for @settingsPrivacyTitle.
  ///
  /// In en, this message translates to:
  /// **'Privacy Policy'**
  String get settingsPrivacyTitle;

  /// No description provided for @settingsTermsTitle.
  ///
  /// In en, this message translates to:
  /// **'Terms of Service'**
  String get settingsTermsTitle;

  /// No description provided for @settingsSecurityContent.
  ///
  /// In en, this message translates to:
  /// **'Your security is our top priority. We implement industry-standard security measures to protect your data and ensure a safe experience while using our services.'**
  String get settingsSecurityContent;

  /// No description provided for @settingsHelpContent.
  ///
  /// In en, this message translates to:
  /// **'Need assistance? Our support team is here to help you with any questions or concerns you may have about our services.'**
  String get settingsHelpContent;

  /// No description provided for @settingsPrivacyContent.
  ///
  /// In en, this message translates to:
  /// **'We value your privacy. This policy outlines how we collect, use, and protect your personal information when you use our services.'**
  String get settingsPrivacyContent;

  /// No description provided for @settingsTermsContent.
  ///
  /// In en, this message translates to:
  /// **'By using our services, you agree to these terms. Please read them carefully as they govern your use of our platform and services.'**
  String get settingsTermsContent;

  /// No description provided for @notificationsTitle.
  ///
  /// In en, this message translates to:
  /// **'Notifications'**
  String get notificationsTitle;

  /// No description provided for @notificationsToday.
  ///
  /// In en, this message translates to:
  /// **'Today'**
  String get notificationsToday;

  /// No description provided for @notificationsYesterday.
  ///
  /// In en, this message translates to:
  /// **'Yesterday'**
  String get notificationsYesterday;

  /// No description provided for @notificationsRetry.
  ///
  /// In en, this message translates to:
  /// **'Retry'**
  String get notificationsRetry;

  /// No description provided for @notificationsEmpty.
  ///
  /// In en, this message translates to:
  /// **'No Notifications Yet!'**
  String get notificationsEmpty;

  /// No description provided for @notificationsEmptyDesc.
  ///
  /// In en, this message translates to:
  /// **'We\'ll notify you when something arrives.'**
  String get notificationsEmptyDesc;

  /// No description provided for @queueStatusUpcoming.
  ///
  /// In en, this message translates to:
  /// **'Upcoming'**
  String get queueStatusUpcoming;

  /// No description provided for @queueStatusCheckedIn.
  ///
  /// In en, this message translates to:
  /// **'Checked In'**
  String get queueStatusCheckedIn;

  /// No description provided for @queueStatusWaitingRoom.
  ///
  /// In en, this message translates to:
  /// **'Please wait comfortably'**
  String get queueStatusWaitingRoom;

  /// No description provided for @queueStatusCalledIn.
  ///
  /// In en, this message translates to:
  /// **'You\'re being called in!'**
  String get queueStatusCalledIn;

  /// No description provided for @queueStatusInProgress.
  ///
  /// In en, this message translates to:
  /// **'Service in Progress'**
  String get queueStatusInProgress;

  /// No description provided for @queueStatusCompleted.
  ///
  /// In en, this message translates to:
  /// **'Completed'**
  String get queueStatusCompleted;

  /// No description provided for @queueStatusSkipped.
  ///
  /// In en, this message translates to:
  /// **'Skipped'**
  String get queueStatusSkipped;

  /// No description provided for @queueStatusRequeued.
  ///
  /// In en, this message translates to:
  /// **'Re-queued'**
  String get queueStatusRequeued;

  /// No description provided for @queueStatusCanceled.
  ///
  /// In en, this message translates to:
  /// **'Canceled'**
  String get queueStatusCanceled;

  /// No description provided for @queueStatusUnknown.
  ///
  /// In en, this message translates to:
  /// **'Unknown'**
  String get queueStatusUnknown;

  /// No description provided for @queueMessageUpcoming.
  ///
  /// In en, this message translates to:
  /// **'You are #{position} in line. Please be ready.'**
  String queueMessageUpcoming(int position);

  /// No description provided for @queueMessageCheckedIn.
  ///
  /// In en, this message translates to:
  /// **'Let us know when you arrive!'**
  String get queueMessageCheckedIn;

  /// No description provided for @queueMessageWaitingRoom.
  ///
  /// In en, this message translates to:
  /// **'Your provider will call you soon. You\'re #{position} in line.'**
  String queueMessageWaitingRoom(int position);

  /// No description provided for @queueMessageCalledIn.
  ///
  /// In en, this message translates to:
  /// **'Please proceed to the service area.'**
  String get queueMessageCalledIn;

  /// No description provided for @queueMessageInProgress.
  ///
  /// In en, this message translates to:
  /// **'Your service is in progress.'**
  String get queueMessageInProgress;

  /// No description provided for @queueMessageCompleted.
  ///
  /// In en, this message translates to:
  /// **'Thank you for using our service!'**
  String get queueMessageCompleted;

  /// No description provided for @queueMessageSkipped.
  ///
  /// In en, this message translates to:
  /// **'You missed your turn.'**
  String get queueMessageSkipped;

  /// No description provided for @queueMessageRequeued.
  ///
  /// In en, this message translates to:
  /// **'You have been re-added to the queue.'**
  String get queueMessageRequeued;

  /// No description provided for @queueMessageCanceled.
  ///
  /// In en, this message translates to:
  /// **'Your booking was canceled.'**
  String get queueMessageCanceled;

  /// No description provided for @bookingDetailProviderDetails.
  ///
  /// In en, this message translates to:
  /// **'Provider Details:'**
  String get bookingDetailProviderDetails;

  /// No description provided for @bookingDetailAppointmentFor.
  ///
  /// In en, this message translates to:
  /// **'Appointment For:'**
  String get bookingDetailAppointmentFor;

  /// No description provided for @bookingDetailServicePrefix.
  ///
  /// In en, this message translates to:
  /// **'Service: '**
  String get bookingDetailServicePrefix;

  /// No description provided for @bookingDetailQueuePrefix.
  ///
  /// In en, this message translates to:
  /// **'Queue: '**
  String get bookingDetailQueuePrefix;

  /// No description provided for @bookingDetailTimePrefix.
  ///
  /// In en, this message translates to:
  /// **'Time: '**
  String get bookingDetailTimePrefix;

  /// No description provided for @bookingDetailMapButton.
  ///
  /// In en, this message translates to:
  /// **'Map'**
  String get bookingDetailMapButton;

  /// No description provided for @bookingDetailCallButton.
  ///
  /// In en, this message translates to:
  /// **'Call'**
  String get bookingDetailCallButton;

  /// No description provided for @bookingDetailCancelButton.
  ///
  /// In en, this message translates to:
  /// **'Cancel'**
  String get bookingDetailCancelButton;

  /// No description provided for @bookingDetailCancelConfirmTitle.
  ///
  /// In en, this message translates to:
  /// **'Cancel Appointment?'**
  String get bookingDetailCancelConfirmTitle;

  /// No description provided for @bookingDetailCancelConfirmMessage.
  ///
  /// In en, this message translates to:
  /// **'Are you sure you want to cancel this appointment? This action cannot be undone.'**
  String get bookingDetailCancelConfirmMessage;

  /// No description provided for @bookingDetailCancelSuccess.
  ///
  /// In en, this message translates to:
  /// **'Appointment cancelled successfully.'**
  String get bookingDetailCancelSuccess;

  /// No description provided for @bookingDetailCancelError.
  ///
  /// In en, this message translates to:
  /// **'Failed to cancel appointment. Please try again.'**
  String get bookingDetailCancelError;

  /// No description provided for @bookingDetailLocationError.
  ///
  /// In en, this message translates to:
  /// **'Error getting location: {error}'**
  String bookingDetailLocationError(Object error);

  /// No description provided for @bookingDetailLocationPermissionDenied.
  ///
  /// In en, this message translates to:
  /// **'Location permission denied'**
  String get bookingDetailLocationPermissionDenied;

  /// No description provided for @bookingDetailLocationServiceDisabled.
  ///
  /// In en, this message translates to:
  /// **'Location services are disabled'**
  String get bookingDetailLocationServiceDisabled;

  /// No description provided for @bookingDetailDirectionsError.
  ///
  /// In en, this message translates to:
  /// **'Error getting directions: {error}'**
  String bookingDetailDirectionsError(Object error);

  /// No description provided for @bookingDetailDistance.
  ///
  /// In en, this message translates to:
  /// **'Distance: {distance}'**
  String bookingDetailDistance(Object distance);

  /// No description provided for @bookingDetailDuration.
  ///
  /// In en, this message translates to:
  /// **'Duration: {duration}'**
  String bookingDetailDuration(Object duration);

  /// No description provided for @bookingDetailOpenInMaps.
  ///
  /// In en, this message translates to:
  /// **'Open in Maps'**
  String get bookingDetailOpenInMaps;

  /// No description provided for @bookingDetailGetDirections.
  ///
  /// In en, this message translates to:
  /// **'Get Directions'**
  String get bookingDetailGetDirections;

  /// No description provided for @bookingDetailFetchingLocation.
  ///
  /// In en, this message translates to:
  /// **'Fetching location...'**
  String get bookingDetailFetchingLocation;

  /// No description provided for @bookingDetailCalculatingRoutes.
  ///
  /// In en, this message translates to:
  /// **'Calculating routes...'**
  String get bookingDetailCalculatingRoutes;

  /// No description provided for @queueLiveStatusTitle.
  ///
  /// In en, this message translates to:
  /// **'Live Queue Status'**
  String get queueLiveStatusTitle;

  /// No description provided for @queueTimerPaused.
  ///
  /// In en, this message translates to:
  /// **'Paused'**
  String get queueTimerPaused;

  /// No description provided for @queueLoadingService.
  ///
  /// In en, this message translates to:
  /// **'Loading service...'**
  String get queueLoadingService;

  /// No description provided for @queueLoadingDate.
  ///
  /// In en, this message translates to:
  /// **'Loading date...'**
  String get queueLoadingDate;

  /// No description provided for @queueLoadingTime.
  ///
  /// In en, this message translates to:
  /// **'Loading time...'**
  String get queueLoadingTime;

  /// No description provided for @queueFindingProfessional.
  ///
  /// In en, this message translates to:
  /// **'Finding professional...'**
  String get queueFindingProfessional;

  /// No description provided for @queueYouAreNext.
  ///
  /// In en, this message translates to:
  /// **'You Are Next'**
  String get queueYouAreNext;

  /// No description provided for @queueYourTurn.
  ///
  /// In en, this message translates to:
  /// **'Your Turn'**
  String get queueYourTurn;

  /// No description provided for @queueYourPosition.
  ///
  /// In en, this message translates to:
  /// **'Your Position {position}'**
  String queueYourPosition(Object position);

  /// No description provided for @queueTimerPausedMessage.
  ///
  /// In en, this message translates to:
  /// **'Timer Paused: Waiting for service to start.'**
  String get queueTimerPausedMessage;

  /// No description provided for @queueViewQRCode.
  ///
  /// In en, this message translates to:
  /// **'View QRCode'**
  String get queueViewQRCode;

  /// No description provided for @queueCheckInDetails.
  ///
  /// In en, this message translates to:
  /// **'Check-In Details'**
  String get queueCheckInDetails;

  /// No description provided for @queueDone.
  ///
  /// In en, this message translates to:
  /// **'Done'**
  String get queueDone;

  /// No description provided for @queueEmptyOrNotListed.
  ///
  /// In en, this message translates to:
  /// **'Queue is currently empty or your spot is not listed.'**
  String get queueEmptyOrNotListed;

  /// No description provided for @queueIncomingSwapRequests.
  ///
  /// In en, this message translates to:
  /// **'Incoming Swap Requests'**
  String get queueIncomingSwapRequests;

  /// No description provided for @queueSwapRequestsFor.
  ///
  /// In en, this message translates to:
  /// **'for your spot at {time}'**
  String queueSwapRequestsFor(Object time);

  /// No description provided for @queueSwapStatus.
  ///
  /// In en, this message translates to:
  /// **'Status: {status}'**
  String queueSwapStatus(Object status);

  /// No description provided for @queueSwapRequested.
  ///
  /// In en, this message translates to:
  /// **'Requested: {dateTime}'**
  String queueSwapRequested(Object dateTime);

  /// No description provided for @queueReject.
  ///
  /// In en, this message translates to:
  /// **'Reject'**
  String get queueReject;

  /// No description provided for @queueAccept.
  ///
  /// In en, this message translates to:
  /// **'Accept'**
  String get queueAccept;

  /// No description provided for @queueSwapResponseSent.
  ///
  /// In en, this message translates to:
  /// **'Swap response sent: {action}'**
  String queueSwapResponseSent(Object action);

  /// No description provided for @queueSwapResponseAccepted.
  ///
  /// In en, this message translates to:
  /// **'Accepted'**
  String get queueSwapResponseAccepted;

  /// No description provided for @queueSwapResponseRejected.
  ///
  /// In en, this message translates to:
  /// **'Rejected'**
  String get queueSwapResponseRejected;

  /// No description provided for @queueNotConnected.
  ///
  /// In en, this message translates to:
  /// **'Not connected. Cannot respond to swap.'**
  String get queueNotConnected;

  /// No description provided for @queueMemberLabelYouInProgress.
  ///
  /// In en, this message translates to:
  /// **'Your Turn (In Progress) - Pos {position}'**
  String queueMemberLabelYouInProgress(Object position);

  /// No description provided for @queueMemberLabelYouCompleted.
  ///
  /// In en, this message translates to:
  /// **'You - Service Completed - Pos {position}'**
  String queueMemberLabelYouCompleted(Object position);

  /// No description provided for @queueMemberLabelYouStartingSoon.
  ///
  /// In en, this message translates to:
  /// **'You - Starting Soon - Pos {position}'**
  String queueMemberLabelYouStartingSoon(Object position);

  /// No description provided for @queueMemberLabelYouPosition.
  ///
  /// In en, this message translates to:
  /// **'You - Position {position}'**
  String queueMemberLabelYouPosition(Object position);

  /// No description provided for @queueMemberLabelOtherStartingSoon.
  ///
  /// In en, this message translates to:
  /// **'{name} - Starting Soon - Pos {position}'**
  String queueMemberLabelOtherStartingSoon(Object name, Object position);

  /// No description provided for @queueMemberLabelOtherPosition.
  ///
  /// In en, this message translates to:
  /// **'{name} - Pos {position}'**
  String queueMemberLabelOtherPosition(Object name, Object position);

  /// No description provided for @queueMemberLabelSuffixInProgress.
  ///
  /// In en, this message translates to:
  /// **' (In Progress)'**
  String get queueMemberLabelSuffixInProgress;

  /// No description provided for @queueMemberLabelSuffixCompleted.
  ///
  /// In en, this message translates to:
  /// **' (Completed)'**
  String get queueMemberLabelSuffixCompleted;
}

class _AppLocalizationsDelegate
    extends LocalizationsDelegate<AppLocalizations> {
  const _AppLocalizationsDelegate();

  @override
  Future<AppLocalizations> load(Locale locale) {
    return SynchronousFuture<AppLocalizations>(lookupAppLocalizations(locale));
  }

  @override
  bool isSupported(Locale locale) =>
      <String>['ar', 'en', 'fr'].contains(locale.languageCode);

  @override
  bool shouldReload(_AppLocalizationsDelegate old) => false;
}

AppLocalizations lookupAppLocalizations(Locale locale) {
  // Lookup logic when only language code is specified.
  switch (locale.languageCode) {
    case 'ar':
      return AppLocalizationsAr();
    case 'en':
      return AppLocalizationsEn();
    case 'fr':
      return AppLocalizationsFr();
  }

  throw FlutterError(
    'AppLocalizations.delegate failed to load unsupported locale "$locale". This is likely '
    'an issue with the localizations generation tool. Please file an issue '
    'on GitHub with a reproducible sample app and the gen-l10n configuration '
    'that was used.',
  );
}
