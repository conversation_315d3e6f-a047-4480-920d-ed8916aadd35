import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart'; // For kDebugMode

// Assuming these constants are available, e.g., from a common constants file
// import 'package:dalti/base/constant.dart'; // Ideal: if prefsKeySessionId is here
const String prefsKeySessionId = 'session_id'; // Placeholder: Replace or import
const String baseApiUrl = "https://dapi-test.adscloud.org:8443";

// Placeholder for the response model from requestQueueSwap.
// You might want to define this more concretely based on the actual API response fields.
class QueueSwapRequestModel {
  final int id;
  // Add other fields like status, appointment1Id, appointment2Id, createdAt, etc.
  // e.g., final String status;

  QueueSwapRequestModel({required this.id /*, required this.status */});

  factory QueueSwapRequestModel.fromJson(Map<String, dynamic> json) {
    return QueueSwapRequestModel(
      id: json['id'] as int,
      // status: json['status'] as String,
    );
  }
}

class QueueApiService {
  Future<Map<String, String>> _getHeaders() async {
    final prefs = await SharedPreferences.getInstance();
    final sessionId = prefs.getString(prefsKeySessionId);
    if (sessionId == null || sessionId.isEmpty) {
      throw Exception('User not authenticated. Session ID is missing.');
    }
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $sessionId',
    };
  }

  Future<QueueSwapRequestModel> requestQueueSwap({
    required String appointment1Id,
    required String appointment2Id,
    String? notes,
  }) async {
    final uri = Uri.parse('$baseApiUrl/api/auth/queue/request-swap');
    final Map<String, dynamic> body = {
      'appointment1Id': int.parse(appointment1Id),
      'appointment2Id': int.parse(appointment2Id),
    };
    if (notes != null && notes.isNotEmpty) {
      body['notes'] = notes;
    }

    if (kDebugMode) {
      print("Requesting queue swap: $uri with body: ${jsonEncode(body)}");
    }

    try {
      final headers = await _getHeaders();
      final response = await http.post(
        uri,
        headers: headers,
        body: jsonEncode(body),
      );

      if (response.statusCode == 201) {
        // 201 Created
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        return QueueSwapRequestModel.fromJson(responseData);
      } else if (response.statusCode == 401) {
        throw Exception('User not authenticated.');
      } else {
        // Handle other error codes (400, 403, 404, 500, etc.) based on your API spec
        // For now, a generic error
        String errorMessage = 'Failed to request queue swap.';
        try {
          final responseData =
              jsonDecode(response.body) as Map<String, dynamic>;
          errorMessage = responseData['error'] as String? ?? errorMessage;
        } catch (_) {
          /* Ignore if body isn't JSON or doesn't contain 'error' */
        }
        if (kDebugMode) {
          print(
            "Error requesting queue swap: ${response.statusCode} Body: ${response.body}",
          );
        }
        throw Exception('$errorMessage (Status: ${response.statusCode})');
      }
    } catch (e) {
      if (kDebugMode) {
        print("Exception requesting queue swap: $e");
      }
      if (e is Exception) rethrow;
      throw Exception('Failed to request queue swap: $e');
    }
  }

  Future<QueueSwapRequestModel> respondToQueueSwap({
    required String swapRequestId,
    required bool accept,
    String? notes,
  }) async {
    final uri = Uri.parse('$baseApiUrl/api/auth/queue/respond-swap');
    final Map<String, dynamic> body = {
      'swapRequestId': int.parse(swapRequestId),
      'accept': accept,
    };
    if (notes != null && notes.isNotEmpty) {
      body['notes'] = notes;
    }

    if (kDebugMode) {
      print("Responding to queue swap: $uri with body: ${jsonEncode(body)}");
    }

    try {
      final headers = await _getHeaders();
      final response = await http.post(
        uri,
        headers: headers,
        body: jsonEncode(body),
      );

      print(response.body);
      if (response.statusCode == 200) {
        // 200 OK for update
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        // Assuming this also returns the updated QueueSwapRequestModel
        return QueueSwapRequestModel.fromJson(responseData);
      } else if (response.statusCode == 400) {
        // Try to get a more specific error message from the response body
        String errorMessage =
            'Bad Request (e.g., swapRequestId missing or invalid).';
        try {
          final responseData =
              jsonDecode(response.body) as Map<String, dynamic>;
          errorMessage = responseData['error'] as String? ?? errorMessage;
        } catch (_) {
          /* Ignore if body isn't JSON or doesn't contain 'error' */
        }
        throw Exception(errorMessage);
      } else if (response.statusCode == 401) {
        throw Exception('User not authenticated.');
      } else if (response.statusCode == 403) {
        throw Exception(
          'Forbidden: User not authorized to respond to this swap request.',
        );
      } else if (response.statusCode == 404) {
        throw Exception('Queue swap request not found.');
      } else {
        String errorMessage = 'Failed to respond to queue swap.';
        try {
          final responseData =
              jsonDecode(response.body) as Map<String, dynamic>;
          errorMessage = responseData['error'] as String? ?? errorMessage;
        } catch (_) {
          /* Ignore */
        }
        if (kDebugMode) {
          print(
            "Error responding to queue swap: ${response.statusCode} Body: ${response.body}",
          );
        }
        throw Exception('$errorMessage (Status: ${response.statusCode})');
      }
    } catch (e) {
      if (kDebugMode) {
        print("Exception responding to queue swap: $e");
      }
      if (e is Exception) rethrow;
      throw Exception('Failed to respond to queue swap: $e');
    }
  }
}
