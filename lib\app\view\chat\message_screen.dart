import 'package:dalti/app/models/model_chat_message.dart';
import 'package:dalti/app/view/home/<USER>/tab_messages.dart'; // For ModelMessage (sender info)
import 'package:dalti/base/color_data.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:dalti/base/auth_utils.dart';
import 'package:dalti/services/mobile_messaging_service.dart'; // Add this import
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart'; // Add this import for kDebugMode
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';

class MessageScreen extends StatefulWidget {
  final ModelMessage recipient; // Contains sender info from TabMessages list
  final int conversationId; // Add this parameter
  // final String chatId; // You might pass a chatId to fetch messages

  const MessageScreen({
    Key? key,
    required this.recipient,
    required this.conversationId,
    // required this.chatId,
  }) : super(key: key);

  @override
  State<MessageScreen> createState() => _MessageScreenState();
}

class _MessageScreenState extends State<MessageScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  final MobileMessagingService _messagingService = MobileMessagingService();

  List<MessageModel> _messages = [];
  bool _isLoading = true;
  String? _error;
  String? _currentUserId; // We'll need to get this from your auth service

  @override
  void initState() {
    super.initState();
    _loadMessages();
    _getCurrentUserId(); // Add this call
    // Scroll to bottom after layout
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.jumpTo(_scrollController.position.maxScrollExtent);
      }
    });
  }

  Future<void> _getCurrentUserId() async {
    final user = await checkUserSessionAndFetchProfile(context);
    print('[MessageScreen] Fetched user: $user');
    String? userId;
    if (user != null) {
      userId = user['json']['id'] as String?;
      if (kDebugMode) {
        print('[MessageScreen] User ID extracted: $userId');
      }
    }
    if (kDebugMode) {
      if (user == null) {
        print(
          '[MessageScreen] WARNING: Fetched user_id from SharedPreferences is NULL. Ensure it is saved correctly at login with key \'user_id\'.',
        );
      } else {
        print(
          '[MessageScreen] Fetched from SharedPreferences user_id: $userId',
        );
      }
    }
    if (mounted) {
      setState(() {
        _currentUserId = userId;
      });
    }
  }

  Future<void> _loadMessages() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final messages = await _messagingService.getMessages(
        widget.conversationId,
      );

      if (mounted) {
        setState(() {
          _messages = messages;
          _isLoading = false;
        });

        // Mark messages as read
        _markUnreadMessagesAsRead();

        // Scroll to bottom after loading messages
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_scrollController.hasClients) {
            _scrollController.jumpTo(
              _scrollController.position.maxScrollExtent,
            );
          }
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _error = e.toString();
          _isLoading = false;
        });
      }
    }
  }

  void _markUnreadMessagesAsRead() async {
    try {
      for (var message in _messages) {
        if (message.id != null &&
            message.sender?.id != null &&
            message.status != 'READ' &&
            message.sender!.id != _currentUserId) {
          await _messagingService.markMessageAsRead(
            message.id!,
            widget.conversationId,
          );
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error marking messages as read: $e');
      }
    }
  }

  Future<void> _sendMessage() async {
    final text = _messageController.text.trim();
    if (text.isEmpty) return;

    _messageController.clear();

    try {
      final message = await _messagingService.sendMessage(
        widget.conversationId,
        text,
      );

      if (mounted) {
        setState(() {
          _messages = [..._messages, message];
        });

        // Scroll to bottom after sending
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (_scrollController.hasClients) {
            _scrollController.animateTo(
              _scrollController.position.maxScrollExtent,
              duration: const Duration(milliseconds: 300),
              curve: Curves.easeOut,
            );
          }
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to send message: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    return Scaffold(
      backgroundColor: daltiBackground,
      appBar: _buildAppBar(context),
      body: Column(
        children: [
          if (_error != null)
            Container(
              padding: EdgeInsets.all(FetchPixels.getPixelHeight(16)),
              color: Colors.red.shade50,
              child: Row(
                children: [
                  Icon(Icons.error_outline, color: Colors.red),
                  SizedBox(width: FetchPixels.getPixelWidth(8)),
                  Expanded(
                    child: Text(_error!, style: TextStyle(color: Colors.red)),
                  ),
                  IconButton(
                    icon: Icon(Icons.refresh),
                    onPressed: _loadMessages,
                  ),
                ],
              ),
            ),
          Expanded(
            child:
                _isLoading
                    ? Center(child: CircularProgressIndicator())
                    : ListView.builder(
                      reverse: false,
                      controller: _scrollController,
                      padding: EdgeInsets.symmetric(
                        horizontal: FetchPixels.getPixelWidth(12),
                        vertical: FetchPixels.getPixelHeight(16),
                      ),
                      itemCount: _messages.length,
                      itemBuilder: (context, index) {
                        final message = _messages[index];

                        if (kDebugMode) {
                          print('[MessageScreen] Comparing for isCurrentUser:');
                          print('  _currentUserId: $_currentUserId');
                          print('  message.sender?.id: ${message.sender?.id}');
                          print('  message.content: ${message.content}');
                        }

                        final bool isCurrentUser =
                            message.sender?.id == _currentUserId &&
                            _currentUserId != null;

                        if (kDebugMode) {
                          print('  Resulting isCurrentUser: $isCurrentUser');
                        }

                        bool showDateHeader = false;
                        if (index == 0) {
                          showDateHeader = true;
                        } else {
                          final prevMessage = _messages[index - 1];
                          if (message.createdAt.day !=
                                  prevMessage.createdAt.day ||
                              message.createdAt.month !=
                                  prevMessage.createdAt.month ||
                              message.createdAt.year !=
                                  prevMessage.createdAt.year) {
                            showDateHeader = true;
                          }
                        }

                        return Column(
                          children: [
                            if (showDateHeader)
                              _buildDateHeader(message.createdAt),
                            _buildMessageItem(message, isCurrentUser, context),
                          ],
                        );
                      },
                    ),
          ),
          _buildMessageInputField(),
        ],
      ),
    );
  }

  AppBar _buildAppBar(BuildContext context) {
    Widget avatarImage;
    final recipientAvatarUrl = widget.recipient.senderAvatarUrl;

    if (recipientAvatarUrl != null && recipientAvatarUrl.startsWith('http')) {
      avatarImage = CircleAvatar(
        radius: FetchPixels.getPixelHeight(18),
        backgroundColor: daltiPrimaryLight,
        backgroundImage: NetworkImage(recipientAvatarUrl),
        onBackgroundImageError: (exception, stackTrace) {
          if (kDebugMode)
            print('Error loading network image for recipient: $exception');
          // Optionally, set a flag to use fallback
        },
        // If backgroundImage fails, child (Icon) will be shown if not null and background is transparent enough
        // To ensure fallback icon shows, we might need more complex logic or remove child if image loads.
        // For simplicity, relying on onBackgroundImageError for logging, default Icon used if URL is bad.
      );
    } else if (recipientAvatarUrl != null && recipientAvatarUrl.isNotEmpty) {
      // Try as asset if not HTTP and not empty
      avatarImage = Image.asset(
        recipientAvatarUrl,
        width: FetchPixels.getPixelHeight(36),
        height: FetchPixels.getPixelHeight(36),
        fit: BoxFit.cover,
        errorBuilder: (context, error, stackTrace) {
          if (kDebugMode)
            print(
              'Error loading asset image for recipient $recipientAvatarUrl: $error',
            );
          return CircleAvatar(
            radius: FetchPixels.getPixelHeight(18),
            backgroundColor: daltiPrimaryLight,
            child: Icon(
              Icons.person,
              color: daltiPrimary,
              size: FetchPixels.getPixelHeight(20),
            ),
          );
        },
      );
    } else {
      // Fallback for null or empty senderAvatarUrl - Initials or default icon
      avatarImage = CircleAvatar(
        radius: FetchPixels.getPixelHeight(18),
        backgroundColor: daltiPrimaryLight,
        child: Text(
          widget.recipient.senderName.isNotEmpty
              ? widget.recipient.senderName[0].toUpperCase()
              : "?",
          style: TextStyle(
            fontSize: 18,
            color: daltiPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
      );
    }

    return AppBar(
      backgroundColor: daltiCard,
      elevation: 0.5,
      leading: IconButton(
        icon: Icon(Icons.arrow_back_ios_new_rounded, color: daltiIconDefault),
        onPressed: () => Navigator.of(context).pop(),
      ),
      title: Row(
        children: [
          ClipOval(child: avatarImage),
          getHorSpace(FetchPixels.getPixelWidth(10)),
          Expanded(
            child: getCustomFont(
              widget.recipient.senderName,
              17,
              daltiTextHeadline,
              1,
              fontWeight: FontWeight.w600,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
      actions: [
        IconButton(
          icon: Icon(Icons.more_horiz_rounded, color: daltiIconDefault),
          onPressed: () {
            // TODO: Implement more options functionality
          },
        ),
      ],
      centerTitle: false,
    );
  }

  Widget _buildDateHeader(DateTime date) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: FetchPixels.getPixelHeight(10)),
      alignment: Alignment.center,
      child: Text(
        DateFormat('MMMM dd, yyyy').format(date), // e.g., March 28, 2022
        style: TextStyle(
          color: daltiTextMuted,
          fontSize: 12,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget _buildMessageItem(
    MessageModel message,
    bool isCurrentUser,
    BuildContext context,
  ) {
    final alignment =
        isCurrentUser ? CrossAxisAlignment.end : CrossAxisAlignment.start;
    final bubbleColor = isCurrentUser ? daltiPrimary : daltiCard;
    final textColor = isCurrentUser ? daltiTextOnPrimary : daltiTextBody;
    final borderRadius = BorderRadius.only(
      topLeft: Radius.circular(FetchPixels.getPixelHeight(18)),
      topRight: Radius.circular(FetchPixels.getPixelHeight(18)),
      bottomLeft:
          isCurrentUser
              ? Radius.circular(FetchPixels.getPixelHeight(18))
              : Radius.circular(FetchPixels.getPixelHeight(4)),
      bottomRight:
          isCurrentUser
              ? Radius.circular(FetchPixels.getPixelHeight(4))
              : Radius.circular(FetchPixels.getPixelHeight(18)),
    );

    Widget avatarWidget = const SizedBox.shrink();
    if (!isCurrentUser) {
      final recipientAvatarUrl = widget.recipient.senderAvatarUrl;
      if (recipientAvatarUrl != null && recipientAvatarUrl.startsWith('http')) {
        avatarWidget = CircleAvatar(
          radius: FetchPixels.getPixelHeight(16),
          backgroundImage: NetworkImage(recipientAvatarUrl),
          onBackgroundImageError: (e, s) {
            if (kDebugMode)
              print("Error loading recipient avatar in message item: $e");
          },
          backgroundColor:
              daltiPrimaryLight, // Fallback background for CircleAvatar
        );
      } else if (recipientAvatarUrl != null && recipientAvatarUrl.isNotEmpty) {
        // Attempt to load as asset
        avatarWidget = CircleAvatar(
          radius: FetchPixels.getPixelHeight(16),
          backgroundImage: AssetImage(recipientAvatarUrl),
          onBackgroundImageError: (e, s) {
            if (kDebugMode)
              print("Error loading asset avatar in message item: $e");
          },
          backgroundColor: daltiPrimaryLight,
          child: Icon(
            // Fallback if asset load fails
            Icons.person,
            size: FetchPixels.getPixelHeight(18),
            color: daltiPrimary,
          ),
        );
      } else {
        // Fallback for null or empty senderAvatarUrl - Initials or default icon
        avatarWidget = CircleAvatar(
          radius: FetchPixels.getPixelHeight(16),
          backgroundColor: daltiPrimaryLight,
          child: Text(
            widget.recipient.senderName.isNotEmpty
                ? widget.recipient.senderName[0].toUpperCase()
                : "?",
            style: TextStyle(
              fontSize: 14,
              color: daltiPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
        );
      }
    }

    return Padding(
      padding: EdgeInsets.symmetric(vertical: FetchPixels.getPixelHeight(4)),
      child: Row(
        mainAxisAlignment:
            isCurrentUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          if (!isCurrentUser) ...[
            avatarWidget,
            getHorSpace(FetchPixels.getPixelWidth(8)),
          ],
          Column(
            crossAxisAlignment: alignment,
            children: [
              Container(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.7,
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: FetchPixels.getPixelWidth(14),
                  vertical: FetchPixels.getPixelHeight(10),
                ),
                decoration: BoxDecoration(
                  color: bubbleColor,
                  borderRadius: borderRadius,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.07),
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      message.content,
                      style: TextStyle(
                        color: textColor,
                        fontSize: 15,
                        fontWeight: FontWeight.w400,
                        height: 1.4,
                      ),
                    ),
                    if (!isCurrentUser) ...[
                      SizedBox(height: FetchPixels.getPixelHeight(4)),
                      Text(
                        message.sender?.name ?? 'Unknown Sender',
                        style: TextStyle(
                          color: textColor.withOpacity(0.7),
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              getVerSpace(FetchPixels.getPixelHeight(4)),
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    DateFormat('hh:mm a').format(message.createdAt),
                    style: TextStyle(
                      color: daltiTextMuted.withOpacity(0.9),
                      fontSize: 11,
                    ),
                  ),
                  if (isCurrentUser) ...[
                    getHorSpace(FetchPixels.getPixelWidth(4)),
                    Icon(
                      message.status == 'DELIVERED'
                          ? Icons.done_all
                          : Icons.done,
                      size: 14,
                      color:
                          message.status == 'READ'
                              ? daltiPrimary
                              : daltiTextMuted,
                    ),
                  ],
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMessageInputField() {
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: FetchPixels.getPixelWidth(12),
        vertical: FetchPixels.getPixelHeight(10),
      ),
      decoration: BoxDecoration(
        color: daltiCard, // Changed to daltiCard for a slight lift
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.06),
            offset: const Offset(0, -2),
            blurRadius: 8,
          ),
        ],
      ),
      child: SafeArea(
        child: Row(
          crossAxisAlignment:
              CrossAxisAlignment.center, // Align items vertically
          children: [
            // Optional: Attachment Button (simplified)
            // IconButton(
            //   icon: Icon(Icons.add_photo_alternate_outlined, color: daltiIconDefault, size: FetchPixels.getPixelHeight(26)),
            //   onPressed: () { /* TODO: Implement attachment */ },
            //   padding: EdgeInsets.only(right: FetchPixels.getPixelWidth(8)),
            //   constraints: BoxConstraints(),
            // ),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color:
                      daltiBackground, // Light background for the text field itself
                  borderRadius: BorderRadius.circular(
                    FetchPixels.getPixelHeight(24),
                  ),
                  border: Border.all(
                    color: daltiDividerLine.withOpacity(0.5),
                    width: 0.8,
                  ),
                ),
                child: Row(
                  children: [
                    IconButton(
                      icon: Icon(
                        Icons.sentiment_satisfied_alt_outlined, // Emoji icon
                        color: daltiIconActive,
                        size: FetchPixels.getPixelHeight(22),
                      ),
                      onPressed: () {
                        // TODO: Implement emoji picker
                      },
                      padding: EdgeInsets.only(
                        left: FetchPixels.getPixelWidth(8),
                        right: FetchPixels.getPixelWidth(4),
                      ),
                      constraints: const BoxConstraints(),
                    ),
                    Expanded(
                      child: TextField(
                        controller: _messageController,
                        decoration: InputDecoration(
                          hintText: "Message...",
                          hintStyle: TextStyle(
                            color: daltiTextMuted.withOpacity(0.9),
                            fontSize: 15.5,
                            fontWeight: FontWeight.w400,
                          ),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: FetchPixels.getPixelWidth(10),
                            vertical: FetchPixels.getPixelHeight(
                              12,
                            ), // Adjusted padding
                          ),
                        ),
                        style: TextStyle(
                          color: daltiTextBody,
                          fontSize: 15.5,
                          fontWeight: FontWeight.w400,
                        ),
                        maxLines: 5,
                        minLines: 1,
                        textInputAction: TextInputAction.newline,
                      ),
                    ),
                    // Attachment button moved or could be outside
                    IconButton(
                      icon: Icon(
                        Icons.attach_file_rounded, // Attachment icon
                        color: daltiIconActive,
                        size: FetchPixels.getPixelHeight(22),
                      ),
                      onPressed: () {
                        // TODO: Implement attachment options (gallery, camera, doc)
                        _showAttachmentOptions(context);
                      },
                      padding: EdgeInsets.only(
                        left: FetchPixels.getPixelWidth(4),
                        right: FetchPixels.getPixelWidth(8),
                      ),
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ),
            ),
            getHorSpace(FetchPixels.getPixelWidth(10)),
            InkWell(
              onTap: _sendMessage,
              borderRadius: BorderRadius.circular(
                FetchPixels.getPixelHeight(23),
              ),
              child: Container(
                padding: EdgeInsets.all(FetchPixels.getPixelHeight(11)),
                decoration: BoxDecoration(
                  color: daltiPrimary,
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  Icons.send_rounded,
                  color: daltiTextOnPrimary,
                  size: FetchPixels.getPixelHeight(22),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Added _showAttachmentOptions method (copied from existing code but can be refined)
  void _showAttachmentOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: daltiCard,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(FetchPixels.getPixelHeight(16)),
        ),
      ),
      builder:
          (context) => Container(
            padding: EdgeInsets.symmetric(
              vertical: FetchPixels.getPixelHeight(10),
              horizontal: FetchPixels.getPixelWidth(10),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: Icon(
                    Icons.photo_library_outlined,
                    color: daltiIconDefault,
                    size: FetchPixels.getPixelHeight(24),
                  ),
                  title: getCustomFont(
                    'Gallery',
                    16,
                    daltiTextBody,
                    1,
                    fontWeight: FontWeight.w500,
                  ),
                  onTap: () {
                    /* TODO: Implement gallery pick */
                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.camera_alt_outlined,
                    color: daltiIconDefault,
                    size: FetchPixels.getPixelHeight(24),
                  ),
                  title: getCustomFont(
                    'Camera',
                    16,
                    daltiTextBody,
                    1,
                    fontWeight: FontWeight.w500,
                  ),
                  onTap: () {
                    /* TODO: Implement camera pick */
                    Navigator.pop(context);
                  },
                ),
                ListTile(
                  leading: Icon(
                    Icons.document_scanner_outlined,
                    color: daltiIconDefault,
                    size: FetchPixels.getPixelHeight(24),
                  ),
                  title: getCustomFont(
                    'Document',
                    16,
                    daltiTextBody,
                    1,
                    fontWeight: FontWeight.w500,
                  ),
                  onTap: () {
                    /* TODO: Implement document pick */
                    Navigator.pop(context);
                  },
                ),
                getVerSpace(
                  FetchPixels.getPixelHeight(8),
                ), // For bottom padding from safe area
              ],
            ),
          ),
    );
  }
}
