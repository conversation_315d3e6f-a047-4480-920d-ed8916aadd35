import 'package:dots_indicator/dots_indicator.dart';
import 'package:dalti/app/data/data_file.dart';
import 'package:dalti/app/routes/app_routes.dart';
import 'package:dalti/base/color_data.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:flutter/material.dart';
import 'package:dalti/l10n/app_localizations.dart';

import '../../../base/constant.dart';
import '../../models/model_intro.dart';
import '../../../services/advertisement_service.dart';

class IntroScreen extends StatefulWidget {
  const IntroScreen({Key? key}) : super(key: key);

  @override
  State<IntroScreen> createState() => _IntroScreenState();
}

class _IntroScreenState extends State<IntroScreen> {
  void backClick() {
    Constant.backToPrev(context);
  }

  ValueNotifier selectedPage = ValueNotifier(0);
  final _controller = PageController();

  List<ModelIntro> _introList = [];
  bool _isLoading = true;
  bool _hasError = false;
  final AdvertisementService _advertisementService = AdvertisementService();

  @override
  void initState() {
    super.initState();
    _loadIntroContent();
  }

  Future<void> _loadIntroContent() async {
    try {
      // Start with static content
      _introList = List.from(DataFile.introList);

      // Try to fetch dynamic content from API
      final advertisements = await _advertisementService.getAdvertisements();

      // Update intro list with dynamic content (up to 3 items)
      for (int i = 0; i < advertisements.length && i < _introList.length; i++) {
        final ad = advertisements[i];
        _introList[i] = ModelIntro(
          _introList[i].id,
          _introList[i].titleKey,
          _introList[i].descriptionKey,
          _introList[i].image,
          _introList[i].color,
          dynamicTitle: ad.title,
          dynamicDescription: ad.description,
          callToActionText: ad.callToActionText,
          callToActionLink: ad.callToActionLink,
          isExternal: ad.isExternal,
        );
      }

      setState(() {
        _isLoading = false;
        _hasError = false;
      });
    } catch (e) {
      print('Error loading advertisements: $e');
      // Fall back to static content
      setState(() {
        _introList = List.from(DataFile.introList);
        _isLoading = false;
        _hasError = true;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context);

    if (localizations == null) {
      // This can happen if localizations are not properly loaded for the current locale.
      // Provide a fallback UI or log an error.
      return Scaffold(
        body: Center(child: Text('Error: Localizations not available.')),
      );
    }

    if (_isLoading) {
      return Scaffold(
        body: Center(child: CircularProgressIndicator(color: daltiPrimary)),
      );
    }

    return WillPopScope(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        body: SizedBox(
          height: double.infinity,
          width: double.infinity,
          child: PageView.builder(
            controller: _controller,
            onPageChanged: (value) {
              selectedPage.value = value;
            },
            itemCount: _introList.length,
            itemBuilder: (context, index) {
              ModelIntro _introModel = _introList[index];

              // Helper function to get localized string from a key
              String getLocalizedString(String key) {
                switch (key) {
                  case "intro1Title":
                    return localizations.intro1Title;
                  case "intro1Description":
                    return localizations.intro1Description;
                  case "intro2Title":
                    return localizations.intro2Title;
                  case "intro2Description":
                    return localizations.intro2Description;
                  case "intro3Title":
                    return localizations.intro3Title;
                  case "intro3Description":
                    return localizations.intro3Description;
                  // Add more cases if you have more dynamic keys from ModelIntro
                  default:
                    return ""; // Fallback for unknown key
                }
              }

              String title = _introModel.getTitle(getLocalizedString);
              String description = _introModel.getDescription(
                getLocalizedString,
              );

              return Stack(
                alignment: Alignment.bottomCenter,
                children: [
                  Container(
                    alignment: Alignment.topCenter,
                    color: _introModel.color,
                    child: Column(
                      children: [
                        getVerSpace(FetchPixels.getPixelHeight(55)),
                        getAssetImage(
                          _introModel.image ?? "",
                          FetchPixels.getPixelWidth(277),
                          FetchPixels.getPixelHeight(435),
                        ),
                      ],
                    ),
                  ),
                  Positioned(
                    child: Stack(
                      alignment: Alignment.bottomCenter,
                      children: [
                        Container(
                          child: getAssetImage(
                            "shape.png",
                            FetchPixels.getPixelWidth(double.infinity),
                            FetchPixels.getPixelHeight(460),
                            boxFit: BoxFit.fill,
                          ),
                        ),
                        Positioned(
                          top: FetchPixels.getPixelHeight(50),
                          width: FetchPixels.width,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              SizedBox(
                                width: FetchPixels.getPixelHeight(263),
                                child: getMultilineCustomFont(
                                  title, // Use localized title
                                  34,
                                  daltiTextHeadline,
                                  fontWeight: FontWeight.w800,
                                  textAlign: TextAlign.center,
                                  txtHeight: FetchPixels.getPixelHeight(1.3),
                                ),
                              ),
                              getVerSpace(FetchPixels.getPixelHeight(10)),
                              getPaddingWidget(
                                EdgeInsets.symmetric(
                                  horizontal: FetchPixels.getPixelWidth(20),
                                ),
                                getMultilineCustomFont(
                                  description, // Use localized description
                                  16,
                                  daltiTextBody,
                                  fontWeight: FontWeight.w400,
                                  textAlign: TextAlign.center,
                                  txtHeight: FetchPixels.getPixelHeight(1.3),
                                ),
                              ),
                              getVerSpace(FetchPixels.getPixelHeight(51)),
                              DotsIndicator(
                                dotsCount: 3,
                                position: index,
                                decorator: DotsDecorator(
                                  size: Size.square(
                                    FetchPixels.getPixelHeight(8),
                                  ),
                                  activeSize: Size.square(
                                    FetchPixels.getPixelHeight(8),
                                  ),
                                  activeColor: daltiPrimary,
                                  color: daltiPrimary.withValues(alpha: 0.3),
                                  spacing: EdgeInsets.symmetric(
                                    horizontal: FetchPixels.getPixelWidth(5),
                                  ),
                                ),
                              ),
                              getVerSpace(FetchPixels.getPixelHeight(29)),
                              getButton(
                                context,
                                daltiPrimary,
                                _introModel.callToActionText ??
                                    localizations
                                        .nextButtonText, // Use dynamic text or fallback
                                daltiTextOnPrimary,
                                () {
                                  // Handle dynamic call-to-action link
                                  if (_introModel.callToActionLink != null &&
                                      _introModel
                                          .callToActionLink!
                                          .isNotEmpty) {
                                    // TODO: Handle external links when needed
                                    // For now, continue with normal flow
                                  }

                                  if (index == _introList.length - 1) {
                                    Constant.sendToNext(
                                      context,
                                      Routes.preferredMethodRoute,
                                    );
                                  } else {
                                    _controller.animateToPage(
                                      index + 1,
                                      duration: const Duration(
                                        milliseconds: 250,
                                      ),
                                      curve: Curves.easeInSine,
                                    );
                                  }
                                },
                                18,
                                weight: FontWeight.w600,
                                buttonHeight: FetchPixels.getPixelHeight(60),
                                insetsGeometry: EdgeInsets.symmetric(
                                  horizontal: FetchPixels.getPixelWidth(20),
                                ),
                                borderRadius: BorderRadius.circular(
                                  FetchPixels.getPixelHeight(15),
                                ),
                              ),
                              getVerSpace(FetchPixels.getPixelHeight(16)),
                              index == 2
                                  ? Container()
                                  : GestureDetector(
                                    onTap: () {
                                      Constant.sendToNext(
                                        context,
                                        Routes.preferredMethodRoute,
                                      );
                                    },
                                    child: getCustomFont(
                                      localizations
                                          .skipButtonText, // Already localized
                                      19,
                                      daltiPrimary,
                                      1,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              );
            },
          ),
        ),
      ),
      onWillPop: () async {
        backClick();
        return false;
      },
    );
  }
}
