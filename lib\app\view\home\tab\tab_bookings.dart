import 'package:dalti/app/view/bookings/active_booking_screen.dart';
import 'package:dalti/app/view/bookings/all_booking_screen.dart';
import 'package:dalti/app/view/bookings/cancel_booking_screen.dart';
import 'package:dalti/app/view/bookings/complete_booking_screen.dart';
import 'package:dalti/base/color_data.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:flutter/material.dart';
import 'package:dalti/l10n/app_localizations.dart';

class TabBookings extends StatefulWidget {
  const TabBookings({Key? key}) : super(key: key);

  @override
  State<TabBookings> createState() => _TabBookingsState();
}

class _TabBookingsState extends State<TabBookings>
    with SingleTickerProviderStateMixin {
  final PageController _controller = PageController(initialPage: 0);

  late TabController tabController;
  var position = 0;

  // Define status colors
  final Map<String, Color> statusColors = {
    'All': daltiPrimary,
    'Active': daltiPrimary,
    'Completed': daltiSuccessGreen,
    'Cancelled': daltiErrorRed,
  };

  @override
  void initState() {
    tabController = TabController(length: 4, vsync: this);
    tabController.addListener(() {
      if (tabController.indexIsChanging) {
        setState(() {});
      }
    });
    setState(() {});
    super.initState();
  }

  @override
  void dispose() {
    _controller.dispose();
    tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;

    return Column(
      children: [
        getVerSpace(FetchPixels.getPixelHeight(20)),
        getPaddingWidget(
          EdgeInsets.symmetric(horizontal: FetchPixels.getPixelWidth(20)),
          withoutleftIconToolbar(
            context,
            isrightimage: true,
            title: localizations.bookingsTabTitle,
            weight: FontWeight.w800,
            textColor: Colors.black,
            fontsize: 24,
            istext: true,
            rightimage: "notification.svg",
          ),
        ),
        getVerSpace(FetchPixels.getPixelHeight(30)),
        tabbar(localizations),
        getVerSpace(FetchPixels.getPixelHeight(10)),
        pageViewer(),
      ],
    );
  }

  Expanded pageViewer() {
    return Expanded(
      child: PageView(
        physics: const BouncingScrollPhysics(),
        controller: _controller,
        scrollDirection: Axis.horizontal,
        children: const [
          AllBookingScreen(),
          ActiveBookingScreen(),
          CompleteBookingScreen(),
          CancelBookingScreen(),
        ],
        onPageChanged: (value) {
          tabController.animateTo(value);
          position = value;
          setState(() {});
        },
      ),
    );
  }

  Widget tabbar(AppLocalizations localizations) {
    final tabNames = [
      localizations.bookingsTabAll,
      localizations.bookingsTabActive,
      localizations.bookingsTabCompleted,
      localizations.bookingsTabCancelled,
    ];

    final List<String> statusColorKeys = [
      'All',
      'Active',
      'Completed',
      'Cancelled',
    ];
    final currentTabColor =
        statusColors[statusColorKeys[tabController.index]] ?? appPrimary;

    return getPaddingWidget(
      EdgeInsets.symmetric(horizontal: FetchPixels.getPixelWidth(20)),
      TabBar(
        controller: tabController,
        onTap: (index) {
          _controller.jumpToPage(index);
        },
        labelStyle: const TextStyle(fontWeight: FontWeight.w600, fontSize: 14),
        unselectedLabelStyle: const TextStyle(
          fontWeight: FontWeight.w400,
          fontSize: 14,
        ),
        labelColor: currentTabColor,
        unselectedLabelColor: appTextBody,
        indicatorColor: currentTabColor,
        indicatorWeight: 2,
        indicatorSize: TabBarIndicatorSize.tab,
        padding: EdgeInsets.zero,
        labelPadding: EdgeInsets.symmetric(
          horizontal: FetchPixels.getPixelWidth(4),
          vertical: FetchPixels.getPixelHeight(8),
        ),
        indicator: UnderlineTabIndicator(
          borderSide: BorderSide(width: 2.0, color: currentTabColor),
        ),
        tabs: [
          Tab(text: localizations.bookingsTabAll),
          Tab(text: localizations.bookingsTabActive),
          Tab(text: localizations.bookingsTabCompleted),
          Tab(text: localizations.bookingsTabCancelled),
        ],
      ),
    );
  }
}
