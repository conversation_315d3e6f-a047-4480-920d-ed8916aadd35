import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';

class PasswordResetService {
  static const String _baseUrl = "https://dapi-test.adscloud.org:8443";

  /// Request password reset OTP
  /// Returns success message or throws exception with error details
  static Future<String> requestPasswordResetOtp(String email) async {
    const String endpoint = "/api/auth/request-password-reset-otp";
    final Uri uri = Uri.parse("$_baseUrl$endpoint");

    try {
      final response = await http.post(
        uri,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'email': email}),
      );

      final Map<String, dynamic>? responseData = 
          response.body.isNotEmpty ? jsonDecode(response.body) : null;

      if (response.statusCode == 200) {
        return responseData?['message'] ?? 
            "Password reset OTP sent successfully to your email address.";
      } else {
        // Handle specific error cases
        String errorMessage = responseData?['message'] ?? 
            "Failed to send password reset OTP.";
        
        switch (response.statusCode) {
          case 404:
            throw PasswordResetException(
              errorMessage, 
              PasswordResetErrorType.userNotFound
            );
          case 400:
            // Could be either no password login or invalid request
            if (errorMessage.contains("password login enabled")) {
              throw PasswordResetException(
                errorMessage, 
                PasswordResetErrorType.passwordLoginDisabled
              );
            } else {
              throw PasswordResetException(
                errorMessage, 
                PasswordResetErrorType.invalidRequest
              );
            }
          case 429:
            throw PasswordResetException(
              errorMessage, 
              PasswordResetErrorType.rateLimited
            );
          default:
            throw PasswordResetException(
              errorMessage, 
              PasswordResetErrorType.serverError
            );
        }
      }
    } catch (e) {
      if (e is PasswordResetException) {
        rethrow;
      }
      if (kDebugMode) {
        print("Network error in requestPasswordResetOtp: $e");
      }
      throw PasswordResetException(
        "Network error. Please check your connection and try again.",
        PasswordResetErrorType.networkError
      );
    }
  }

  /// Verify password reset OTP
  /// Returns reset token or throws exception with error details
  static Future<String> verifyPasswordResetOtp(String email, String otp) async {
    const String endpoint = "/api/auth/verify-password-reset-otp";
    final Uri uri = Uri.parse("$_baseUrl$endpoint");

    try {
      final response = await http.post(
        uri,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'email': email, 'otp': otp}),
      );

      final Map<String, dynamic>? responseData = 
          response.body.isNotEmpty ? jsonDecode(response.body) : null;

      if (response.statusCode == 200) {
        String resetToken = responseData?['resetToken'] ?? '';
        if (resetToken.isEmpty) {
          throw PasswordResetException(
            "Invalid response from server. Reset token not received.",
            PasswordResetErrorType.serverError
          );
        }
        return resetToken;
      } else {
        String errorMessage = responseData?['message'] ?? 
            "Failed to verify OTP.";
        
        switch (response.statusCode) {
          case 400:
            if (errorMessage.contains("expired")) {
              throw PasswordResetException(
                errorMessage, 
                PasswordResetErrorType.otpExpired
              );
            } else {
              throw PasswordResetException(
                errorMessage, 
                PasswordResetErrorType.invalidOtp
              );
            }
          case 404:
            throw PasswordResetException(
              errorMessage, 
              PasswordResetErrorType.userNotFound
            );
          default:
            throw PasswordResetException(
              errorMessage, 
              PasswordResetErrorType.serverError
            );
        }
      }
    } catch (e) {
      if (e is PasswordResetException) {
        rethrow;
      }
      if (kDebugMode) {
        print("Network error in verifyPasswordResetOtp: $e");
      }
      throw PasswordResetException(
        "Network error. Please check your connection and try again.",
        PasswordResetErrorType.networkError
      );
    }
  }

  /// Reset password using reset token
  /// Returns success message or throws exception with error details
  static Future<String> resetPassword(String resetToken, String newPassword) async {
    const String endpoint = "/api/auth/reset-password";
    final Uri uri = Uri.parse("$_baseUrl$endpoint");

    try {
      final response = await http.post(
        uri,
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({'resetToken': resetToken, 'newPassword': newPassword}),
      );

      final Map<String, dynamic>? responseData = 
          response.body.isNotEmpty ? jsonDecode(response.body) : null;

      if (response.statusCode == 200) {
        return responseData?['message'] ?? 
            "Password reset successfully. You can now log in with your new password.";
      } else {
        String errorMessage = responseData?['message'] ?? 
            "Failed to reset password.";
        
        switch (response.statusCode) {
          case 400:
            if (errorMessage.contains("expired")) {
              throw PasswordResetException(
                errorMessage, 
                PasswordResetErrorType.tokenExpired
              );
            } else {
              throw PasswordResetException(
                errorMessage, 
                PasswordResetErrorType.invalidToken
              );
            }
          case 500:
            throw PasswordResetException(
              errorMessage, 
              PasswordResetErrorType.serverError
            );
          default:
            throw PasswordResetException(
              errorMessage, 
              PasswordResetErrorType.serverError
            );
        }
      }
    } catch (e) {
      if (e is PasswordResetException) {
        rethrow;
      }
      if (kDebugMode) {
        print("Network error in resetPassword: $e");
      }
      throw PasswordResetException(
        "Network error. Please check your connection and try again.",
        PasswordResetErrorType.networkError
      );
    }
  }
}

/// Custom exception for password reset operations
class PasswordResetException implements Exception {
  final String message;
  final PasswordResetErrorType type;

  PasswordResetException(this.message, this.type);

  @override
  String toString() => message;
}

/// Error types for password reset operations
enum PasswordResetErrorType {
  userNotFound,
  passwordLoginDisabled,
  rateLimited,
  invalidOtp,
  otpExpired,
  invalidToken,
  tokenExpired,
  invalidRequest,
  serverError,
  networkError,
}
