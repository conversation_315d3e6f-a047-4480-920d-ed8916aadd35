import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:flutter/material.dart';
import 'package:dalti/services/user_service.dart';
import 'package:dalti/l10n/app_localizations.dart';

import '../../../base/color_data.dart';
import '../../../base/constant.dart';
import '../../../base/widget_utils.dart';
import 'package:shared_preferences/shared_preferences.dart';

class EditProfileScreen extends StatefulWidget {
  const EditProfileScreen({Key? key}) : super(key: key);

  @override
  State<EditProfileScreen> createState() => _EditProfileScreenState();
}

class _EditProfileScreenState extends State<EditProfileScreen> {
  TextEditingController nameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController phoneController = TextEditingController();

  final UserService _userService = UserService();
  String? _selectedLanguage;
  final List<Map<String, String>> _supportedLanguages = [
    {'code': 'EN', 'name': 'English'},
    {'code': 'AR', 'name': 'Arabic'},
    {'code': 'FR', 'name': 'French'},
  ];

  @override
  void initState() {
    super.initState();
    // _selectedLanguage = _supportedLanguages.first['code'];
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    await Future.delayed(const Duration(seconds: 3));
    final prefs = await SharedPreferences.getInstance();
    String? languageCode = prefs.getString('language_code')?.toUpperCase();
    // String? countryCode = prefs.getString('country_code');

    print(_supportedLanguages);
    print('DEBUG: languageCode: $languageCode');
    // print('DEBUG: countryCode: $countryCode');

    // if (languageCode == null) {
    //   // No language saved, determine from OS/Platform
    //   String osLocaleName = getPlatformLocaleName(); // Use the utility function
    //   print('DEBUG: osLocaleName: $osLocaleName');
    //   String osLang =
    //       osLocaleName
    //           .split('_')
    //           .first
    //           .split('-')
    //           .first; // "en", "ar", etc. Handles "en-US" and "en_US"

    //   if (['en', 'ar', 'fr'].contains(osLang)) {
    //     languageCode = osLang;
    //   } else {
    //     languageCode = 'en'; // Default to English
    //   }
    //   // countryCode = null; // Or determine based on osLocaleName if needed

    //   // Save the determined locale
    //   await prefs.setString('language_code', languageCode);
    // }

    if (mounted) {
      _selectedLanguage = languageCode;
      setState(() {
        _selectedLanguage = languageCode;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    nameController.text = "Alena Gomez";
    emailController.text = "<EMAIL>";
    phoneController.text = "(*************";
    FetchPixels(context);
    return WillPopScope(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: daltiBackground,
        bottomNavigationBar: saveButton(context),
        body: SafeArea(
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: FetchPixels.getPixelWidth(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                getVerSpace(FetchPixels.getPixelHeight(20)),
                gettoolbarMenu(
                  context,
                  "back.svg",
                  () {
                    Constant.backToPrev(context);
                  },
                  istext: true,
                  title: "Edit Profile",
                  weight: FontWeight.w800,
                  fontsize: 24,
                  textColor: daltiTextHeadline,
                ),
                getVerSpace(FetchPixels.getPixelHeight(30)),
                profilePictureEdit(context),
                getVerSpace(FetchPixels.getPixelHeight(40)),
                getDefaultTextFiledWithLabel(
                  context,
                  "Name",
                  nameController,
                  daltiTextMuted,
                  function: () {},
                  height: FetchPixels.getPixelHeight(60),
                  isEnable: false,
                  withprefix: true,
                  image: "profile.svg",
                  imageWidth: FetchPixels.getPixelHeight(24),
                  imageHeight: FetchPixels.getPixelHeight(24),
                ),
                getVerSpace(FetchPixels.getPixelHeight(20)),
                getDefaultTextFiledWithLabel(
                  context,
                  "Email",
                  emailController,
                  daltiTextMuted,
                  function: () {},
                  height: FetchPixels.getPixelHeight(60),
                  isEnable: false,
                  withprefix: true,
                  image: "message.svg",
                  imageWidth: FetchPixels.getPixelHeight(24),
                  imageHeight: FetchPixels.getPixelHeight(24),
                ),
                getVerSpace(FetchPixels.getPixelHeight(20)),
                getDefaultTextFiledWithLabel(
                  context,
                  "Phone",
                  phoneController,
                  daltiTextMuted,
                  function: () {},
                  height: FetchPixels.getPixelHeight(60),
                  isEnable: false,
                  withprefix: true,
                  image: "call.svg",
                  imageWidth: FetchPixels.getPixelHeight(24),
                  imageHeight: FetchPixels.getPixelHeight(24),
                ),
                getVerSpace(FetchPixels.getPixelHeight(20)),
                _buildLanguageDropdown(context),
              ],
            ),
          ),
        ),
      ),
      onWillPop: () async {
        Constant.backToPrev(context);
        return false;
      },
    );
  }

  Container saveButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: daltiCard,
        boxShadow: [
          BoxShadow(
            color: daltiDividerLine.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: Offset(0, -2),
          ),
        ],
      ),
      padding: EdgeInsets.only(
        left: FetchPixels.getPixelWidth(20),
        right: FetchPixels.getPixelWidth(20),
        bottom: FetchPixels.getPixelHeight(30),
        top: FetchPixels.getPixelHeight(15),
      ),
      child: getButton(
        context,
        daltiPrimary,
        "Save",
        daltiTextOnPrimary,
        () async {
          if (_selectedLanguage == null) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('Please select a language.')),
            );
            return;
          }
          try {
            bool success = await _userService.updatePreferredLanguage(
              _selectedLanguage!,
            );
            if (success) {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Preferred language updated successfully!'),
                ),
              );
              Constant.backToPrev(context);
            } else {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Failed to update preferred language.'),
                ),
              );
            }
          } catch (e) {
            ScaffoldMessenger.of(
              context,
            ).showSnackBar(SnackBar(content: Text('Error: ${e.toString()}')));
          }
        },
        18,
        weight: FontWeight.w600,
        buttonHeight: FetchPixels.getPixelHeight(60),
        borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(14)),
      ),
    );
  }

  Widget _buildLanguageDropdown(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    Map<String, String> languageNames = {
      'EN': localizations.english,
      'AR': localizations.arabic,
      'FR': localizations.french,
    };

    print('[DEBUG-EDIT-PROFILE] languageNames: $languageNames');

    return DropdownButtonFormField<String>(
      decoration: InputDecoration(
        labelText: localizations.chooseLanguage,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
          borderSide: BorderSide(color: daltiDividerLine.withOpacity(0.3)),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
          borderSide: BorderSide(color: daltiDividerLine.withOpacity(0.3)),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
          borderSide: BorderSide(color: daltiPrimary, width: 1.5),
        ),
        filled: true,
        labelStyle: TextStyle(
          color: daltiTextMuted,
          fontSize: FetchPixels.getPixelHeight(14),
          fontWeight: FontWeight.w400,
        ),
        fillColor: Colors.white,
        contentPadding: EdgeInsets.symmetric(
          horizontal: FetchPixels.getPixelWidth(16),
          vertical: FetchPixels.getPixelHeight(18),
        ),
      ),
      value: _selectedLanguage,
      hint: Text(localizations.chooseLanguage),
      isExpanded: true,
      icon: Icon(Icons.arrow_drop_down, color: daltiPrimary),
      items:
          _supportedLanguages.map((language) {
            return DropdownMenuItem<String>(
              value: language['code'],
              child: Text(
                languageNames[language['code']] ?? language['name']!,
                style: TextStyle(
                  color: daltiTextMuted,
                  fontSize: FetchPixels.getPixelHeight(14),
                  fontWeight: FontWeight.w400,
                ),
              ),
            );
          }).toList(),
      onChanged: (String? newValue) {
        print('[DEBUG-EDIT-PROFILE] Selected language: $newValue');
        setState(() {
          _selectedLanguage = newValue;
        });
      },
      validator: (value) => value == null ? 'Please select a language' : null,
    );
  }

  Align profilePictureEdit(BuildContext context) {
    return Align(
      alignment: Alignment.topCenter,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          Container(
            height: FetchPixels.getPixelHeight(100),
            width: FetchPixels.getPixelHeight(100),
            decoration: BoxDecoration(
              image: getDecorationAssetImage(context, "profile_picture.png"),
            ),
          ),
          Positioned(
            top: FetchPixels.getPixelHeight(68),
            left: FetchPixels.getPixelHeight(70),
            child: Container(
              height: FetchPixels.getPixelHeight(46),
              width: FetchPixels.getPixelHeight(46),
              padding: EdgeInsets.symmetric(
                vertical: FetchPixels.getPixelHeight(10),
                horizontal: FetchPixels.getPixelHeight(10),
              ),
              decoration: BoxDecoration(
                color: daltiCard,
                boxShadow: [
                  BoxShadow(
                    color: daltiDividerLine.withOpacity(0.4),
                    blurRadius: 8,
                    offset: Offset(0.0, 3.0),
                  ),
                ],
                borderRadius: BorderRadius.circular(
                  FetchPixels.getPixelHeight(35),
                ),
              ),
              child: getSvgImage(
                "edit.svg",
                color: daltiPrimary,
                height: FetchPixels.getPixelHeight(24),
                width: FetchPixels.getPixelHeight(24),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
