// ignore_for_file: prefer_const_constructors

import 'dart:convert';
import 'package:dalti/app/models/model_appointment.dart';
import 'package:dalti/app/models/model_category.dart';
import 'package:dalti/app/routes/app_routes.dart';
import 'package:dalti/base/color_data.dart';
import 'package:dalti/base/constant.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';

const String prefsKeySessionId = 'session_id';

class AllBookingScreen extends StatefulWidget {
  const AllBookingScreen({Key? key}) : super(key: key);

  @override
  State<AllBookingScreen> createState() => _AllBookingScreenState();
}

class _AllBookingScreenState extends State<AllBookingScreen> {
  List<ModelAppointment> _appointments = [];
  bool _isLoading = true;
  String? _error;

  final DateFormat _dateFormat = DateFormat('dd MMMM, yyyy, hh:mm a');

  @override
  void initState() {
    super.initState();
    _fetchAppointments();
  }

  Future<void> _fetchAppointments() async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
      _error = null;
    });

    final prefs = await SharedPreferences.getInstance();
    final sessionId = prefs.getString(prefsKeySessionId);

    if (sessionId == null || sessionId.isEmpty) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _error = "Please log in to view your bookings.";
        });
      }
      return;
    }

    const String apiUrl =
        "https://dapi-test.adscloud.org:8443/api/auth/customer/appointments";

    try {
      final response = await http.get(
        Uri.parse(apiUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $sessionId',
        },
      );

      if (!mounted) return;

      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);
        setState(() {
          _appointments =
              responseData
                  .map(
                    (data) =>
                        ModelAppointment.fromJson(data as Map<String, dynamic>),
                  )
                  .toList();
          _isLoading = false;
        });
      } else {
        String errorMessage = "Failed to fetch appointments.";
        try {
          final errorData = jsonDecode(response.body);
          errorMessage = errorData['message'] as String? ?? errorMessage;
        } catch (_) {}
        setState(() {
          _isLoading = false;
          _error = errorMessage;
        });
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _error = "An error occurred: $e";
      });
    }
  }

  Color _getStatusColor(String? status) {
    status = status?.toLowerCase();
    switch (status) {
      case 'pending':
        return daltiWarningYellow.withOpacity(0.15);
      case 'confirmed': 
        return daltiPrimary.withOpacity(0.15);
      case 'in_progress':
      case 'inprogress':
        return appPrimary.withOpacity(0.15);
      case 'completed':
        return daltiSuccessGreen.withOpacity(0.15);
      case 'cancelled':
      case 'canceled':
      case 'noshow': 
        return daltiErrorRed.withOpacity(0.15);
      default:
        print('Unknown status: $status');
        return Colors.grey.shade200;
    }
  }

  String _getStatusText(String? status) {
    status = status?.toLowerCase();
    switch (status) {
      case 'pending':
        return "Pending";
      case 'confirmed':
        return "Active";
      case 'in_progress':
      case 'inprogress':
        return "In Progress";
      case 'completed':
        return "Completed";
      case 'cancelled':
      case 'canceled':
        return "Cancelled";
      case 'noshow':
        return "No Show";
      default:
        print('Unknown status text: $status');
        return status?.capitalize() ?? "Unknown";
    }
  }

  Color _getStatusTextColor(String? status) {
    status = status?.toLowerCase();
    switch (status) {
      case 'pending':
        return daltiWarningYellow;
      case 'confirmed':
        return daltiPrimary;
      case 'in_progress':
      case 'inprogress':
        return appPrimary;
      case 'completed':
        return daltiSuccessGreen;
      case 'cancelled':
      case 'canceled':
      case 'noshow':
        return daltiErrorRed;
      default:
        print('Unknown status text color: $status');
        return Colors.grey.shade700;
    }
  }

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    if (_isLoading) {
      return Center(child: CircularProgressIndicator(color: appPrimary));
    }

    if (_error != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  _error!,
                  style: TextStyle(color: appPrimary, fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                getVerSpace(FetchPixels.getPixelHeight(20)),
                ElevatedButton(
                  onPressed: _fetchAppointments,
                  child: Text("Retry"),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: appPrimary,
                    foregroundColor: appBackground,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Container(
      color: appBackground,
      child:
          _appointments.isEmpty
              ? _buildEmptyBookingsView(context)
              : _buildAllAppointmentListView(),
    );
  }

  ListView _buildAllAppointmentListView() {
    return ListView.builder(
      physics: const BouncingScrollPhysics(),
      padding: EdgeInsets.symmetric(
        horizontal: FetchPixels.getPixelWidth(20),
        vertical: FetchPixels.getPixelHeight(10),
      ),
      itemCount: _appointments.length,
      itemBuilder: (context, index) {
        ModelAppointment appointment = _appointments[index];
        final statusColor = _getStatusColor(appointment.status);
        final statusTextColor = _getStatusTextColor(appointment.status);
        
        return GestureDetector(
          onTap: () {
            Constant.sendToNext(
              context,
              Routes.bookingRoute,
              arguments: appointment,
            );
          },
          child: Container(
            margin: EdgeInsets.only(bottom: FetchPixels.getPixelHeight(20)),
            decoration: BoxDecoration(
              color: appCardBackground,
              borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
              border: Border.all(color: Colors.grey.shade100),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                // Status indicator at the top
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(
                    vertical: FetchPixels.getPixelHeight(8),
                    horizontal: FetchPixels.getPixelWidth(16),
                  ),
                  decoration: BoxDecoration(
                    color: statusColor,
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(FetchPixels.getPixelHeight(12)),
                      topRight: Radius.circular(FetchPixels.getPixelHeight(12)),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: statusTextColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                      getHorSpace(FetchPixels.getPixelWidth(8)),
                      Text(
                        _getStatusText(appointment.status),
                        style: TextStyle(
                          color: statusTextColor,
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                // Main content
                Padding(
                  padding: EdgeInsets.all(FetchPixels.getPixelHeight(16)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Service image or placeholder
                          Container(
                            height: FetchPixels.getPixelHeight(80),
                            width: FetchPixels.getPixelHeight(80),
                            decoration: BoxDecoration(
                              color: statusColor,
                              borderRadius: BorderRadius.circular(
                                FetchPixels.getPixelHeight(10),
                              ),
                            ),
                            child: Icon(
                              Icons.category_outlined,
                              size: 32,
                              color: statusTextColor,
                            ),
                          ),
                          getHorSpace(FetchPixels.getPixelWidth(16)),
                          // Service details
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                getCustomFont(
                                  appointment.customerFolder.provider.category.title,
                                  16,
                                  appDarkText,
                                  1,
                                  fontWeight: FontWeight.w700,
                                ),
                                getVerSpace(FetchPixels.getPixelHeight(4)),
                                getCustomFont(
                                  appointment.service.title,
                                  14,
                                  appMediumText,
                                  2,
                                  fontWeight: FontWeight.w500,
                                ),
                                getVerSpace(FetchPixels.getPixelHeight(8)),
                                Row(
                                  children: [
                                    Icon(
                                      Icons.access_time_rounded,
                                      size: 16,
                                      color: appMediumText,
                                    ),
                                    getHorSpace(FetchPixels.getPixelWidth(6)),
                                    Expanded(
                                      child: getCustomFont(
                                        _dateFormat.format(
                                          appointment.expectedAppointmentStartTime.toLocal(),
                                        ),
                                        13,
                                        appMediumText,
                                        1,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      getVerSpace(FetchPixels.getPixelHeight(16)),
                      // Provider info and actions
                      Row(
                        children: [
                          Expanded(
                            child: Row(
                              children: [
                                Icon(
                                  Icons.person_outline_rounded,
                                  size: 16,
                                  color: appMediumText,
                                ),
                                getHorSpace(FetchPixels.getPixelWidth(6)),
                                Expanded(
                                  child: getCustomFont(
                                    "By ${appointment.customerFolder.provider.title}",
                                    13,
                                    appMediumText,
                                    1,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Row(
                            children: [
                              if (appointment.status.toLowerCase() == 'pending' ||
                                  appointment.status.toLowerCase() == 'confirmed')
                                _buildActionButton(
                                  icon: Icons.message_outlined,
                                  onTap: () {
                                    // Implement chat functionality
                                  },
                                ),
                              if (appointment.status.toLowerCase() == 'pending')
                                Row(
                                  children: [
                                    getHorSpace(FetchPixels.getPixelWidth(12)),
                                    _buildActionButton(
                                      icon: Icons.delete_outline_rounded,
                                      onTap: () {
                                        // Implement delete functionality
                                      },
                                      color: daltiErrorRed,
                                    ),
                                  ],
                                ),
                            ],
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onTap,
    Color? color,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.all(FetchPixels.getPixelHeight(8)),
        decoration: BoxDecoration(
          color: (color ?? appMediumText).withOpacity(0.1),
          borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(8)),
        ),
        child: Icon(
          icon,
          size: 20,
          color: color ?? appMediumText,
        ),
      ),
    );
  }

  Widget _buildEmptyBookingsView(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        getSvgImage(
          "clipboard.svg",
          height: FetchPixels.getPixelHeight(124),
          width: FetchPixels.getPixelHeight(124),
        ),
        getVerSpace(FetchPixels.getPixelHeight(40)),
        getCustomFont(
          "No Bookings Yet!",
          20,
          appTextHeadline,
          1,
          fontWeight: FontWeight.w800,
        ),
        getVerSpace(FetchPixels.getPixelHeight(10)),
        getCustomFont(
          "Go to services and book the best services.",
          16,
          appTextBody,
          1,
          fontWeight: FontWeight.w400,
        ),
        getVerSpace(FetchPixels.getPixelHeight(30)),
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: FetchPixels.getPixelWidth(60),
          ),
          child: getButton(
            context,
            appPrimary,
            "Explore Services",
            appBackground,
            () async {
              final selectedCategory = await Navigator.pushNamed(
                context,
                Routes.categoryRoute,
              );
              if (selectedCategory is ModelCategory) {
                Constant.sendToNext(
                  context,
                  Routes.searchRoute,
                  arguments: {'category': selectedCategory},
                );
              }
            },
            16,
            weight: FontWeight.w600,
            buttonHeight: FetchPixels.getPixelHeight(50),
            borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(25)),
            insetsGeometrypadding: EdgeInsets.symmetric(
              vertical: FetchPixels.getPixelHeight(10),
              horizontal: FetchPixels.getPixelWidth(20),
            ),
          ),
        ),
      ],
    );
  }
}

extension StringExtension on String {
  String capitalize() {
    if (this.isEmpty) return "";
    return "${this[0].toUpperCase()}${this.substring(1).toLowerCase()}";
  }
}
