import 'package:flutter/material.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:dalti/base/color_data.dart';
import 'package:intl/intl.dart';

class SwapConfirmationDialog extends StatelessWidget {
  final String currentPosition;
  final String targetPosition;
  final DateTime currentAppointmentTime;
  final DateTime targetAppointmentTime;
  final String targetUserName;
  final VoidCallback onConfirm;
  final VoidCallback onCancel;

  const SwapConfirmationDialog({
    Key? key,
    required this.currentPosition,
    required this.targetPosition,
    required this.currentAppointmentTime,
    required this.targetAppointmentTime,
    required this.targetUserName,
    required this.onConfirm,
    required this.onCancel,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final timeFormat = DateFormat('hh:mm a');

    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(20)),
      ),
      elevation: 0,
      backgroundColor: Colors.transparent,
      child: Container(
        padding: EdgeInsets.all(FetchPixels.getPixelHeight(20)),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(20)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.1),
              spreadRadius: 2,
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with icon
            Icon(
              Icons.swap_horiz_rounded,
              size: FetchPixels.getPixelHeight(40),
              color: daltiPrimary,
            ),
            getVerSpace(FetchPixels.getPixelHeight(15)),

            // Title
            getCustomFont(
              "Confirm Queue Swap",
              20,
              Colors.black,
              1,
              fontWeight: FontWeight.bold,
            ),
            getVerSpace(FetchPixels.getPixelHeight(20)),

            // Swap details
            Container(
              padding: EdgeInsets.all(FetchPixels.getPixelHeight(15)),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(
                  FetchPixels.getPixelHeight(12),
                ),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Column(
                children: [
                  _buildSwapDetail(
                    "Your Position",
                    "#$currentPosition at ${timeFormat.format(currentAppointmentTime)}",
                    Icons.person_outline_rounded,
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(15)),
                  Icon(
                    Icons.swap_vert_rounded,
                    color: daltiPrimary,
                    size: FetchPixels.getPixelHeight(24),
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(15)),
                  _buildSwapDetail(
                    targetUserName,
                    "#$targetPosition at ${timeFormat.format(targetAppointmentTime)}",
                    Icons.person_outline_rounded,
                  ),
                ],
              ),
            ),
            getVerSpace(FetchPixels.getPixelHeight(25)),

            // Warning text
            Padding(
              padding: EdgeInsets.symmetric(
                horizontal: FetchPixels.getPixelWidth(10),
              ),
              child: Text(
                "Once requested, the swap will need to be approved by $targetUserName.",
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey.shade600,
                  fontSize: FetchPixels.getPixelHeight(14),
                ),
              ),
            ),
            getVerSpace(FetchPixels.getPixelHeight(25)),

            // Action buttons
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: TextButton(
                    onPressed: onCancel,
                    style: TextButton.styleFrom(
                      padding: EdgeInsets.symmetric(
                        vertical: FetchPixels.getPixelHeight(12),
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          FetchPixels.getPixelHeight(10),
                        ),
                      ),
                    ),
                    child: getCustomFont(
                      "Cancel",
                      16,
                      Colors.grey.shade700,
                      1,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                getHorSpace(FetchPixels.getPixelWidth(15)),
                Expanded(
                  child: ElevatedButton(
                    onPressed: onConfirm,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: daltiPrimary,
                      padding: EdgeInsets.symmetric(
                        vertical: FetchPixels.getPixelHeight(12),
                      ),
                      elevation: 0,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          FetchPixels.getPixelHeight(10),
                        ),
                      ),
                    ),
                    child: getCustomFont(
                      "Request Swap",
                      16,
                      Colors.white,
                      1,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSwapDetail(String title, String detail, IconData icon) {
    return Row(
      children: [
        Container(
          padding: EdgeInsets.all(FetchPixels.getPixelHeight(8)),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(8)),
            border: Border.all(color: Colors.grey.shade300),
          ),
          child: Icon(
            icon,
            size: FetchPixels.getPixelHeight(20),
            color: daltiPrimary,
          ),
        ),
        getHorSpace(FetchPixels.getPixelWidth(12)),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              getCustomFont(
                title,
                14,
                Colors.grey.shade600,
                1,
                fontWeight: FontWeight.w500,
              ),
              getVerSpace(FetchPixels.getPixelHeight(4)),
              getCustomFont(
                detail,
                16,
                Colors.black,
                1,
                fontWeight: FontWeight.w600,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
