// Defines the data models related to the booking process.

// Represents a service offered by a provider.
class ModelService {
  final int id;
  final String name;
  final int duration; // Added duration
  final double pointsRequirements; // Added pointsRequirements

  ModelService({
    required this.id,
    required this.name,
    required this.duration,
    required this.pointsRequirements, // Added pointsRequirements
  }); // Added duration
  // Add any other relevant service details, e.g., description, price if not covered by 'fees' in ModelDoctor

  // Optional: Factory constructor for JSON parsing if you fetch services separately
  factory ModelService.fromJson(Map<String, dynamic> json) {
    return ModelService(
      id: json['id'] as int,
      name:
          json['title'] as String? ??
          'Unnamed Service', // Assuming 'title' for name from API
      duration:
          json['duration'] as int? ?? 30, // Added duration parsing with default
      pointsRequirements:
          (json['pointsRequirements'] as num?)?.toDouble() ??
          0.0, // Added parsing
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ModelService &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

// Represents a queue associated with a service or providing place.
class ModelQueue {
  final int id;
  final String name;
  final List<int> serviceIds; // Added to store associated service IDs

  ModelQueue({required this.id, required this.name, required this.serviceIds});

  factory ModelQueue.fromJson(Map<String, dynamic> json) {
    List<int> sIds = [];
    if (json['services'] != null && json['services'] is List) {
      for (var serviceItem in (json['services'] as List)) {
        if (serviceItem is Map<String, dynamic> && serviceItem['id'] is int) {
          sIds.add(serviceItem['id'] as int);
        }
      }
    }
    return ModelQueue(
      id: json['id'] as int,
      name:
          json['title'] as String? ??
          json['name'] as String? ??
          'Unnamed Queue', // API uses 'title' for queue name
      serviceIds: sIds,
    );
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ModelQueue && runtimeType == other.runtimeType && id == other.id;

  @override
  int get hashCode => id.hashCode;
}

// Represents a single available time slot from the API.
class AvailabilitySlot {
  final DateTime startTime; // Parsed from ISO 8601 string
  final int queueId;
  final bool isBooked;

  AvailabilitySlot({
    required this.startTime,
    required this.queueId,
    required this.isBooked,
  });

  factory AvailabilitySlot.fromJson(Map<String, dynamic> json) {
    return AvailabilitySlot(
      startTime: DateTime.parse(json['startTime'] as String),
      queueId: json['queueId'] as int,
      isBooked: json['isBooked'] as bool,
    );
  }
}

// Represents the availability for a single day from the API.
class DailyAvailabilityResult {
  final DateTime date; // Parsed from YYYY-MM-DD string
  final List<AvailabilitySlot> slots;

  DailyAvailabilityResult({required this.date, required this.slots});

  factory DailyAvailabilityResult.fromJson(Map<String, dynamic> json) {
    var slotList = <AvailabilitySlot>[];
    if (json['slots'] != null) {
      slotList =
          (json['slots'] as List)
              .map((s) => AvailabilitySlot.fromJson(s as Map<String, dynamic>))
              .toList();
    }
    return DailyAvailabilityResult(
      date: DateTime.parse(json['date'] as String),
      slots: slotList,
    );
  }
}
