import './model_detailed_address.dart';

class ModelPlace {
  final int? id;
  final String? name;
  final String? address; // This might be legacy or a simple address string
  final String? city; // This might be legacy
  final String? timezone;
  final ModelDetailedAddress? detailedAddress;

  ModelPlace({
    this.id,
    this.name,
    this.address,
    this.city,
    this.timezone,
    this.detailedAddress,
  });

  factory ModelPlace.fromJson(Map<String, dynamic> json) {
    return ModelPlace(
      id: json['id'] as int?,
      name: json['name'] as String?,
      address: json['address'] as String?,
      city: json['city'] as String?,
      timezone: json['timezone'] as String?,
      detailedAddress:
          json['detailedAddress'] == null
              ? null
              : ModelDetailedAddress.fromJson(
                json['detailedAddress'] as Map<String, dynamic>,
              ),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'city': city,
      'timezone': timezone,
      'detailedAddress': detailedAddress?.to<PERSON><PERSON>(),
    };
  }
}
