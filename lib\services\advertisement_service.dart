import 'dart:convert';
import 'package:http/http.dart' as http;

class AdvertisementService {
  final String _baseUrl = "https://dapi-test.adscloud.org:8443";

  // Fetch advertisements from the public API (no authentication required)
  Future<List<Advertisement>> getAdvertisements() async {
    print(
      'DEBUG: Fetching advertisements from: $_baseUrl/api/auth/public/advertisements',
    );

    final response = await http.get(
      Uri.parse('$_baseUrl/api/auth/public/advertisements'),
      headers: {'Content-Type': 'application/json'},
    );

    print('DEBUG: Response status: ${response.statusCode}');
    print('DEBUG: Response body: ${response.body}');

    if (response.statusCode == 200) {
      final Map<String, dynamic> responseData = jsonDecode(response.body);
      if (responseData['success'] == true && responseData['data'] != null) {
        final List<dynamic> advertisements =
            responseData['data']['advertisements'];
        return advertisements.map((ad) => Advertisement.fromJson(ad)).toList();
      } else {
        throw Exception('Invalid response format');
      }
    } else {
      throw Exception(
        'Failed to fetch advertisements. Status: ${response.statusCode}',
      );
    }
  }
}

class Advertisement {
  final int id;
  final String title;
  final String subtitle;
  final String description;
  final String callToActionText;
  final String? callToActionLink;
  final bool isExternal;
  final bool isActive;
  final int sortOrder;
  final String? backgroundImageId;
  final String? pngImageId;
  final ImageData? backgroundImage;
  final ImageData? pngImage;

  Advertisement({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.description,
    required this.callToActionText,
    this.callToActionLink,
    required this.isExternal,
    required this.isActive,
    required this.sortOrder,
    this.backgroundImageId,
    this.pngImageId,
    this.backgroundImage,
    this.pngImage,
  });

  factory Advertisement.fromJson(Map<String, dynamic> json) {
    print('DEBUG: Parsing advertisement JSON: $json');
    final ad = Advertisement(
      id: json['id'],
      title: json['title'] ?? '',
      subtitle: json['subtitle'] ?? '',
      description: json['description'] ?? '',
      callToActionText: json['callToActionText'] ?? '',
      callToActionLink: json['callToActionLink'],
      isExternal: json['isExternal'] ?? false,
      isActive: json['isActive'] ?? true, // Default to true if not provided
      sortOrder: json['sortOrder'] ?? 0,
      backgroundImageId: json['backgroundImageId'],
      pngImageId: json['pngImageId'],
      backgroundImage:
          json['backgroundImage'] != null
              ? ImageData.fromJson(json['backgroundImage'])
              : null,
      pngImage:
          json['pngImage'] != null
              ? ImageData.fromJson(json['pngImage'])
              : null,
    );
    print('DEBUG: Created advertisement: ${ad.title}');
    return ad;
  }
}

class ImageData {
  final String id;
  final String name;
  final String type;
  final String url;

  ImageData({
    required this.id,
    required this.name,
    required this.type,
    required this.url,
  });

  factory ImageData.fromJson(Map<String, dynamic> json) {
    return ImageData(
      id: json['id'],
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      url: json['url'] ?? '',
    );
  }

  // The image URL is already complete
  String get fullImageUrl => url;
}
