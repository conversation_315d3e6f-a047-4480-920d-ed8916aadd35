import 'package:dalti/app/data/data_file.dart';
import 'package:dalti/app/routes/app_routes.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:syncfusion_flutter_datepicker/datepicker.dart';

import '../../../base/color_data.dart';
import '../../../base/constant.dart';
import '../../../base/widget_utils.dart';

class DateTimeScreen extends StatefulWidget {
  const DateTimeScreen({Key? key}) : super(key: key);

  @override
  State<DateTimeScreen> createState() => _DateTimeScreenState();
}

class _DateTimeScreenState extends State<DateTimeScreen> {
  List<String> timeLists = DataFile.timeList;
  var select = 0;
  var horspace = FetchPixels.getPixelWidth(20);

  SharedPreferences? selection;

  @override
  void initState() {
    super.initState();

    SharedPreferences.getInstance().then((SharedPreferences sp) {
      selection = sp;
      setState(() {});
    });
  }

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    return WillPopScope(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: backGroundColor,
        bottomNavigationBar: doneButton(context),
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              getVerSpace(FetchPixels.getPixelHeight(20)),
              getPaddingWidget(
                EdgeInsets.symmetric(horizontal: horspace),
                gettoolbarMenu(
                  context,
                  "back.svg",
                  () {
                    Constant.backToPrev(context);
                  },
                  title: "Date & Time",
                  weight: FontWeight.w800,
                  istext: true,
                  fontsize: 24,
                  textColor: Colors.black,
                ),
              ),
              getVerSpace(FetchPixels.getPixelHeight(30)),
              Expanded(
                flex: 1,
                child: ListView(
                  primary: true,
                  shrinkWrap: true,
                  children: [
                    calendarContainer(),
                    getVerSpace(FetchPixels.getPixelHeight(30)),
                    getPaddingWidget(
                      EdgeInsets.symmetric(horizontal: horspace),
                      getCustomFont(
                        "Select Time",
                        16,
                        Colors.black,
                        1,
                        fontWeight: FontWeight.w800,
                      ),
                    ),
                    getVerSpace(FetchPixels.getPixelHeight(10)),
                    GridView.builder(
                      primary: false,
                      shrinkWrap: true,
                      padding: EdgeInsets.only(
                        left: horspace,
                        right: horspace,
                        bottom: FetchPixels.getPixelHeight(15),
                      ),
                      physics: const BouncingScrollPhysics(),
                      itemCount: timeLists.length,
                      itemBuilder: (context, index) {
                        return GestureDetector(
                          onTap: () {
                            setState(() {
                              select = index;
                              selection!.setString("time", timeLists[index]);
                            });
                          },
                          child: Container(
                            alignment: Alignment.center,
                            decoration: BoxDecoration(
                              color: Colors.white,
                              boxShadow: const [
                                BoxShadow(
                                  color: Colors.black12,
                                  blurRadius: 10,
                                  offset: Offset(0.0, 4.0),
                                ),
                              ],
                              border:
                                  select == index
                                      ? Border.all(color: blueColor, width: 2)
                                      : null,
                              borderRadius: BorderRadius.circular(
                                FetchPixels.getPixelHeight(12),
                              ),
                            ),
                            child: getCustomFont(
                              timeLists[index],
                              16,
                              select == index ? blueColor : Colors.black,
                              1,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        );
                      },
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 3,
                        mainAxisExtent: FetchPixels.getPixelHeight(56),
                        crossAxisSpacing: FetchPixels.getPixelWidth(19),
                        mainAxisSpacing: FetchPixels.getPixelHeight(16),
                      ),
                    ),
                    getVerSpace(FetchPixels.getPixelHeight(20)),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      onWillPop: () async {
        Constant.backToPrev(context);
        return false;
      },
    );
  }

  Container doneButton(BuildContext context) {
    return Container(
      color: backGroundColor,
      padding: EdgeInsets.only(
        left: FetchPixels.getPixelWidth(20),
        right: FetchPixels.getPixelWidth(20),
        bottom: FetchPixels.getPixelHeight(33),
      ),
      child: getButton(
        context,
        blueColor,
        "Done",
        Colors.white,
        () {
          Constant.sendToNext(context, Routes.paymentRoute);
        },
        18,
        weight: FontWeight.w600,
        buttonHeight: FetchPixels.getPixelHeight(60),
        borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(14)),
      ),
    );
  }

  Container calendarContainer() {
    return Container(
      height: FetchPixels.getPixelHeight(363),
      margin: EdgeInsets.symmetric(horizontal: FetchPixels.getPixelWidth(20)),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0.0, 4.0),
          ),
        ],
        borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(20)),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(20)),
        child: SfDateRangePicker(
          monthViewSettings: const DateRangePickerMonthViewSettings(
            dayFormat: "EEE",
          ),
          onSelectionChanged: (DateRangePickerSelectionChangedArgs args) {
            selection!.setString("date", DateFormat.d().format(args.value));
            selection!.setString("month", DateFormat.MMMM().format(args.value));
            selection!.setString("year", DateFormat.y().format(args.value));
          },
          selectionShape: DateRangePickerSelectionShape.circle,
          showNavigationArrow: true,
          backgroundColor: Colors.white,
          selectionColor: blueColor,
          monthCellStyle: DateRangePickerMonthCellStyle(
            todayCellDecoration: BoxDecoration(
              border: Border.all(color: blueColor),
              shape: BoxShape.circle,
            ),
            textStyle: TextStyle(
              color: Colors.black,
              fontSize: FetchPixels.getPixelHeight(14),
              fontWeight: FontWeight.w400,
            ),
            todayTextStyle: TextStyle(
              color: blueColor,
              fontSize: FetchPixels.getPixelHeight(14),
              fontWeight: FontWeight.w400,
            ),
          ),
          selectionTextStyle: TextStyle(
            color: Colors.white,
            fontSize: FetchPixels.getPixelHeight(14),
            fontWeight: FontWeight.w400,
          ),
          selectionMode: DateRangePickerSelectionMode.single,
          headerStyle: DateRangePickerHeaderStyle(
            textAlign: TextAlign.start,
            textStyle: TextStyle(
              color: Colors.black,
              fontWeight: FontWeight.w800,
              fontSize: FetchPixels.getPixelHeight(16),
            ),
          ),
        ),
      ),
    );
  }
}
