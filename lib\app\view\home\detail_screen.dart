import 'package:dalti/app/data/data_file.dart';
import 'package:dalti/app/models/model_cart.dart';
import 'package:dalti/app/models/model_doctor.dart';
import 'package:dalti/app/models/booking_models.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:dalti/l10n/app_localizations.dart';
import 'package:dalti/app/view/home/<USER>';

import '../../../base/color_data.dart';
import '../../../base/constant.dart';
import '../dialog/color_dialog.dart';
import '../../routes/app_routes.dart';

const String prefsKeySessionId = 'session_id';

class DetailScreen extends StatefulWidget {
  final ModelDoctor doctor;

  const DetailScreen({Key? key, required this.doctor}) : super(key: key);

  @override
  State<DetailScreen> createState() => _DetailScreenState();
}

class _DetailScreenState extends State<DetailScreen> {
  ModelService? _selectedService;
  ModelQueue? _selectedQueue;
  DateTime _focusedDay = DateTime.now();
  DateTime? _selectedDate;
  AvailabilitySlot? _selectedSlot;

  bool _isLoadingAvailability = false;
  DateTime? _currentlyFetchedMonthStart;
  bool _isBookingInProgress = false;

  List<ModelQueue> _filteredQueuesForSelectedService = [];
  List<DailyAvailabilityResult> _availabilityForDateRange = [];

  final DateFormat _headerMonthYearFormat = DateFormat('MMMM yyyy');
  final DateFormat _slotTimeFormat = DateFormat('h:mm a');

  @override
  void initState() {
    super.initState();
    _selectedDate = _focusedDay;
    if (widget.doctor.services.isNotEmpty) {
      _selectedService = widget.doctor.services.first;
      _filterQueuesForService(_selectedService!.id);
      if (_filteredQueuesForSelectedService.isNotEmpty) {
        _selectedQueue = _filteredQueuesForSelectedService.first;
      }
      _fetchAvailability();
    } else {
      _fetchAvailability();
    }
  }

  void _filterQueuesForService(int serviceId) {
    if (!mounted) return;
    setState(() {
      _filteredQueuesForSelectedService =
          widget.doctor.queues.where((queue) {
            return queue.serviceIds.isEmpty ||
                queue.serviceIds.contains(serviceId);
          }).toList();

      if (_filteredQueuesForSelectedService.isEmpty ||
          !_filteredQueuesForSelectedService.contains(_selectedQueue)) {
        _selectedQueue =
            _filteredQueuesForSelectedService.isNotEmpty
                ? _filteredQueuesForSelectedService.first
                : null;
      }
      _availabilityForDateRange = [];
      _selectedSlot = null;
      _currentlyFetchedMonthStart = null;
      if (_selectedDate != null &&
          (_selectedQueue != null ||
              _filteredQueuesForSelectedService.isEmpty)) {
        _fetchAvailability();
      }
    });
  }

  Future<void> _fetchAvailability() async {
    if (_selectedService == null || _selectedDate == null) {
      setState(() {
        _availabilityForDateRange = [];
        _selectedSlot = null;
      });
      return;
    }

    if (!mounted) return;

    DateTime monthToFetch = DateTime(
      _selectedDate!.year,
      _selectedDate!.month,
      1,
    );

    if (monthToFetch == _currentlyFetchedMonthStart &&
        _availabilityForDateRange.isNotEmpty) {
      if (_isLoadingAvailability) {
        setState(() {
          _isLoadingAvailability = false;
        });
      }
      return;
    }

    setState(() {
      _isLoadingAvailability = true;
      _availabilityForDateRange = [];
      _selectedSlot = null;
    });

    DateTime firstDayOfMonth = monthToFetch;
    DateTime lastDayOfMonth = DateTime(
      monthToFetch.year,
      monthToFetch.month + 1,
      0,
    );

    final DateFormat formatter = DateFormat('yyyy-MM-dd');
    final String startDate = formatter.format(firstDayOfMonth);
    final String endDate = formatter.format(lastDayOfMonth);

    const String baseUrl =
        "https://dapi-test.adscloud.org:8443/api/provider-availability";
    Map<String, String> queryParams = {
      'sProvidingPlaceId': widget.doctor.sProvidingPlaceId.toString(),
      'serviceId': _selectedService!.id.toString(),
      'startDate': startDate,
      'endDate': endDate,
    };

    if (_selectedQueue != null) {
      queryParams['queueId'] = _selectedQueue!.id.toString();
    }

    Uri uri = Uri.parse(baseUrl).replace(queryParameters: queryParams);
    print(
      "Fetching availability from: $uri for month: ${DateFormat('MMM yyyy').format(monthToFetch)}",
    );

    try {
      final response = await http.get(
        uri,
        headers: {'Accept': 'application/json'},
      );
      if (!mounted) return;

      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);
        setState(() {
          _availabilityForDateRange =
              responseData
                  .map(
                    (data) => DailyAvailabilityResult.fromJson(
                      data as Map<String, dynamic>,
                    ),
                  )
                  .toList();
          _currentlyFetchedMonthStart = firstDayOfMonth;
        });
      } else {
        final errorData = jsonDecode(response.body);
        final errorMessage =
            errorData is Map
                ? errorData['message'] as String? ??
                    "Failed to fetch availability"
                : "Failed to fetch availability: ${response.statusCode}";
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text(errorMessage), backgroundColor: Colors.red),
          );
        }
        _availabilityForDateRange = [];
        _currentlyFetchedMonthStart = null;
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text("Error during availability fetch: $e"),
          backgroundColor: Colors.red,
        ),
      );
      _availabilityForDateRange = [];
      _currentlyFetchedMonthStart = null;
    } finally {
      if (!mounted) return;
      setState(() {
        _isLoadingAvailability = false;
      });
    }
  }

  void _onDateSelected(DateTime selectedDay, DateTime focusedDay) {
    final localizations = AppLocalizations.of(context)!;
    final today = DateTime(
      DateTime.now().year,
      DateTime.now().month,
      DateTime.now().day,
    );
    if (selectedDay.isBefore(today)) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(localizations.detailScreenCannotSelectPastDate),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }

    if (!isSameDay(_selectedDate, selectedDay)) {
      setState(() {
        _selectedDate = selectedDay;
        _focusedDay = focusedDay;
        _selectedSlot = null;

        DateTime newMonthStart = DateTime(
          selectedDay.year,
          selectedDay.month,
          1,
        );
        if (newMonthStart != _currentlyFetchedMonthStart ||
            _availabilityForDateRange.isEmpty) {
          _fetchAvailability();
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    return WillPopScope(
      child: Scaffold(
        backgroundColor: themedBackgroundColor,
        body: SafeArea(
          child: Column(
            children: [
              getPaddingWidget(
                EdgeInsets.symmetric(
                  horizontal: FetchPixels.getPixelWidth(20),
                  vertical: FetchPixels.getPixelHeight(10),
                ),
                gettoolbarMenu(
                  context,
                  "back.svg",
                  () {
                    Constant.backToPrev(context);
                  },
                  title: widget.doctor.specialization,
                  weight: FontWeight.bold,
                  textColor: appDarkText,
                  fontsize: 20,
                  istext: true,
                  isrightimage: true,
                  rightimage: "more.svg",
                  rightFunction: () {},
                ),
              ),
              Expanded(
                child: ListView(
                  padding: EdgeInsets.symmetric(
                    horizontal: FetchPixels.getPixelWidth(12),
                    vertical: FetchPixels.getPixelHeight(10),
                  ),
                  children: [
                    _buildDoctorInfoCard(),
                    getVerSpace(FetchPixels.getPixelHeight(24)),
                    _buildBookingSection(),
                    getVerSpace(FetchPixels.getPixelHeight(30)),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
      onWillPop: () async {
        Constant.backToPrev(context);
        return false;
      },
    );
  }

  Widget _buildDoctorInfoCard() {
    return GestureDetector(
      onTap: () {
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => ProviderProfileScreen(
                  providerId: widget.doctor.id.toString(),
                ),
          ),
        );
      },
      child: Card(
        elevation: 3.0,
        color: appCardBackground,
        margin: EdgeInsets.symmetric(vertical: FetchPixels.getPixelHeight(8)),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
        ),
        child: Padding(
          padding: EdgeInsets.all(FetchPixels.getPixelHeight(16)),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        getCustomFont(
                          widget.doctor.name,
                          19,
                          appDarkText,
                          2,
                          fontWeight: FontWeight.w700,
                        ),
                        getVerSpace(FetchPixels.getPixelHeight(5)),
                        getCustomFont(
                          widget.doctor.specialization,
                          15,
                          themedBlueColor,
                          1,
                          fontWeight: FontWeight.w600,
                        ),
                        getVerSpace(FetchPixels.getPixelHeight(3)),
                        getCustomFont(
                          "at ${widget.doctor.hospital}",
                          13,
                          appMediumText,
                          2,
                          fontWeight: FontWeight.normal,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              getVerSpace(FetchPixels.getPixelHeight(16)),
              Row(
                children: [
                  ...List.generate(5, (idx) {
                    return Icon(
                      idx < widget.doctor.rating
                          ? Icons.star_rounded
                          : Icons.star_border_rounded,
                      color: Colors.amber,
                      size: FetchPixels.getPixelHeight(20),
                    );
                  }),
                  getHorSpace(FetchPixels.getPixelWidth(8)),
                  getCustomFont(
                    "(${widget.doctor.reviewCount} reviews)",
                    13,
                    appLightText,
                    1,
                    fontWeight: FontWeight.normal,
                  ),
                ],
              ),
              getVerSpace(FetchPixels.getPixelHeight(10)),
              Divider(
                color: appMuted.withOpacity(0.4),
                height: FetchPixels.getPixelHeight(12),
              ),
              getVerSpace(FetchPixels.getPixelHeight(12)),
              if (widget.doctor.phone != null &&
                  widget.doctor.phone!.isNotEmpty)
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        Icon(
                          Icons.phone_outlined,
                          size: 15,
                          color: appMediumText,
                        ),
                        getHorSpace(FetchPixels.getPixelWidth(8)),
                        getCustomFont(
                          widget.doctor.phone!,
                          14,
                          appDarkText,
                          1,
                          fontWeight: FontWeight.w500,
                        ),
                      ],
                    ),
                    IconButton(
                      icon: Icon(
                        Icons.call_rounded,
                        color: themedBlueColor,
                        size: 22,
                      ),
                      onPressed: () {
                        _makePhoneCall(widget.doctor.phone);
                      },
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints(),
                    ),
                  ],
                )
              else
                getCustomFont("No phone number available", 13, appLightText, 1),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _makePhoneCall(String? phoneNumber) async {
    final localizations = AppLocalizations.of(context)!;
    if (phoneNumber == null || phoneNumber.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(localizations.detailScreenNoPhoneNumber),
            backgroundColor: Colors.orange,
          ),
        );
      }
      return;
    }
    final Uri launchUri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(launchUri)) {
      await launchUrl(launchUri);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(localizations.detailScreenCannotLaunchCall),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Widget _buildBookingSection() {
    final localizations = AppLocalizations.of(context)!;
    return Card(
      elevation: 3.0,
      color: appCardBackground,
      margin: EdgeInsets.symmetric(vertical: FetchPixels.getPixelHeight(8)),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
      ),
      child: Padding(
        padding: EdgeInsets.all(FetchPixels.getPixelHeight(16)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            getCustomFont(
              localizations.detailScreenBookAppointment,
              18,
              appDarkText,
              1,
              fontWeight: FontWeight.w700,
            ),
            getVerSpace(FetchPixels.getPixelHeight(18)),

            _buildServiceSelection(),
            getVerSpace(FetchPixels.getPixelHeight(18)),

            if (_selectedService != null) ...[
              _buildQueueSelection(),
              getVerSpace(FetchPixels.getPixelHeight(18)),
            ],

            if (_selectedService != null &&
                (_selectedQueue != null ||
                    _filteredQueuesForSelectedService.isEmpty)) ...[
              _buildCalendarView(),
              getVerSpace(FetchPixels.getPixelHeight(18)),
            ],

            if (_selectedDate != null &&
                _selectedService != null &&
                (_selectedQueue != null ||
                    _filteredQueuesForSelectedService.isEmpty)) ...[
              _buildSectionTitle("Select Time"),
              getVerSpace(FetchPixels.getPixelHeight(10)),
              _buildSlotsDisplay(),
              getVerSpace(FetchPixels.getPixelHeight(24)),
            ],

            if (_selectedService != null &&
                _selectedService!.pointsRequirements > 0)
              _buildPointsRequirementAlert(),

            if (_selectedService != null &&
                _selectedDate != null &&
                _selectedSlot != null)
              _buildConfirmBookingButton(),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: EdgeInsets.only(bottom: FetchPixels.getPixelHeight(8)),
      child: getCustomFont(
        title,
        15,
        appDarkText,
        1,
        fontWeight: FontWeight.w600,
      ),
    );
  }

  Widget _buildServiceSelection() {
    final localizations = AppLocalizations.of(context)!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(localizations.detailScreenServiceLabel),
        if (widget.doctor.services.isEmpty)
          Padding(
            padding: EdgeInsets.symmetric(
              vertical: FetchPixels.getPixelHeight(8),
            ),
            child: getCustomFont(
              localizations.detailScreenNoServices,
              13,
              appLightText,
              1,
            ),
          )
        else
          Container(
            height: FetchPixels.getPixelHeight(42),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: widget.doctor.services.length,
              itemBuilder: (context, index) {
                ModelService service = widget.doctor.services[index];
                bool isSelected = _selectedService == service;
                return Padding(
                  padding: EdgeInsets.only(right: FetchPixels.getPixelWidth(8)),
                  child: ChoiceChip(
                    label: Text(
                      service.name,
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                    selected: isSelected,
                    onSelected: (bool selected) {
                      if (_selectedService == service && !selected) return;
                      if (selected) {
                        setState(() {
                          _selectedService = service;
                          _filterQueuesForService(service.id);
                        });
                      }
                    },
                    selectedColor: appLightTeal,
                    backgroundColor: appCardBackground,
                    labelStyle: TextStyle(
                      color:
                          isSelected
                              ? themedBlueColor
                              : appUnselectedButtonText,
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: FetchPixels.getPixelWidth(12),
                      vertical: FetchPixels.getPixelHeight(8),
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        FetchPixels.getPixelHeight(20),
                      ),
                      side: BorderSide(
                        color:
                            isSelected
                                ? themedBlueColor
                                : appMuted.withOpacity(0.5),
                        width: isSelected ? 1.5 : 1.0,
                      ),
                    ),
                    elevation: isSelected ? 1.0 : 0.0,
                    pressElevation: 2.0,
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  Widget _buildQueueSelection() {
    final localizations = AppLocalizations.of(context)!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(localizations.detailScreenQueueLabel),
        if (_filteredQueuesForSelectedService.isEmpty)
          Padding(
            padding: EdgeInsets.symmetric(
              vertical: FetchPixels.getPixelHeight(8),
            ),
            child: getCustomFont(
              localizations.detailScreenGeneralAvailability,
              13,
              appLightText,
              1,
            ),
          )
        else
          Container(
            height: FetchPixels.getPixelHeight(42),
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _filteredQueuesForSelectedService.length,
              itemBuilder: (context, index) {
                ModelQueue queue = _filteredQueuesForSelectedService[index];
                bool isSelected = _selectedQueue == queue;
                return Padding(
                  padding: EdgeInsets.only(right: FetchPixels.getPixelWidth(8)),
                  child: ChoiceChip(
                    label: Text(
                      queue.name,
                      style: TextStyle(
                        fontSize: 13,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                    selected: isSelected,
                    onSelected: (bool selected) {
                      if (_selectedQueue == queue && !selected) return;
                      if (selected) {
                        setState(() {
                          _selectedQueue = queue;
                          _availabilityForDateRange = [];
                          _selectedSlot = null;
                          _currentlyFetchedMonthStart = null;
                          if (_selectedDate != null) {
                            _fetchAvailability();
                          }
                        });
                      }
                    },
                    selectedColor: appLightTeal,
                    backgroundColor: appCardBackground,
                    labelStyle: TextStyle(
                      color:
                          isSelected
                              ? themedBlueColor
                              : appUnselectedButtonText,
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: FetchPixels.getPixelWidth(12),
                      vertical: FetchPixels.getPixelHeight(8),
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        FetchPixels.getPixelHeight(20),
                      ),
                      side: BorderSide(
                        color:
                            isSelected
                                ? themedBlueColor
                                : appMuted.withOpacity(0.5),
                        width: isSelected ? 1.5 : 1.0,
                      ),
                    ),
                    elevation: isSelected ? 1.0 : 0.0,
                    pressElevation: 2.0,
                  ),
                );
              },
            ),
          ),
      ],
    );
  }

  Widget _buildCalendarView() {
    final localizations = AppLocalizations.of(context)!;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSectionTitle(localizations.detailScreenSelectDate),
        Container(
          decoration: BoxDecoration(
            color: appCardBackground,
            borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(10)),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withOpacity(0.1),
                spreadRadius: 0.5,
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          padding: EdgeInsets.symmetric(
            vertical: FetchPixels.getPixelHeight(6),
            horizontal: FetchPixels.getPixelHeight(4),
          ),
          child: TableCalendar(
            locale: 'en_US',
            firstDay: DateTime(
              DateTime.now().year,
              DateTime.now().month,
              DateTime.now().day,
            ),
            lastDay: DateTime.now().add(const Duration(days: 365 * 2)),
            focusedDay: _focusedDay,
            selectedDayPredicate: (day) => isSameDay(_selectedDate, day),
            calendarFormat: CalendarFormat.week,
            startingDayOfWeek: StartingDayOfWeek.monday,
            daysOfWeekVisible: true,

            enabledDayPredicate: (day) {
              final today = DateTime(
                DateTime.now().year,
                DateTime.now().month,
                DateTime.now().day,
              );
              return !day.isBefore(today);
            },

            headerStyle: HeaderStyle(
              formatButtonVisible: false,
              titleCentered: true,
              titleTextStyle: TextStyle(
                fontSize: 15,
                fontWeight: FontWeight.w600,
                color: appDarkText,
              ),
              titleTextFormatter:
                  (date, locale) => _headerMonthYearFormat.format(date),
              leftChevronIcon: Icon(
                Icons.chevron_left_rounded,
                color: themedBlueColor,
                size: 26,
              ),
              rightChevronIcon: Icon(
                Icons.chevron_right_rounded,
                color: themedBlueColor,
                size: 26,
              ),
              headerPadding: EdgeInsets.symmetric(
                vertical: FetchPixels.getPixelHeight(8),
              ),
              decoration: BoxDecoration(
                color: Colors.grey[50],
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(FetchPixels.getPixelHeight(10)),
                  topRight: Radius.circular(FetchPixels.getPixelHeight(10)),
                ),
              ),
            ),

            calendarStyle: CalendarStyle(
              defaultTextStyle: TextStyle(color: appMediumText, fontSize: 14),
              weekendTextStyle: TextStyle(color: appMediumText, fontSize: 14),
              selectedTextStyle: TextStyle(
                color: appSelectedButtonText,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              selectedDecoration: BoxDecoration(
                color: themedBlueColor,
                shape: BoxShape.rectangle,
                borderRadius: BorderRadius.circular(
                  FetchPixels.getPixelHeight(8),
                ),
              ),
              todayTextStyle: TextStyle(
                color: themedBlueColor,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
              todayDecoration: BoxDecoration(
                border: Border.all(color: themedBlueColor, width: 1.5),
                shape: BoxShape.rectangle,
                borderRadius: BorderRadius.circular(
                  FetchPixels.getPixelHeight(8),
                ),
              ),
              defaultDecoration: BoxDecoration(
                shape: BoxShape.rectangle,
                borderRadius: BorderRadius.circular(
                  FetchPixels.getPixelHeight(8),
                ),
              ),
              weekendDecoration: BoxDecoration(
                shape: BoxShape.rectangle,
                borderRadius: BorderRadius.circular(
                  FetchPixels.getPixelHeight(8),
                ),
              ),
              outsideDaysVisible: false,
              cellMargin: EdgeInsets.all(FetchPixels.getPixelHeight(3)),
              cellPadding: EdgeInsets.zero,
            ),

            daysOfWeekStyle: DaysOfWeekStyle(
              weekdayStyle: TextStyle(
                color: appLightText,
                fontWeight: FontWeight.w500,
                fontSize: 11,
              ),
              weekendStyle: TextStyle(
                color: appLightText,
                fontWeight: FontWeight.w500,
                fontSize: 11,
              ),
            ),

            onDaySelected: _onDateSelected,
            onPageChanged: (focusedDay) {
              if (!isSameDay(_focusedDay, focusedDay)) {
                _focusedDay = focusedDay;
                bool isSelectedDayVisible =
                    focusedDay.month == _selectedDate?.month &&
                    focusedDay.year == _selectedDate?.year;
                if (!isSelectedDayVisible || _selectedDate == null) {
                  _onDateSelected(focusedDay, focusedDay);
                } else {
                  DateTime newMonthStart = DateTime(
                    focusedDay.year,
                    focusedDay.month,
                    1,
                  );
                  if (newMonthStart != _currentlyFetchedMonthStart) {
                    _fetchAvailability();
                  }
                }
              }
            },
          ),
        ),
      ],
    );
  }

  Map<String, List<AvailabilitySlot>> _groupAvailableSlots(
    List<AvailabilitySlot> slots,
  ) {
    Map<String, List<AvailabilitySlot>> grouped = {};
    final now = DateTime.now();
    final bool isToday =
        _selectedDate != null && isSameDay(_selectedDate!, now);

    for (var slot in slots) {
      if (!slot.isBooked) {
        if (isToday && slot.startTime.toLocal().isBefore(now)) {
          continue;
        }
        String timeKey = _slotTimeFormat.format(slot.startTime.toLocal());
        grouped.putIfAbsent(timeKey, () => []).add(slot);
      }
    }
    return grouped;
  }

  Widget _buildSlotsDisplay() {
    final localizations = AppLocalizations.of(context)!;
    if (_isLoadingAvailability) {
      return Center(
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 20.0),
          child: CircularProgressIndicator(
            strokeWidth: 2,
            color: themedBlueColor,
          ),
        ),
      );
    }

    if (_selectedDate == null) {
      return Center(
        child: Padding(
          padding: EdgeInsets.symmetric(
            vertical: FetchPixels.getPixelHeight(15),
          ),
          child: getCustomFont(
            localizations.detailScreenPleaseSelectDate,
            13,
            appLightText,
            1,
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    DailyAvailabilityResult? dayAvailability;
    try {
      dayAvailability = _availabilityForDateRange.firstWhere(
        (dayResult) => isSameDay(dayResult.date, _selectedDate!),
      );
    } catch (e) {
      dayAvailability = null;
    }

    if (dayAvailability == null || dayAvailability.slots.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.symmetric(
            vertical: FetchPixels.getPixelHeight(15),
          ),
          child: getCustomFont(
            localizations.detailScreenNoSlotsFound(
              DateFormat('MMM d').format(_selectedDate!),
            ),
            13,
            appLightText,
            1,
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    Map<String, List<AvailabilitySlot>> groupedSlots = _groupAvailableSlots(
      dayAvailability.slots,
    );

    if (groupedSlots.isEmpty) {
      return Center(
        child: Padding(
          padding: EdgeInsets.symmetric(
            vertical: FetchPixels.getPixelHeight(15),
          ),
          child: getCustomFont(
            localizations.detailScreenAllSlotsBooked(
              DateFormat('MMM d').format(_selectedDate!),
            ),
            13,
            appLightText,
            1,
            textAlign: TextAlign.center,
          ),
        ),
      );
    }

    List<String> sortedTimeKeys = groupedSlots.keys.toList();
    sortedTimeKeys.sort((a, b) {
      try {
        DateTime timeA = _slotTimeFormat.parse(a);
        DateTime timeB = _slotTimeFormat.parse(b);
        return timeA.compareTo(timeB);
      } catch (e) {
        return a.compareTo(b);
      }
    });

    return Container(
      height: FetchPixels.getPixelHeight(42),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: sortedTimeKeys.length,
        itemBuilder: (context, index) {
          String timeKey = sortedTimeKeys[index];
          List<AvailabilitySlot> slotsAtThisTime = groupedSlots[timeKey]!;
          bool isCurrentlySelectedTime =
              _selectedSlot != null &&
              timeKey ==
                  _slotTimeFormat.format(_selectedSlot!.startTime.toLocal());

          return Padding(
            padding: EdgeInsets.only(right: FetchPixels.getPixelWidth(8)),
            child: ChoiceChip(
              label: Text(
                timeKey,
                style: TextStyle(
                  fontSize: 13,
                  fontWeight:
                      isCurrentlySelectedTime
                          ? FontWeight.w600
                          : FontWeight.normal,
                ),
              ),
              selected: isCurrentlySelectedTime,
              onSelected: (bool selected) {
                if (isCurrentlySelectedTime && !selected) return;
                if (selected) {
                  setState(() {
                    if (slotsAtThisTime.isNotEmpty) {
                      _selectedSlot = slotsAtThisTime.first;
                    }
                  });
                }
              },
              selectedColor: appLightTeal,
              backgroundColor: appCardBackground,
              labelStyle: TextStyle(
                color:
                    isCurrentlySelectedTime
                        ? themedBlueColor
                        : appUnselectedButtonText,
              ),
              padding: EdgeInsets.symmetric(
                horizontal: FetchPixels.getPixelWidth(12),
                vertical: FetchPixels.getPixelHeight(8),
              ),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(
                  FetchPixels.getPixelHeight(20),
                ),
                side: BorderSide(
                  color:
                      isCurrentlySelectedTime
                          ? themedBlueColor
                          : appMuted.withOpacity(0.5),
                  width: isCurrentlySelectedTime ? 1.5 : 1.0,
                ),
              ),
              elevation: isCurrentlySelectedTime ? 1.0 : 0.0,
              pressElevation: 2.0,
            ),
          );
        },
      ),
    );
  }

  Widget _buildConfirmBookingButton() {
    final localizations = AppLocalizations.of(context)!;
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: FetchPixels.getPixelWidth(0),
        vertical: FetchPixels.getPixelHeight(10),
      ),
      child: ElevatedButton(
        style: ElevatedButton.styleFrom(
          backgroundColor: themedBlueColor,
          padding: EdgeInsets.symmetric(
            vertical: FetchPixels.getPixelHeight(15),
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(10)),
          ),
          minimumSize: Size(double.infinity, FetchPixels.getPixelHeight(50)),
          elevation: 2.0,
        ),
        onPressed: () {
          showDialog(
            context: context,
            builder:
                (c) => AlertDialog(
                  backgroundColor: appCardBackground,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(
                      FetchPixels.getPixelHeight(12),
                    ),
                  ),
                  title: Text(
                    localizations.detailScreenConfirmBooking,
                    style: TextStyle(
                      fontWeight: FontWeight.w700,
                      color: appDarkText,
                      fontSize: 17,
                    ),
                  ),
                  content: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildConfirmationDetailRow(
                        localizations.detailScreenServiceDetail,
                        _selectedService!.name,
                      ),
                      _buildConfirmationDetailRow(
                        localizations.detailScreenQueueDetail,
                        _selectedQueue?.name ?? 'General',
                      ),
                      _buildConfirmationDetailRow(
                        localizations.detailScreenDateDetail,
                        DateFormat('EEE, MMM d, yyyy').format(_selectedDate!),
                      ),
                      _buildConfirmationDetailRow(
                        localizations.detailScreenTimeDetail,
                        _slotTimeFormat.format(
                          _selectedSlot!.startTime.toLocal(),
                        ),
                      ),
                    ],
                  ),
                  actionsPadding: EdgeInsets.symmetric(
                    horizontal: FetchPixels.getPixelWidth(10),
                    vertical: FetchPixels.getPixelHeight(8),
                  ),
                  actions: [
                    TextButton(
                      onPressed: () => Navigator.of(c).pop(),
                      child: Text(
                        localizations.detailScreenCancel,
                        style: TextStyle(
                          color: appMediumText,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    TextButton(
                      onPressed: () {
                        Navigator.of(c).pop();
                        _processBooking();
                      },
                      child:
                          _isBookingInProgress
                              ? SizedBox(
                                width: 20,
                                height: 20,
                                child: CircularProgressIndicator(
                                  strokeWidth: 2,
                                  valueColor: AlwaysStoppedAnimation<Color>(
                                    themedBlueColor,
                                  ),
                                ),
                              )
                              : Text(
                                localizations.detailScreenConfirm,
                                style: TextStyle(
                                  color: themedBlueColor,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 15,
                                ),
                              ),
                    ),
                  ],
                ),
          );
        },
        child:
            (_isBookingInProgress && _selectedSlot != null)
                ? SizedBox(
                  width: FetchPixels.getPixelHeight(24),
                  height: FetchPixels.getPixelHeight(24),
                  child: CircularProgressIndicator(
                    strokeWidth: 2.5,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      appSelectedButtonText,
                    ),
                  ),
                )
                : Text(
                  localizations.detailScreenBook(
                    _slotTimeFormat.format(_selectedSlot!.startTime.toLocal()),
                    DateFormat('MMM d').format(_selectedDate!),
                  ),
                  style: TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: appSelectedButtonText,
                  ),
                ),
      ),
    );
  }

  Future<void> _processBooking() async {
    final localizations = AppLocalizations.of(context)!;
    if (_selectedService == null ||
        _selectedSlot == null ||
        _selectedDate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(localizations.detailScreenIncompleteDetails),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isBookingInProgress = true;
    });

    final String startTimeIso = _selectedSlot!.startTime.toIso8601String();
    final String endTimeIso =
        _selectedSlot!.startTime
            .add(Duration(minutes: _selectedService!.duration))
            .toIso8601String();

    // Retrieve session ID
    final prefs = await SharedPreferences.getInstance();
    final String? sessionId = prefs.getString(prefsKeySessionId);

    if (sessionId == null || sessionId.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(localizations.detailScreenPleaseLogin),
            backgroundColor: Colors.orange,
          ),
        );
        setState(() {
          _isBookingInProgress = false;
        });
      }
      return;
    }

    final bookingData = {
      'placeId': widget.doctor.sProvidingPlaceId,
      'serviceId': _selectedService!.id,
      'queueId': _selectedSlot!.queueId,
      'startTime': startTimeIso,
      'endTime': endTimeIso,
      // 'notes': 'Optional notes here' // Add if notes field is implemented
    };

    const String bookingUrl =
        "https://dapi-test.adscloud.org:8443/api/auth/appointments/customer-booking";
    print("Booking attempt: $bookingUrl with data: ${jsonEncode(bookingData)}");

    try {
      final response = await http.post(
        Uri.parse(bookingUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $sessionId',
        },
        body: jsonEncode(bookingData),
      );

      if (!mounted) return;

      if (response.statusCode == 201) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(localizations.detailScreenBookingSuccess),
            backgroundColor: success,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                FetchPixels.getPixelHeight(8),
              ),
            ),
          ),
        );
        // Clear selections and refresh
        setState(() {
          _selectedService =
              widget.doctor.services.isNotEmpty
                  ? widget.doctor.services.first
                  : null;
          if (_selectedService != null)
            _filterQueuesForService(_selectedService!.id);
          else
            _filteredQueuesForSelectedService = [];
          _selectedQueue =
              _filteredQueuesForSelectedService.isNotEmpty
                  ? _filteredQueuesForSelectedService.first
                  : null;
          // _selectedDate remains, but availability for it will be refetched
          _selectedSlot = null;
          _availabilityForDateRange = [];
          _currentlyFetchedMonthStart = null;
        });
        _fetchAvailability(); // Refresh availability for the current view

        // Navigate to home screen and select the bookings tab (index 1)
        Navigator.pushNamedAndRemoveUntil(
          context,
          Routes.homeScreenRoute,
          (route) => false,
          arguments: 1,
        );
      } else {
        String errorMessage = localizations.detailScreenBookingFailed;
        try {
          final errorData = jsonDecode(response.body);
          errorMessage = errorData['message'] as String? ?? errorMessage;
        } catch (_) {}
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMessage), backgroundColor: Colors.red),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(localizations.detailScreenErrorOccurred(e.toString())),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      if (!mounted) return;
      setState(() {
        _isBookingInProgress = false;
      });
    }
  }

  Widget _buildConfirmationDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: FetchPixels.getPixelHeight(4)),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          getCustomFont(
            "$label ",
            14,
            appMediumText,
            1,
            fontWeight: FontWeight.w600,
          ),
          Expanded(
            child: getCustomFont(
              value,
              14,
              appDarkText,
              2,
              fontWeight: FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPointsRequirementAlert() {
    final localizations = AppLocalizations.of(context)!;
    if (_selectedService == null || _selectedService!.pointsRequirements <= 0) {
      return SizedBox.shrink();
    }
    return Container(
      margin: EdgeInsets.only(bottom: FetchPixels.getPixelHeight(16)),
      padding: EdgeInsets.symmetric(
        horizontal: FetchPixels.getPixelWidth(12),
        vertical: FetchPixels.getPixelHeight(10),
      ),
      decoration: BoxDecoration(
        color: themedBlueColor.withOpacity(0.08),
        borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(8)),
        border: Border.all(color: themedBlueColor.withOpacity(0.6), width: 1),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline_rounded,
            color: themedBlueColor,
            size: FetchPixels.getPixelHeight(20),
          ),
          getHorSpace(FetchPixels.getPixelWidth(10)),
          Expanded(
            child: getCustomFont(
              localizations.detailScreenPointsRequired(
                _selectedService!.pointsRequirements.toStringAsFixed(0),
              ),
              13.5,
              themedBlueColor.withOpacity(0.9),
              2,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }
}
