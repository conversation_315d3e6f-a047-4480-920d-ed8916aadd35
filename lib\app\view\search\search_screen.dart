import 'dart:convert'; // For jsonDecode
import 'package:dalti/app/view/home/<USER>'; // Updated import
import 'package:dalti/app/models/model_category.dart'; // Added for type hinting
import 'package:http/http.dart' as http; // For API calls
import 'package:dalti/app/models/model_doctor.dart';
import 'package:dalti/app/models/booking_models.dart'; // Added import for ModelService
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart'; // For date formatting
import 'package:dalti/l10n/app_localizations.dart'; // Import AppLocalizations
import 'package:shared_preferences/shared_preferences.dart';
import '../../../base/color_data.dart';
import '../../../base/constant.dart';
import '../../../base/widget_utils.dart';
import './widgets/filter_modal_widget.dart'; // Import for the new externalized modal
import 'package:dalti/app/routes/app_routes.dart'; // Added import for Routes

// Helper class to pass filter results from the modal - MOVED to filter_modal_widget.dart
// class FilterResult { ... }

class SearchScreen extends StatefulWidget {
  final ModelCategory? initialCategory; // Added constructor argument

  const SearchScreen({Key? key, this.initialCategory})
    : super(key: key); // Updated constructor

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen> {
  TextEditingController searchController = TextEditingController();
  List<ModelDoctor> _doctors = [];
  bool _isLoadingApiCall = false;
  String _currentSearchQuery = '';

  // State variables for applied filters
  int? _selectedCategoryId;
  String? _selectedCategoryName;
  String? _selectedCity;

  // Using daltiPrimaryLight and daltiAccent as potential dynamic image backgrounds
  // Or we can define a new list in color_data.dart for this purpose
  final List<Color> _providerImageColors = [
    daltiPrimaryLight,
    daltiAccent.withOpacity(0.3),
    "#E0F2FE".toColor(), // Light Blue (kept one for variety)
    "#D1FAE5".toColor(), // Light Green (kept one for variety)
    daltiPrimary.withOpacity(0.2),
    daltiAccent.withOpacity(0.2),
  ];

  @override
  void initState() {
    super.initState();
    _currentSearchQuery = "";
    searchController.text = _currentSearchQuery;
    // Initial fetch moved to didChangeDependencies
  }

  bool _initialCategoryProcessed = false;

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final localizations = AppLocalizations.of(context)!;
    if (!_initialCategoryProcessed) {
      ModelCategory? categoryFromRoute;
      if (widget.initialCategory != null) {
        categoryFromRoute = widget.initialCategory;
      } else {
        final arguments = ModalRoute.of(context)?.settings.arguments;
        if (arguments is Map<String, dynamic> &&
            arguments.containsKey('category')) {
          if (arguments['category'] is ModelCategory) {
            categoryFromRoute = arguments['category'] as ModelCategory?;
          }
        }
      }

      if (categoryFromRoute != null) {
        _selectedCategoryId = categoryFromRoute.id;
        _selectedCategoryName = categoryFromRoute.title;
        _initialCategoryProcessed = true;
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            _fetchProviders(
              localizations: localizations,
              query: _currentSearchQuery,
              categoryId: _selectedCategoryId,
              city: _selectedCity,
            );
          }
        });
      } else if (!_initialCategoryProcessed && widget.initialCategory == null) {
        _fetchProviders(
          localizations: localizations,
          query: _currentSearchQuery,
        );
        _initialCategoryProcessed = true;
      }
    }
  }

  Future<void> _fetchProviders({
    required AppLocalizations localizations, // Pass localizations
    String? query,
    int? categoryId,
    String? city,
    int skip = 0,
    int take = 10,
  }) async {
    if (!mounted) return;
    setState(() {
      _isLoadingApiCall = true;
    });

    const String baseUrl =
        "https://dapi-test.adscloud.org:8443/api/search/providers";
    Map<String, String> queryParams = {
      'skip': skip.toString(),
      'take': take.toString(),
    };

    if (query != null && query.isNotEmpty) queryParams['q'] = query;
    if (categoryId != null) {
      queryParams['categoryId'] = categoryId.toString();
    } else if (_selectedCategoryId != null) {
      queryParams['categoryId'] = _selectedCategoryId!.toString();
    }
    if (city != null && city.isNotEmpty) {
      queryParams['city'] = city;
    } else if (_selectedCity != null && _selectedCity!.isNotEmpty) {
      queryParams['city'] = _selectedCity!;
    }

    final prefs = await SharedPreferences.getInstance();
    String? languageCode = prefs.getString('language_code');
    print("DEBUG: languageCode: $languageCode");
    if (languageCode != null) {
      queryParams['targetLanguage'] = languageCode.toUpperCase();
    }

    if (query != null) {
      _currentSearchQuery = query;
    }

    Uri uri = Uri.parse(baseUrl).replace(queryParameters: queryParams);
    print("Fetching providers from: $uri");

    try {
      final response = await http.get(uri);
      if (!mounted) return;

      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);
        setState(() {
          _doctors =
              responseData.map((data) {
                print("data: $data");
                final user = data['user'] as Map<String, dynamic>?;
                final String firstName = user?['firstName'] as String? ?? '';
                final String lastName = user?['lastName'] as String? ?? '';
                final String doctorName =
                    (firstName.isNotEmpty || lastName.isNotEmpty)
                        ? "${localizations.doctorPrefix}$firstName $lastName"
                            .trim()
                        : data['title'] as String? ??
                            localizations.unnamedProvider;

                final categoryData = data['category'] as Map<String, dynamic>?;
                final String specialization =
                    categoryData?['title'] as String? ??
                    localizations.defaultSpecialization;

                final providingPlacesData =
                    data['providingPlaces'] as List<dynamic>?;
                int sProvidingPlaceId = 0;
                String hospitalName = localizations.naHospital;
                List<ModelQueue> doctorQueues = [];

                if (providingPlacesData != null &&
                    providingPlacesData.isNotEmpty) {
                  final firstPlace =
                      providingPlacesData[0] as Map<String, dynamic>?;
                  if (firstPlace != null) {
                    sProvidingPlaceId = firstPlace['id'] as int? ?? 0;
                    hospitalName =
                        firstPlace['name'] as String? ??
                        localizations.naHospital;

                    // Parse queues from this first providing place
                    if (firstPlace['queues'] != null &&
                        firstPlace['queues'] is List) {
                      doctorQueues =
                          (firstPlace['queues'] as List).map((queueData) {
                            return ModelQueue.fromJson(
                              queueData as Map<String, dynamic>,
                            );
                          }).toList();
                    }
                  }
                }

                // Determine fees from services
                final servicesData = data['services'] as List<dynamic>?;
                double feesValue = 25.0;
                List<ModelService> doctorServices = [];

                if (servicesData != null && servicesData.isNotEmpty) {
                  doctorServices =
                      servicesData.map((serviceData) {
                        final serviceMap = serviceData as Map<String, dynamic>;
                        return ModelService.fromJson(serviceMap);
                      }).toList();
                  final firstServiceMap =
                      servicesData[0] as Map<String, dynamic>?;
                  if (firstServiceMap != null &&
                      firstServiceMap['pointsRequirements'] != null) {
                    feesValue =
                        (firstServiceMap['pointsRequirements'] as num)
                            .toDouble();
                  }
                }

                // Parse and format nearestSlot
                String? nearestSlotText;
                final nearestSlotData =
                    data['nearestSlot'] as Map<String, dynamic>?;
                if (nearestSlotData != null &&
                    nearestSlotData['datetime'] != null) {
                  try {
                    final dateTimeUtc = DateTime.parse(
                      nearestSlotData['datetime'] as String,
                    );
                    nearestSlotText = DateFormat(
                      'MMM d, hh:mm a',
                    ).format(dateTimeUtc.toLocal());
                  } catch (e) {
                    print("Error parsing nearestSlot datetime: $e");
                    nearestSlotText = localizations.slotInfoUnavailable;
                  }
                }

                String imageAsset = "default_doctor.png";
                int colorIndex =
                    (firstName + lastName).hashCode.abs() %
                    _providerImageColors.length;
                Color bgColor = _providerImageColors[colorIndex];

                if (specialization.toLowerCase().contains("cardiac")) {
                  imageAsset = "doctor_cardiac.png";
                } else if (specialization.toLowerCase().contains("neuro")) {
                  imageAsset = "doctor_neuro.png";
                } else {
                  imageAsset = "doctor_general.png";
                }

                final String? phoneNumber = data['phone'] as String?;

                return ModelDoctor(
                  id: data['id'] as int? ?? 0,
                  imageAsset: imageAsset,
                  name: doctorName,
                  specialization: specialization,
                  hospital: hospitalName,
                  experience: data['experienceYears'] as int? ?? 5,
                  fees: feesValue,
                  rating: (data['averageRating'] as num?)?.toDouble() ?? 4.0,
                  reviewCount: data['reviewCount'] as int? ?? 10,
                  imageBackgroundColor: bgColor,
                  nearestSlotDisplay:
                      nearestSlotText, // Pass formatted slot info
                  sProvidingPlaceId: sProvidingPlaceId, // Added
                  services: doctorServices, // Added
                  queues: doctorQueues, // Pass the parsed queues
                  phone: phoneNumber, // Pass the parsed phone number
                );
              }).toList();
        });
      } else {
        String errorMessageFramework = localizations
            .errorFailedToFetchProviders(response.statusCode.toString());
        String serverMessage = "";
        try {
          final errorData = jsonDecode(response.body) as Map<String, dynamic>;
          serverMessage = errorData['message'] as String? ?? "";
        } catch (_) {}
        showCustomSnackBar(
          context,
          serverMessage.isNotEmpty
              ? "$errorMessageFramework: $serverMessage"
              : errorMessageFramework,
          CustomSnackBarType.error,
          backgroundColor: daltiErrorRed,
          textColor: daltiTextOnPrimary,
        );
        setState(() {
          _doctors = [];
        });
      }
    } catch (e) {
      if (!mounted) return;
      showCustomSnackBar(
        context,
        localizations.errorFetchingData(e.toString()),
        CustomSnackBarType.error,
        backgroundColor: daltiErrorRed,
        textColor: daltiTextOnPrimary,
      );
      setState(() {
        _doctors = [];
      });
    } finally {
      if (!mounted) return;
      setState(() {
        _isLoadingApiCall = false;
      });
    }
  }

  void _showFilterModal() async {
    final localizations =
        AppLocalizations.of(context)!; // For passing to modal if needed
    final FilterResult? result = await showModalBottomSheet<FilterResult>(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent, // For rounded corners modal
      builder: (BuildContext context) {
        return FilterModalWidget(
          initialCategoryId: _selectedCategoryId,
          initialCategoryName: _selectedCategoryName,
          initialCity: _selectedCity,
          // TODO: Pass themed colors to FilterModalWidget if needed
        );
      },
    );

    if (result != null) {
      setState(() {
        _selectedCategoryId = result.categoryId;
        _selectedCategoryName = result.categoryName;
        _selectedCity = result.city;
      });
      _fetchProviders(
        localizations: localizations, // Pass localizations
        query: searchController.text.trim(),
        categoryId: _selectedCategoryId,
        city: _selectedCity,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    final localizations = AppLocalizations.of(context)!;
    return WillPopScope(
      onWillPop: () async {
        Constant.backToPrev(context);
        return false;
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: Colors.grey[60],
        body: SafeArea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildHeader(context),
              _buildSearchBar(context, localizations),
              _buildFilterButtons(context),
              Expanded(
                child:
                    _isLoadingApiCall
                        ? const Center(child: CircularProgressIndicator())
                        : _doctors.isEmpty
                        ? Center(
                          child: Padding(
                            padding: const EdgeInsets.all(20.0),
                            child: getCustomFont(
                              _buildNoResultsText(localizations),
                              16,
                              appTextMuted,
                              3,
                              textAlign: TextAlign.center,
                            ),
                          ),
                        )
                        : ListView.builder(
                          padding: EdgeInsets.symmetric(
                            horizontal: FetchPixels.getPixelWidth(10),
                          ),
                          itemCount: _doctors.length,
                          itemBuilder: (context, index) {
                            return _buildProviderCard(_doctors[index]);
                          },
                        ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(Icons.arrow_back_ios_new_rounded, size: 20),
            ),
          ),
          const SizedBox(width: 20),
          const Text(
            'Search Results',
            style: TextStyle(fontSize: 24, fontWeight: FontWeight.w600),
          ),
          const Spacer(),
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(Icons.menu_rounded, size: 20),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context, AppLocalizations localizations) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Container(
        height: 50,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Center(
          child: TextField(
            controller: searchController,
            textAlignVertical: TextAlignVertical.center,
            decoration: InputDecoration(
              hintText: 'Search clinics or doctors',
              hintStyle: TextStyle(color: Colors.grey[600], fontSize: 16),
              prefixIcon: Icon(
                Icons.search_rounded,
                color: Colors.grey[600],
                size: 24,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.only(right: 20),
              isDense: true,
            ),
            style: TextStyle(color: Colors.grey[800], fontSize: 16),
            onSubmitted: (value) {
              _fetchProviders(
                localizations: localizations,
                query: value.trim(),
                categoryId: _selectedCategoryId,
                city: _selectedCity,
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildFilterButtons(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 20),
      child: Row(
        children: [
          _buildFilterButton(
            'All Filters',
            Icons.filter_list_rounded,
            onTap: _showFilterModal,
          ),
          _buildFilterButton(
            'Availability',
            Icons.calendar_today_rounded,
            onTap: () {},
          ),
          _buildFilterButton(
            'Specialty',
            Icons.medical_services_rounded,
            onTap: () {},
          ),
        ],
      ),
    );
  }

  Widget _buildFilterButton(
    String text,
    IconData icon, {
    required VoidCallback onTap,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(8),
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey[300]!),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(icon, size: 18, color: Colors.grey[700]),
                  const SizedBox(width: 8),
                  Text(
                    text,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey[700],
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  String _buildResultsHeaderText(AppLocalizations localizations) {
    final String query = _currentSearchQuery;
    final int count = _doctors.length;
    final String category = _selectedCategoryName ?? "";
    final String city = _selectedCity ?? "";

    if (category.isNotEmpty && city.isNotEmpty) {
      return localizations.searchResultCountForQueryInCategoryInCity(
        category,
        city,
        count,
        query,
      );
    } else if (category.isNotEmpty) {
      return localizations.searchResultCountForQueryInCategory(
        category,
        count,
        query,
      );
    } else if (city.isNotEmpty) {
      return localizations.searchResultCountForQueryInCity(city, count, query);
    } else {
      return localizations.searchResultCountForQuery(count, query);
    }
  }

  String _buildNoResultsText(AppLocalizations localizations) {
    String query = _currentSearchQuery;
    String category = _selectedCategoryName ?? "";
    String city = _selectedCity ?? "";

    if (category.isNotEmpty && city.isNotEmpty) {
      return localizations.noProvidersFoundForQueryWithCategoryAndCity(
        query,
        category,
        city,
      );
    } else if (category.isNotEmpty) {
      return localizations.noProvidersFoundForQueryWithCategory(
        query,
        category,
      );
    } else if (city.isNotEmpty) {
      return localizations.noProvidersFoundForQueryWithCity(query, city);
    } else {
      return localizations.noProvidersFoundForQuery(query);
    }
  }

  Widget _buildProviderCard(ModelDoctor doctor) {
    return Container(
      margin: const EdgeInsets.only(bottom: 20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap:
              () => Navigator.pushNamed(
                context,
                Routes.detailRoute,
                arguments: doctor,
              ),
          borderRadius: BorderRadius.circular(12),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Top section with icon and all text info
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Provider icon
                    Container(
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        color: Colors.grey[200],
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Image.asset(
                          Constant.assetImagePath + doctor.imageAsset,
                          width: 50,
                          height: 50,
                          errorBuilder:
                              (context, error, stackTrace) => Icon(
                                Icons.medical_services_rounded,
                                size: 40,
                                color: Colors.grey[600],
                              ),
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    // All text information stacked vertically
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Name
                          Text(
                            doctor.name,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 4),
                          // Specialty
                          Text(
                            doctor.specialization,
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey[600],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                          const SizedBox(height: 8),
                          // Rating and reviews
                          Row(
                            children: [
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: List.generate(5, (index) {
                                  return Icon(
                                    index < doctor.rating.floor()
                                        ? Icons.star_rounded
                                        : Icons.star_border_rounded,
                                    color: const Color(0xFFFFB23F),
                                    size: 16,
                                  );
                                }),
                              ),
                              const SizedBox(width: 8),
                              Text(
                                '(${doctor.reviewCount})',
                                style: TextStyle(
                                  fontSize: 12,
                                  color: Colors.grey[600],
                                ),
                              ),
                            ],
                          ),
                          // Next available slot
                          if (doctor.nearestSlotDisplay != null) ...[
                            const SizedBox(height: 8),
                            Row(
                              children: [
                                Icon(
                                  Icons.access_time_rounded,
                                  size: 16,
                                  color: Colors.grey[600],
                                ),
                                const SizedBox(width: 4),
                                Expanded(
                                  child: Row(
                                    children: [
                                      Text(
                                        'Next Available: ',
                                        style: TextStyle(
                                          fontSize: 13,
                                          color: Colors.grey[600],
                                        ),
                                      ),
                                      Flexible(
                                        child: Text(
                                          doctor.nearestSlotDisplay!,
                                          style: const TextStyle(
                                            fontSize: 13,
                                            fontWeight: FontWeight.w500,
                                            color: Color(0xFF2F80ED),
                                          ),
                                          maxLines: 1,
                                          overflow: TextOverflow.ellipsis,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // Bottom row with location and book button
                Row(
                  children: [
                    Expanded(
                      child: Row(
                        children: [
                          Icon(
                            Icons.location_on_outlined,
                            size: 16,
                            color: Colors.grey[600],
                          ),
                          const SizedBox(width: 4),
                          Flexible(
                            child: Text(
                              doctor.hospital,
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    ElevatedButton(
                      onPressed: () {},
                      style: ElevatedButton.styleFrom(
                        backgroundColor: daltiPrimary,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24,
                          vertical: 12,
                        ),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'Book Now',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

// Define _FilterModalWidget as a separate StatefulWidget - MOVED to filter_modal_widget.dart
// class _FilterModalWidget extends StatefulWidget { ... }
// class _FilterModalWidgetState extends State<_FilterModalWidget> { ... }
