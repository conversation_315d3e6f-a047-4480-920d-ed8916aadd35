name: dalti
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter
  firebase_core: ^2.32.0
  firebase_messaging: ^14.9.3
  flutter_local_notifications: ^17.2.1
  
  shared_preferences: any
  flutter_svg: any
  pinput: any
  # carousel_slider: any
  dots_indicator: any
  web_socket_channel: any
  flutter_localizations:
    sdk: flutter
  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  http: any
  dotted_line: any
  syncfusion_flutter_datepicker: any
  intl: 0.20.2
  flutter_masked_text2: any
  flutter_staggered_animations: any
  table_calendar: ^3.0.9
  url_launcher: any
  qr_flutter: ^4.1.0
  geolocator: any
  google_maps_flutter: any
  google_fonts: ^6.1.0
dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^5.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  generate: true
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/images/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Cairo
      fonts:
        - asset: fonts/Cairo-ExtraLight.ttf
          weight: 200
        - asset: fonts/Cairo-Light.ttf
          weight: 300
        - asset: fonts/Cairo-Regular.ttf
          weight: 400
        - asset: fonts/Cairo-Medium.ttf
          weight: 500
        - asset: fonts/Cairo-SemiBold.ttf
          weight: 600
        - asset: fonts/Cairo-Bold.ttf
          weight: 700
        - asset: fonts/Cairo-ExtraBold.ttf
          weight: 800
        - asset: fonts/Cairo-Black.ttf
          weight: 900
        # No Italic versions for Cairo seem to be present in your list

    - family: Lato
      fonts:
        - asset: fonts/Lato-Hairline.ttf
          weight: 100
        - asset: fonts/Lato-HairlineItalic.ttf
          weight: 100
          style: italic
        - asset: fonts/Lato-Thin.ttf
          weight: 200 # Standard 'Thin' is often w100 or w200, mapping to w200 as ExtraLight is taken by Cairo
        - asset: fonts/Lato-ThinItalic.ttf
          weight: 200
          style: italic
        - asset: fonts/Lato-Light.ttf
          weight: 300
        - asset: fonts/Lato-LightItalic.ttf
          weight: 300
          style: italic
        - asset: fonts/Lato-Regular.ttf
          weight: 400
        - asset: fonts/Lato-Italic.ttf
          weight: 400 # Default weight for a style: italic if not specified
          style: italic
        - asset: fonts/Lato-Medium.ttf
          weight: 500
        - asset: fonts/Lato-MediumItalic.ttf
          weight: 500
          style: italic
        - asset: fonts/Lato-Semibold.ttf
          weight: 600
        - asset: fonts/Lato-SemiboldItalic.ttf
          weight: 600
          style: italic
        - asset: fonts/Lato-Bold.ttf
          weight: 700
        - asset: fonts/Lato-BoldItalic.ttf
          weight: 700
          style: italic
        - asset: fonts/Lato-Heavy.ttf # Heavy is often mapped to 800 or 900
          weight: 900 # Using 900 as it's a common value for "Heavy" or "Black"
        - asset: fonts/Lato-HeavyItalic.ttf
          weight: 900
          style: italic
        - asset: fonts/Lato-Black.ttf
          weight: 900
        - asset: fonts/Lato-BlackItalic.ttf
          weight: 900
          style: italic

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
