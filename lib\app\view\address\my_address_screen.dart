import 'package:dalti/app/models/model_address.dart';
import 'package:dalti/app/routes/app_routes.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../base/color_data.dart';
import '../../../base/constant.dart';
import '../../../../services/address_service.dart';
import './edit_address_screen.dart';

class MyAddressScreen extends StatefulWidget {
  const MyAddressScreen({Key? key}) : super(key: key);

  @override
  State<MyAddressScreen> createState() => _MyAddressScreenState();
}

class _MyAddressScreenState extends State<MyAddressScreen> {
  List<ModelAddress> addressList = [];
  SharedPreferences? selection;
  bool _isLoading = true;
  final AddressService _addressService = AddressService();

  @override
  void initState() {
    super.initState();

    SharedPreferences.getInstance().then((SharedPreferences sp) {
      selection = sp;
      setState(() {});
    });
    _fetchAddresses();
  }

  Future<void> _fetchAddresses() async {
    setState(() {
      _isLoading = true;
    });
    try {
      // No arguments needed for getCustomerAddresses
      final addresses = await _addressService.getCustomerAddresses();
      if (mounted) {
        setState(() {
          addressList = addresses;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to load addresses: ${e.toString()}')),
        );
        setState(() {
          addressList = [];
        });
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _deleteAddress(String addressId) async {
    // Show confirmation dialog
    bool? confirmDelete = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete Address'),
          content: const Text('Are you sure you want to delete this address?'),
          actions: <Widget>[
            TextButton(
              child: const Text('Cancel'),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            TextButton(
              child: Text('Delete', style: TextStyle(color: daltiErrorRed)),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    );

    if (confirmDelete == true) {
      if (mounted) {
        setState(() {
          _isLoading = true; // Show loader while deleting
        });
      }
      try {
        // Only addressId is needed for deleteCustomerAddress
        await _addressService.deleteCustomerAddress(addressId);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Address deleted successfully')),
          );
        }
        _fetchAddresses(); // Refresh the list
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to delete address: ${e.toString()}'),
            ),
          );
          setState(() {
            _isLoading = false; // Hide loader on error
          });
        }
      } finally {
        if (mounted && _isLoading) {
          // Ensure loader is hidden if delete didn't complete fully but started
          setState(() {
            _isLoading = false;
          });
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    return WillPopScope(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: daltiBackground,
        body: SafeArea(
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: FetchPixels.getPixelWidth(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                getVerSpace(FetchPixels.getPixelHeight(20)),
                gettoolbarMenu(
                  context,
                  "back.svg",
                  () {
                    Constant.backToPrev(context);
                  },
                  istext: true,
                  title: "My Address",
                  weight: FontWeight.w800,
                  fontsize: 24,
                  textColor: daltiTextHeadline,
                ),
                if (_isLoading)
                  const Expanded(
                    child: Center(child: CircularProgressIndicator()),
                  )
                else if (addressList.isEmpty)
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          height: FetchPixels.getPixelHeight(124),
                          width: FetchPixels.getPixelHeight(124),
                          decoration: BoxDecoration(
                            image: getDecorationAssetImage(
                              context,
                              'home_address.png',
                            ),
                          ),
                        ),
                        getVerSpace(FetchPixels.getPixelHeight(40)),
                        getCustomFont(
                          "No Address Yet!",
                          20,
                          daltiTextHeadline,
                          1,
                          fontWeight: FontWeight.w800,
                        ),
                        getVerSpace(FetchPixels.getPixelHeight(10)),
                        getCustomFont(
                          "Add your address and lets get started.",
                          16,
                          daltiTextBody,
                          1,
                          fontWeight: FontWeight.w400,
                        ),
                        getVerSpace(FetchPixels.getPixelHeight(30)),
                        getButton(
                          context,
                          daltiCard,
                          "Add Address",
                          daltiPrimary,
                          () {
                            Constant.sendToNext(
                              context,
                              Routes.editAddressRoute,
                              arguments: null,
                            );
                          },
                          18,
                          weight: FontWeight.w600,
                          buttonHeight: FetchPixels.getPixelHeight(60),
                          insetsGeometry: EdgeInsets.symmetric(
                            horizontal: FetchPixels.getPixelWidth(98),
                          ),
                          borderRadius: BorderRadius.circular(
                            FetchPixels.getPixelHeight(14),
                          ),
                          isBorder: true,
                          borderColor: daltiPrimary,
                          borderWidth: 1.5,
                        ),
                      ],
                    ),
                  )
                else
                  Expanded(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Column(
                          children: [
                            getVerSpace(FetchPixels.getPixelHeight(30)),
                            Align(
                              alignment: Alignment.topLeft,
                              child: getCustomFont(
                                "Your addresses",
                                16,
                                daltiTextBody,
                                1,
                                fontWeight: FontWeight.w400,
                              ),
                            ),
                            getVerSpace(FetchPixels.getPixelHeight(20)),
                            ListView.builder(
                              padding: EdgeInsets.zero,
                              scrollDirection: Axis.vertical,
                              physics: const BouncingScrollPhysics(),
                              shrinkWrap: true,
                              itemCount: addressList.length,
                              itemBuilder: (context, index) {
                                ModelAddress modelAddress = addressList[index];
                                return Container(
                                  margin: EdgeInsets.only(
                                    bottom: FetchPixels.getPixelHeight(20),
                                  ),
                                  padding: EdgeInsets.only(
                                    top: FetchPixels.getPixelHeight(16),
                                    bottom: FetchPixels.getPixelHeight(16),
                                    left: FetchPixels.getPixelWidth(16),
                                    right: FetchPixels.getPixelWidth(16),
                                  ),
                                  decoration: BoxDecoration(
                                    color: daltiCard,
                                    boxShadow: [
                                      BoxShadow(
                                        color: daltiDividerLine.withOpacity(
                                          0.3,
                                        ),
                                        blurRadius: 8,
                                        offset: const Offset(0.0, 3.0),
                                      ),
                                    ],
                                    borderRadius: BorderRadius.circular(
                                      FetchPixels.getPixelHeight(12),
                                    ),
                                  ),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Expanded(
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            getCustomFont(
                                              modelAddress.address ?? "",
                                              16,
                                              daltiTextBody,
                                              1,
                                              fontWeight: FontWeight.bold,
                                            ),
                                            getVerSpace(
                                              FetchPixels.getPixelHeight(10),
                                            ),
                                            SizedBox(
                                              width: FetchPixels.getPixelWidth(
                                                MediaQuery.of(
                                                      context,
                                                    ).size.width *
                                                    0.6,
                                              ),
                                              child: getMultilineCustomFont(
                                                "${modelAddress.city ?? ''}, ${modelAddress.postalCode ?? ''}, ${modelAddress.country ?? ''}",
                                                16,
                                                daltiTextMuted,
                                                fontWeight: FontWeight.w400,
                                                txtHeight: 1.3,
                                              ),
                                            ),
                                            getVerSpace(
                                              FetchPixels.getPixelHeight(10),
                                            ),
                                            if (modelAddress.phone != null &&
                                                modelAddress.phone!.isNotEmpty)
                                              getCustomFont(
                                                modelAddress.phone ?? "",
                                                16,
                                                daltiTextMuted,
                                                1,
                                                fontWeight: FontWeight.w400,
                                              ),
                                            if (modelAddress.description !=
                                                    null &&
                                                modelAddress
                                                    .description!
                                                    .isNotEmpty) ...[
                                              getVerSpace(
                                                FetchPixels.getPixelHeight(10),
                                              ),
                                              getCustomFont(
                                                "Description: ${modelAddress.description!}",
                                                14,
                                                daltiTextMuted,
                                                1,
                                                fontWeight: FontWeight.w400,
                                              ),
                                            ],
                                          ],
                                        ),
                                      ),
                                      PopupMenuButton<int>(
                                        onSelected: (value) {
                                          if (value == 2) {
                                            if (modelAddress.name != null) {
                                              _deleteAddress(
                                                modelAddress.name!,
                                              );
                                            } else {
                                              ScaffoldMessenger.of(
                                                context,
                                              ).showSnackBar(
                                                const SnackBar(
                                                  content: Text(
                                                    'Address ID is missing, cannot delete.',
                                                  ),
                                                ),
                                              );
                                            }
                                          }
                                          if (value == 1) {
                                            print(
                                              "MyAddressScreen: Navigating to EditAddressScreen. Street: ${modelAddress.street}, City: ${modelAddress.city}, ID(name): ${modelAddress.name}",
                                            );
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder:
                                                    (context) =>
                                                        EditAddressScreen(
                                                          address: modelAddress,
                                                        ),
                                                settings: RouteSettings(
                                                  name: Routes.editAddressRoute,
                                                ),
                                              ),
                                            );
                                          }
                                        },
                                        padding: EdgeInsets.only(
                                          top: FetchPixels.getPixelHeight(0),
                                          right: FetchPixels.getPixelWidth(0),
                                        ),
                                        icon: getSvgImage(
                                          "more_vert.svg",
                                          color: daltiIconDefault,
                                          width: FetchPixels.getPixelHeight(24),
                                          height: FetchPixels.getPixelHeight(
                                            24,
                                          ),
                                        ),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                            FetchPixels.getPixelHeight(12),
                                          ),
                                        ),
                                        itemBuilder:
                                            (context) => [
                                              PopupMenuItem(
                                                child: Text(
                                                  "Edit",
                                                  style: TextStyle(
                                                    color: daltiTextBody,
                                                  ),
                                                ),
                                                value: 1,
                                              ),
                                              const PopupMenuDivider(height: 0),
                                              PopupMenuItem(
                                                child: Text(
                                                  "Delete",
                                                  style: TextStyle(
                                                    color: daltiErrorRed,
                                                  ),
                                                ),
                                                value: 2,
                                              ),
                                            ],
                                        offset: const Offset(0, 40),
                                      ),
                                    ],
                                  ),
                                );
                              },
                            ),
                          ],
                        ),
                        addAddressButton(context),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
      onWillPop: () async {
        Constant.backToPrev(context);

        return false;
      },
    );
  }

  Widget addAddressButton(BuildContext context) {
    return Column(
      children: [
        getButton(
          context,
          daltiPrimary,
          "Add New Address",
          daltiTextOnPrimary,
          () {
            Constant.sendToNext(
              context,
              Routes.editAddressRoute,
              arguments: null,
            );
          },
          18,
          weight: FontWeight.w600,
          buttonHeight: FetchPixels.getPixelHeight(60),
          borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(14)),
        ),
        getVerSpace(FetchPixels.getPixelHeight(30)),
      ],
    );
  }
}
