import 'package:dalti/app/routes/app_routes.dart';
import 'package:dalti/base/color_data.dart';
import 'package:dalti/base/constant.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dalti/l10n/app_localizations.dart';

const String prefsKeyPreferredMethod =
    'preferred_method'; // 'email' or 'mobile'

class PreferredMethodScreen extends StatefulWidget {
  const PreferredMethodScreen({Key? key}) : super(key: key);

  @override
  State<PreferredMethodScreen> createState() => _PreferredMethodScreenState();
}

class _PreferredMethodScreenState extends State<PreferredMethodScreen> {
  Future<void> _selectPreferenceAndNavigate(String method) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(prefsKeyPreferredMethod, method);
    // Navigate to LoginScreen, passing the selected method
    Constant.sendToNext(
      context,
      Routes.loginRoute,
      arguments: {'preferred_method': method},
    );
  }

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: daltiBackground,
      body: SafeArea(
        child: Container(
          padding: EdgeInsets.symmetric(
            horizontal: FetchPixels.getDefaultHorSpace(context),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              getCustomFont(
                localizations.preferredMethodTitle,
                24,
                daltiTextHeadline,
                1,
                fontWeight: FontWeight.w800,
                textAlign: TextAlign.center,
              ),
              getVerSpace(FetchPixels.getPixelHeight(15)),
              getCustomFont(
                localizations.preferredMethodSubtitle,
                16,
                daltiTextBody,
                2,
                fontWeight: FontWeight.w400,
                textAlign: TextAlign.center,
              ),
              getVerSpace(FetchPixels.getPixelHeight(50)),
              getButton(
                context,
                daltiPrimary,
                localizations.useEmailButton,
                daltiTextOnPrimary,
                () => _selectPreferenceAndNavigate('email'),
                18,
                weight: FontWeight.w600,
                buttonHeight: FetchPixels.getPixelHeight(60),
                borderRadius: BorderRadius.circular(
                  FetchPixels.getPixelHeight(15),
                ),
              ),
              getVerSpace(FetchPixels.getPixelHeight(20)),
              getButton(
                context,
                daltiCard, // Secondary button style
                localizations.useMobileButton,
                daltiPrimary, // Text color for secondary button
                () => _selectPreferenceAndNavigate('mobile'),
                18,
                weight: FontWeight.w600,
                buttonHeight: FetchPixels.getPixelHeight(60),
                borderRadius: BorderRadius.circular(
                  FetchPixels.getPixelHeight(15),
                ),
                boxShadow: [
                  BoxShadow(
                    color: daltiDividerLine.withOpacity(0.4),
                    blurRadius: 8,
                    offset: const Offset(0.0, 2.0),
                  ),
                ],
              ),
              getVerSpace(FetchPixels.getPixelHeight(60)),
              GestureDetector(
                onTap: () {
                  // Allow users to go back to intro or skip if that's desired
                  // For now, let's assume direct navigation or closing the app
                  // if they don't want to choose.
                  // If there's an IntroScreen, they might want to go back there.
                  // This could be handled by Navigator.pop(context); if appropriate.
                  // Or, if this is the first screen after intro, maybe an exit option.
                  // For now, we'll just print a message.
                  print("User chose to go back or skip preference selection.");
                  // Constant.backToPrev(context); // Uncomment if you have a screen to go back to
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.arrow_back_ios, size: 16, color: daltiTextMuted),
                    getHorSpace(FetchPixels.getPixelWidth(5)),
                    getCustomFont(
                      localizations.goBackButton,
                      16,
                      daltiTextMuted,
                      1,
                      fontWeight: FontWeight.w600,
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
