// import 'dart:io'; // Removed to use platform-aware utility
import 'package:dalti/app/utils/locale_utils.dart'; // Added for platform-aware locale
import 'package:dalti/app/routes/app_pages.dart';
import 'package:dalti/app/routes/app_routes.dart';
import 'package:flutter/material.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
// import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:dalti/l10n/app_localizations.dart';
import 'firebase_options.dart';
import 'package:shared_preferences/shared_preferences.dart';
// import 'package:dalti/app/screens/language_selection_screen.dart'; // Removed LanguageSelectionScreen
import 'package:dalti/app/view/splash_screen.dart';
// Comment out or remove google_fonts if only using local fonts now
// import 'package:google_fonts/google_fonts.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    print('Firebase initialized successfully');
  } catch (e) {
    print('Firebase initialization failed: $e');
    // Continue anyway to see if the app can run without Firebase
  }

  runApp(const MyApp());
}

class MyApp extends StatefulWidget {
  const MyApp({Key? key}) : super(key: key);

  static void setLocale(BuildContext context, Locale newLocale) {
    _MyAppState? state = context.findAncestorStateOfType<_MyAppState>();
    state?.changeLocale(newLocale);
  }

  @override
  State<MyApp> createState() => _MyAppState();
}

class _MyAppState extends State<MyApp> {
  Locale? _locale;
  bool _isLoadingLocale = true;
  bool _splashDone = false;

  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    await Future.delayed(const Duration(seconds: 3));
    final prefs = await SharedPreferences.getInstance();
    String? languageCode = prefs.getString('language_code');
    // String? countryCode = prefs.getString('country_code');

    if (languageCode == null) {
      // No language saved, determine from OS/Platform
      String osLocaleName = getPlatformLocaleName(); // Use the utility function
      String osLang =
          osLocaleName
              .split('_')
              .first
              .split('-')
              .first; // "en", "ar", etc. Handles "en-US" and "en_US"

      if (['en', 'ar', 'fr'].contains(osLang)) {
        languageCode = osLang;
      } else {
        languageCode = 'en'; // Default to English
      }
      // countryCode = null; // Or determine based on osLocaleName if needed

      // Save the determined locale
      await prefs.setString('language_code', languageCode);
    }

    if (mounted) {
      setState(() {
        _locale = Locale(languageCode!);
        _isLoadingLocale = false;
        _splashDone = true;
      });
    }
  }

  void changeLocale(Locale locale) {
    setState(() {
      _locale = locale;
    });
  }

  @override
  Widget build(BuildContext context) {
    bool isArabic = _locale?.languageCode.toLowerCase() == 'ar';
    String activeFontFamily =
        isArabic ? 'Cairo' : 'Lato'; // Using local font family names

    // Create a base text theme, then apply the family.
    TextTheme baseTypography = Typography.englishLike2021;
    TextTheme activeTextTheme = baseTypography.apply(
      fontFamily: activeFontFamily,
    );
    // if (!_splashDone) {
    return MaterialApp(
      home: const SplashScreen(),
      debugShowCheckedModeBanner: false,
      locale: _locale,
      localizationsDelegates: [
        AppLocalizations.delegate,
        GlobalMaterialLocalizations.delegate,
        GlobalWidgetsLocalizations.delegate,
        GlobalCupertinoLocalizations.delegate,
      ],
      theme: ThemeData(
        fontFamily: activeFontFamily,
        textTheme: activeTextTheme,
      ),
      supportedLocales: AppLocalizations.supportedLocales,
      onGenerateRoute: AppPages.onGenerateRoute,
    );
    // }

    // try {
    //   print('DEBUG: AppLocalizations.delegate: ${AppLocalizations.delegate}');
    //   print(
    //     'DEBUG: AppLocalizations.supportedLocales: ${AppLocalizations.supportedLocales}',
    //   );
    // } catch (e) {
    //   print('DEBUG: Error accessing AppLocalizations static members: $e');
    // }

    // return MaterialApp(
    //   debugShowCheckedModeBanner: false,
    //   locale: _locale,
    //   localizationsDelegates: [
    //     AppLocalizations.delegate,
    //     GlobalMaterialLocalizations.delegate,
    //     GlobalWidgetsLocalizations.delegate,
    //     GlobalCupertinoLocalizations.delegate,
    //   ],
    //   supportedLocales: AppLocalizations.supportedLocales,
    //   theme: ThemeData(
    //     fontFamily: activeFontFamily,
    //     textTheme: activeTextTheme,
    //   ),
    //   initialRoute: Routes.introRoute,
    //   onGenerateRoute: AppPages.onGenerateRoute,
    // );
  }
}
