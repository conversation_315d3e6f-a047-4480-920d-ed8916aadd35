import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:dalti/app/routes/app_routes.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dalti/l10n/app_localizations.dart'; // Import AppLocalizations

import '../../../base/color_data.dart';
import '../../../base/constant.dart';
import '../../../base/widget_utils.dart';
import '../login/prefered_method.dart'; // Import for prefsKeyPreferredMethod

class SignUpScreen extends StatefulWidget {
  final String? preferredMethod; // Added to accept argument

  const SignUpScreen({Key? key, this.preferredMethod}) : super(key: key);

  @override
  State<SignUpScreen> createState() => _SignUpScreenState();
}

class _SignUpScreenState extends State<SignUpScreen> {
  final _formKey = GlobalKey<FormState>();
  String? _currentPreferredMethod;

  void finishView() {
    // Go back to LoginScreen, passing the current preferred method
    Constant.sendToNext(
      context,
      Routes.loginRoute,
      arguments: {'preferred_method': _currentPreferredMethod},
    );
  }

  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController phoneNumberController = TextEditingController();
  SharedPreferences? selection;
  bool agree = false;
  bool _isCheckingUser = false;

  TextEditingController passwordController = TextEditingController();
  bool ispass = true;

  @override
  void initState() {
    super.initState();
    // Initialize preferred method from widget argument or SharedPreferences
    _initializePreferredMethod();
    SharedPreferences.getInstance().then((SharedPreferences sp) {
      selection = sp;
      if (mounted) {
        setState(() {});
      }
    });
  }

  Future<void> _initializePreferredMethod() async {
    if (widget.preferredMethod != null) {
      _currentPreferredMethod = widget.preferredMethod;
    } else {
      final prefs = await SharedPreferences.getInstance();
      // Fallback if not passed via argument, though it should be.
      _currentPreferredMethod = prefs.getString(prefsKeyPreferredMethod);
    }
    if (mounted) {
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    final localizations = AppLocalizations.of(context)!; // Get localizations

    // If preferredMethod comes from arguments and differs, update _currentPreferredMethod
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    if (args != null &&
        args['preferred_method'] != null &&
        _currentPreferredMethod != args['preferred_method']) {
      _currentPreferredMethod = args['preferred_method'] as String?;
      // Optionally clear fields if the method context changes, though less likely here than in login.
    }

    bool isEmailPreferred = _currentPreferredMethod == 'email';
    bool isMobilePreferred = _currentPreferredMethod == 'mobile';

    String subtitle;
    if (isEmailPreferred) {
      subtitle = localizations.signUpSubtitleEmail;
    } else if (isMobilePreferred) {
      subtitle = localizations.signUpSubtitleMobile;
    } else {
      subtitle = localizations.signUpSubtitleGeneric;
    }

    return WillPopScope(
      onWillPop: () async {
        finishView(); // Custom back navigation
        return false;
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: daltiBackground,
        body: SafeArea(
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: FetchPixels.getPixelWidth(20),
            ),
            child: Form(
              key: _formKey,
              child: ListView(
                // Changed to ListView to prevent overflow
                children: [
                  getVerSpace(FetchPixels.getPixelHeight(26)),
                  gettoolbarMenu(context, "back.svg", () {
                    finishView();
                  }),
                  getVerSpace(FetchPixels.getPixelHeight(22)),
                  getCustomFont(
                    localizations.signUpTitle, // Use localized string
                    24,
                    daltiTextHeadline,
                    1,
                    fontWeight: FontWeight.w800,
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(10)),
                  getCustomFont(
                    subtitle, // Use dynamic localized subtitle
                    16,
                    daltiTextBody,
                    1,
                    fontWeight: FontWeight.w400,
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(30)),
                  getDefaultTextFiledWithLabel(
                    context,
                    localizations.firstNameLabel, // Use localized string
                    firstNameController,
                    daltiTextMuted,
                    function: () {},
                    height: FetchPixels.getPixelHeight(60),
                    isEnable: false,
                    withprefix: true,
                    image: "user.svg",
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(20)),
                  getDefaultTextFiledWithLabel(
                    context,
                    localizations.lastNameLabel, // Use localized string
                    lastNameController,
                    daltiTextMuted,
                    function: () {},
                    height: FetchPixels.getPixelHeight(60),
                    isEnable: false,
                    withprefix: true,
                    image: "user.svg",
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(20)),

                  // Conditional Email Field
                  if (isEmailPreferred ||
                      !isMobilePreferred) // Show if email is preferred or no preference (show both)
                    getDefaultTextFiledWithLabel(
                      context,
                      localizations.emailLabel, // Reuse from login
                      emailController,
                      daltiTextMuted,
                      function: () {},
                      height: FetchPixels.getPixelHeight(60),
                      isEnable: false,
                      withprefix: true,
                      image: "message.svg",
                    ),
                  if (isEmailPreferred || !isMobilePreferred)
                    getVerSpace(FetchPixels.getPixelHeight(20)),

                  // Conditional Phone Field
                  if (isMobilePreferred ||
                      !isEmailPreferred) // Show if mobile is preferred or no preference (show both)
                    GestureDetector(
                      onTap: () async {
                        await Constant.sendToNext(
                          context,
                          Routes.selectCountryRoute,
                        );
                        setState(() {});
                      },
                      child: getCountryTextField(
                        context,
                        localizations.phoneNumberLabel, // Reuse from login
                        phoneNumberController,
                        daltiTextMuted,
                        selection?.getString("code") ?? "+213",
                        function: () {},
                        height: FetchPixels.getPixelHeight(60),
                        isEnable: false,
                        minLines: true,
                        image:
                            selection?.getString("country") ??
                            "image_algeria.png",
                      ),
                    ),
                  if (isMobilePreferred || !isEmailPreferred)
                    getVerSpace(FetchPixels.getPixelHeight(20)),

                  getDefaultTextFiledWithLabel(
                    context,
                    localizations.passwordLabel, // Reuse from login
                    passwordController,
                    daltiTextMuted,
                    function: () {},
                    height: FetchPixels.getPixelHeight(60),
                    isEnable: false,
                    withprefix: true,
                    image: "lock.svg",
                    isPass: ispass,
                    imageWidth: FetchPixels.getPixelWidth(19),
                    imageHeight: FetchPixels.getPixelHeight(17.66),
                    withSufix: true,
                    suffiximage: "eye.svg",
                    imagefunction: () {
                      setState(() {
                        ispass = !ispass;
                      });
                    },
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(30)),
                  Row(
                    children: [
                      GestureDetector(
                        onTap: () {
                          setState(() {
                            agree = !agree;
                          });
                        },
                        child: Container(
                          height: FetchPixels.getPixelHeight(24),
                          width: FetchPixels.getPixelHeight(24),
                          decoration: BoxDecoration(
                            color: (agree) ? daltiPrimary : daltiCard,
                            border:
                                (agree)
                                    ? null
                                    : Border.all(
                                      color: daltiDividerLine,
                                      width: 1.5,
                                    ),
                            borderRadius: BorderRadius.circular(
                              FetchPixels.getPixelHeight(6),
                            ),
                          ),
                          padding: EdgeInsets.symmetric(
                            vertical: FetchPixels.getPixelHeight(6),
                            horizontal: FetchPixels.getPixelWidth(4),
                          ),
                          child:
                              (agree)
                                  ? getSvgImage(
                                    "done.svg",
                                    color: daltiTextOnPrimary,
                                  )
                                  : null,
                        ),
                      ),
                      getHorSpace(FetchPixels.getPixelWidth(10)),
                      Expanded(
                        // Added Expanded for long text
                        child: getCustomFont(
                          localizations
                              .agreeTermsPrivacyText, // Use localized string
                          16,
                          daltiTextBody,
                          1,
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(50)),
                  _isCheckingUser
                      ? Center(
                        child: CircularProgressIndicator(color: daltiPrimary),
                      )
                      : getButton(
                        context,
                        daltiPrimary,
                        localizations.signUpButton, // Use localized string
                        daltiTextOnPrimary,
                        () {
                          if (_formKey.currentState!.validate()) {
                            if (agree) {
                              _checkUserExistsAndProceed();
                            } else {
                              showCustomSnackBar(
                                context,
                                localizations
                                    .errorAgreeTerms, // Use localized string
                                CustomSnackBarType.warning,
                                backgroundColor: daltiWarningYellow,
                                textColor: daltiTextHeadline,
                              );
                            }
                          }
                        },
                        18,
                        weight: FontWeight.w600,
                        buttonHeight: FetchPixels.getPixelHeight(60),
                        borderRadius: BorderRadius.circular(
                          FetchPixels.getPixelHeight(15),
                        ),
                      ),
                  getVerSpace(FetchPixels.getPixelHeight(30)),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      getCustomFont(
                        localizations
                            .alreadyHaveAccountText, // Use localized string
                        14,
                        daltiTextMuted,
                        1,
                        fontWeight: FontWeight.w400,
                      ),
                      GestureDetector(
                        onTap: () {
                          finishView(); // Navigate back to Login with preference
                        },
                        child: getCustomFont(
                          localizations
                              .loginButtonSignUpScreen, // Use localized string
                          16,
                          daltiPrimary,
                          1,
                          fontWeight: FontWeight.w800,
                        ),
                      ),
                    ],
                  ),
                  getVerSpace(
                    FetchPixels.getPixelHeight(20),
                  ), // Added space at bottom for scroll
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _checkUserExistsAndProceed() async {
    final localizations = AppLocalizations.of(context)!; // Get localizations
    setState(() {
      _isCheckingUser = true;
    });

    String countryCode = selection?.getString("code") ?? "+213";
    String localPhoneNumber = phoneNumberController.text.trim();
    String fullPhoneNumber =
        localPhoneNumber.isNotEmpty ? countryCode + localPhoneNumber : "";
    String email = emailController.text.trim();

    // Validation based on preferred method
    bool isEmailPreferred = _currentPreferredMethod == 'email';
    bool isMobilePreferred = _currentPreferredMethod == 'mobile';

    if (isEmailPreferred && email.isEmpty) {
      showCustomSnackBar(
        context,
        localizations.errorEmailEmptyPreferred, // Use localized string
        CustomSnackBarType.error,
        backgroundColor: daltiErrorRed,
        textColor: daltiTextOnPrimary,
      );
      setState(() {
        _isCheckingUser = false;
      });
      return;
    }
    if (isMobilePreferred && localPhoneNumber.isEmpty) {
      showCustomSnackBar(
        context,
        localizations.errorMobileEmptyPreferred, // Use localized string
        CustomSnackBarType.error,
        backgroundColor: daltiErrorRed,
        textColor: daltiTextOnPrimary,
      );
      setState(() {
        _isCheckingUser = false;
      });
      return;
    }
    // If no preference was set (should not happen with new flow, but defensive)
    // or if both fields are optional and empty.
    if (email.isEmpty && localPhoneNumber.isEmpty) {
      showCustomSnackBar(
        context,
        localizations.errorProvideEmailOrPhone, // Use localized string
        CustomSnackBarType.error,
        backgroundColor: daltiErrorRed,
        textColor: daltiTextOnPrimary,
      );
      setState(() {
        _isCheckingUser = false;
      });
      return;
    }

    String apiUrl = 'https://dapi-test.adscloud.org:8443/api/auth/user-exists';
    Map<String, String> queryParams = {};
    // Prioritize preferred method for user-exists check if both are filled
    if (isEmailPreferred && email.isNotEmpty) {
      queryParams['email'] = email;
    } else if (isMobilePreferred && localPhoneNumber.isNotEmpty) {
      queryParams['mobileNumber'] =
          localPhoneNumber; // API expects 'mobileNumber' for phone
    } else {
      // Fallback if no preference or only one field is filled
      if (email.isNotEmpty) {
        queryParams['email'] = email;
      }
      if (localPhoneNumber.isNotEmpty) {
        queryParams['mobileNumber'] = localPhoneNumber;
      }
    }

    if (queryParams.isEmpty) {
      // Should be caught by earlier checks
      showCustomSnackBar(
        context,
        localizations.errorProvideSignUpDetails, // Use localized string
        CustomSnackBarType.error,
        backgroundColor: daltiErrorRed,
        textColor: daltiTextOnPrimary,
      );
      setState(() {
        _isCheckingUser = false;
      });
      return;
    }

    Uri uri = Uri.parse(apiUrl).replace(queryParameters: queryParams);

    try {
      final response = await http.get(
        uri,
        headers: {'Content-Type': 'application/json'},
      );
      final Map<String, dynamic>? responseData =
          response.body.isNotEmpty ? jsonDecode(response.body) : null;

      if (response.statusCode == 200) {
        if (responseData != null && responseData['exists'] == true) {
          String fieldInUse = "Identifier";
          if (queryParams.containsKey('email') &&
              queryParams.containsKey('mobileNumber')) {
            fieldInUse = "Email or Phone Number";
          } else if (queryParams.containsKey('email')) {
            fieldInUse = "Email";
          } else if (queryParams.containsKey('mobileNumber')) {
            fieldInUse = "Phone number";
          }
          showCustomSnackBar(
            context,
            localizations.errorUserExists(
              fieldInUse,
            ), // Use localized string with placeholder
            CustomSnackBarType.error,
            backgroundColor: daltiErrorRed,
            textColor: daltiTextOnPrimary,
          );
        } else {
          // User does not exist or other success
          _navigateToVerifyScreen(fullPhoneNumber, email);
        }
      } else if (response.statusCode == 404) {
        // User not found, can proceed
        _navigateToVerifyScreen(fullPhoneNumber, email);
      } else if (response.statusCode == 409) {
        // Conflict, user exists
        String fieldInUse = "Identifier";
        if (queryParams.containsKey('email') &&
            queryParams.containsKey('mobileNumber')) {
          fieldInUse = "Email or Phone Number";
        } else if (queryParams.containsKey('email')) {
          fieldInUse = "Email";
        } else if (queryParams.containsKey('mobileNumber')) {
          fieldInUse = "Phone number";
        }
        showCustomSnackBar(
          context,
          localizations.errorUserExistsConflict(
            fieldInUse,
          ), // Use localized string with placeholder
          CustomSnackBarType.error,
          backgroundColor: daltiErrorRed,
          textColor: daltiTextOnPrimary,
        );
      } else {
        String serverMessage =
            responseData != null && responseData['message'] is String
                ? responseData['message']
                : response.body;
        showCustomSnackBar(
          context,
          localizations.errorCheckingUser(
            response.statusCode.toString(),
            serverMessage,
          ), // Use localized string
          CustomSnackBarType.error,
          backgroundColor: daltiErrorRed,
          textColor: daltiTextOnPrimary,
        );
      }
    } catch (e) {
      showCustomSnackBar(
        context,
        localizations.errorFailedCheckUserExists(
          e.toString(),
        ), // Use localized string with placeholder
        CustomSnackBarType.error,
        backgroundColor: daltiErrorRed,
        textColor: daltiTextOnPrimary,
      );
    } finally {
      setState(() {
        _isCheckingUser = false;
      });
    }
  }

  void _navigateToVerifyScreen(
    String fullPhoneNumberForVerify,
    String emailForVerify,
  ) {
    final localizations = AppLocalizations.of(context)!; // Get localizations
    // Determine the primary identifier for verification based on preference
    String identifierForOtpRequest;
    bool sendOtpViaEmail;

    if (_currentPreferredMethod == 'email') {
      identifierForOtpRequest = emailForVerify;
      sendOtpViaEmail = true;
    } else if (_currentPreferredMethod == 'mobile') {
      identifierForOtpRequest = fullPhoneNumberForVerify;
      sendOtpViaEmail = false;
    } else {
      // Fallback: if email is provided, prefer it for OTP, else use phone.
      // This case should be less common with the new `preferred_method` flow.
      if (emailForVerify.isNotEmpty) {
        identifierForOtpRequest = emailForVerify;
        sendOtpViaEmail = true;
      } else {
        identifierForOtpRequest = fullPhoneNumberForVerify;
        sendOtpViaEmail = false;
      }
    }

    // Ensure we have a valid identifier to request OTP for
    if (identifierForOtpRequest.isEmpty) {
      showCustomSnackBar(
        context,
        localizations.errorNoValidOtpIdentifier, // Use localized string
        CustomSnackBarType.error,
      );
      return;
    }

    Navigator.pushNamed(
      context,
      Routes.verifyRoute,
      arguments: {
        'firstName': firstNameController.text.trim(),
        'lastName': lastNameController.text.trim(),
        // Pass both, VerifyScreen can decide which one to use based on its own logic or a new param
        'email': emailForVerify.isNotEmpty ? emailForVerify : null,
        'phoneNumber':
            fullPhoneNumberForVerify.isNotEmpty
                ? fullPhoneNumberForVerify
                : "", // VerifyScreen expects non-null phone
        'password': passwordController.text,
        'preferred_method_for_otp':
            _currentPreferredMethod, // Pass the preference
      },
    );
  }
}
