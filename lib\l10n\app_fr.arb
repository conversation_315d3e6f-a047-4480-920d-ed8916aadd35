{"helloWorld": "Bonjour le monde!", "welcomeMessage": "Bienvenue sur notre application", "chooseLanguage": "Choisissez votre langue", "english": "<PERSON><PERSON><PERSON>", "arabic": "<PERSON><PERSON>", "french": "Français", "nextButtonText": "Suivant", "skipButtonText": "Passer", "intro1Title": "Titre de la diapositive d'intro 1 (FR)", "intro1Description": "Description pour la diapositive d'intro 1 (FR).", "intro2Title": "Titre de la diapositive d'intro 2 (FR)", "intro2Description": "Description pour la diapositive d'intro 2 (FR).", "intro3Title": "Titre de la diapositive d'intro 3 (FR)", "intro3Description": "Description pour la diapositive d'intro 3 (FR).", "preferredMethodTitle": "Choisissez votre méthode préférée", "preferredMethodSubtitle": "Sélectionnez comment vous souhaitez vous inscrire ou vous connecter.", "useEmailButton": "Utiliser l'e-mail", "useMobileButton": "Utiliser le numéro de mobile", "goBackButton": "Retour", "loginWithEmailTitle": "Connexion avec e-mail", "loginWithMobileTitle": "Connexion avec mobile", "loginSubtitle": "Ravi de vous revoir !", "emailLabel": "E-mail", "phoneNumberLabel": "Numéro de mobile", "passwordLabel": "Mot de passe", "forgotPasswordButton": "Mot de passe oublié ?", "loginButton": "Connexion", "dontHaveAccountText": "Vous n'avez pas de compte ?", "signUpButtonLoginScreen": " S'inscrire", "errorPhoneNumberEmpty": "Le numéro de mobile ne peut pas être vide.", "errorEmailPasswordEmpty": "L'e-mail et le mot de passe ne peuvent pas être vides.", "errorPhonePasswordEmpty": "Le numéro de mobile et le mot de passe ne peuvent pas être vides.", "errorLoginInvalidResponse": "Échec de la connexion : réponse du serveur invalide. Statut : {statusCode}", "loginSuccess": "Connexion réussie !", "errorLoginNoSessionId": "Échec de la connexion : ID de session non reçu.", "errorLoginWithMessageStatus": "Échec de la connexion : {message} (Statut : {statusCode})", "errorLoginGeneric": "Une erreur s'est produite lors de la connexion : {errorDetails}", "signUpTitle": "S'inscrire", "signUpSubtitleEmail": "Inscrivez-vous avec votre e-mail.", "signUpSubtitleMobile": "Inscrivez-vous avec votre numéro de mobile.", "signUpSubtitleGeneric": "Entrez vos informations pour vous inscrire !", "firstNameLabel": "Prénom", "lastNameLabel": "Nom de famille", "agreeTermsPrivacyText": "J'accepte les Termes et la Politique de confidentialité", "signUpButton": "S'inscrire", "alreadyHaveAccountText": "Vous avez déjà un compte ?", "loginButtonSignUpScreen": " Se connecter", "errorAgreeTerms": "Veuillez accepter les Termes et la Politique de confidentialité.", "errorEmailEmptyPreferred": "L'e-mail ne peut pas être vide car c'est votre méthode préférée.", "errorMobileEmptyPreferred": "Le numéro de mobile ne peut pas être vide car c'est votre méthode préférée.", "errorProvideEmailOrPhone": "Veuillez fournir un e-mail ou un numéro de mobile.", "errorProvideSignUpDetails": "Veuillez fournir des informations pour l'inscription.", "errorUserExists": "{fieldInUse} est déjà utilisé(e).", "errorUserExistsConflict": "{fieldInUse} est déjà utilisé(e) (conflit).", "errorCheckingUser": "Erreur lors de la vérification de l'utilisateur : {statusCode} {serverMessage}", "errorFailedCheckUserExists": "Échec de la vérification de l'existence de l'utilisateur : {errorDetails}", "errorNoValidOtpIdentifier": "Impossible de continuer sans e-mail ou téléphone valide pour l'OTP.", "forgotPasswordTitle": "Mot de passe oublié ?", "forgotPasswordSubtitleEmail": "Entrez votre e-mail pour réinitialiser votre mot de passe.", "forgotPasswordSubtitleMobile": "Entrez votre numéro de téléphone pour réinitialiser votre mot de passe.", "phoneNumberWithCodeLabel": "Numéro de téléphone (code pays requis)", "submitButton": "So<PERSON><PERSON><PERSON>", "resetPasswordTitle": "Réinitialiser le mot de passe", "resetPasswordSubtitle": "Entrez les détails de votre nouveau mot de passe ci-dessous.", "oldPasswordLabel": "Ancien mot de passe", "newPasswordLabel": "Nouveau mot de passe", "confirmPasswordLabel": "Confirmer le mot de passe", "setLocationDefault": "Définir l'emplacement", "searchHintText": "Rechercher...", "homeBannerTitle": "Service de peinture murale", "homeBannerSubtitle": "<PERSON><PERSON> votre mur élégant", "bookNowButton": "Réserver", "categoriesTitle": "Catégories", "seeAllButton": "Voir tout", "errorLoadingCategories": "Erreur : {errorMessage}", "noCategoriesFound": "Aucune catégorie trouvée.", "noParentCategoriesFound": "Aucune catégorie parente trouvée.", "profileLoading": "Chargement...", "profileUserFallback": "Utilisa<PERSON>ur", "profileErrorDisplayDetails": "E<PERSON>ur d'affichage des détails", "profileGuestUser": "Invi<PERSON>", "profilePleaseLogIn": "<PERSON><PERSON><PERSON>z vous connecter", "profileTabTitle": "Profil", "profileLogoutButton": "Déconnexion", "profileSettingsButton": "Paramètres", "profileMyAddressButton": "<PERSON> adresse", "profileMyCardsButton": "Mes cartes", "profileMyProfileButton": "Mon profil", "bookingsTabTitle": "Réservations", "bookingsTabAll": "Toutes", "bookingsTabActive": "Actives", "bookingsTabCompleted": "Terminées", "bookingsTabCancelled": "<PERSON><PERSON><PERSON>", "messagesTabTitle": "Messages", "messagesSearchHint": "Rechercher des messages...", "messagesEmptyTitle": "Aucun message", "messagesEmptySubtitle": "Votre liste de messages est actuellement vide.", "doctorPrefix": "Dr ", "unnamedProvider": "Prestataire non nommé", "defaultSpecialization": "Général", "naHospital": "Hôpital N/A", "slotInfoUnavailable": "Informations sur le créneau indisponibles", "errorFailedToFetchProviders": "Échec de la récupération des prestataires (Statut : {statusCode})", "errorFetchingData": "Erreur lors de la récupération des données : {errorDetails}", "searchResultCountForQuery": "{count,plural, =0{Aucun résultat} =1{{count} résultat} other{{count} résultats}} pour ''{query}''", "searchResultCountForQueryInCategory": "{count,plural, =0{Aucun résultat} =1{{count} résultat} other{{count} résultats}} pour ''{query}'' dans {category}", "searchResultCountForQueryInCity": "{count,plural, =0{Aucun résultat} =1{{count} résultat} other{{count} résultats}} pour ''{query}'' à {city}", "searchResultCountForQueryInCategoryInCity": "{count,plural, =0{Aucun résultat} =1{{count} résultat} other{{count} résultats}} pour ''{query}'' dans {category} à {city}", "noProvidersFoundForQuery": "<PERSON>cun prestataire trouvé pour ''{query}''.", "noProvidersFoundForQueryWithCategory": "Aucun prestataire trouvé pour ''{query}''\nCatégorie : {category}", "noProvidersFoundForQueryWithCity": "Aucun prestataire trouvé pour ''{query}''\nVille : {city}", "noProvidersFoundForQueryWithCategoryAndCity": "Aucun prestataire trouvé pour ''{query}''\nCatégorie : {category}\nVille : {city}", "searchScreenTitle": "Recherche", "searchByKeywordHint": "Recherche par mot-clé", "hospitalPrefix": "à ", "experienceInYears": "Exp. {years} ans", "feesDisplay": "Frais {currencySymbol}{amount}", "reviewCountDisplay": "({count} avis)", "nextAvailableSlotTitle": "Prochain créneau disponible", "filtersModalTitle": "Filtres", "filtersCategorySectionTitle": "Type de service (Catégorie)", "filtersSelectCategoryPlaceholder": "Sélectionner une catégorie", "filtersCitySectionTitle": "Ville (Wilaya)", "filtersEnterCityHint": "Entrer la ville ou Wilaya", "filtersClearButton": "Effacer les filtres", "filtersApplyButton": "Appliquer les filtres", "filtersAllCategoriesTitle": "Toutes les catégories", "filtersNoSubcategories": "Aucune sous-catégorie.", "filtersNoCategoriesAvailable": "Aucune catégorie disponible.", "errorFetchingCategories": "Erreur lors de la récupération des catégories : {errorDetails}", "detailScreenCannotSelectPastDate": "Impossible de sélectionner une date passée.", "detailScreenBookAppointment": "Prendre un rendez-vous", "detailScreenServiceLabel": "Service :", "detailScreenNoServices": "Aucun service disponible.", "detailScreenQueueLabel": "File d'attente :", "detailScreenGeneralAvailability": "Disponibilité générale pour ce service.", "detailScreenSelectDate": "Sélectionner une date", "detailScreenSelectTime": "Sélectionner une heure", "detailScreenPleaseSelectDate": "<PERSON><PERSON><PERSON>z sélectionner une date.", "detailScreenNoSlotsFound": "Aucun créneau trouvé pour le {date}.", "detailScreenAllSlotsBooked": "Tous les créneaux sont réservés pour le {date}.", "detailScreenConfirmBooking": "Confirmer la réservation", "detailScreenCancel": "Annuler", "detailScreenConfirm": "Confirmer", "detailScreenIncompleteDetails": "Les détails de la réservation sont incomplets.", "detailScreenPleaseLogin": "Veuillez vous connecter pour effectuer une réservation.", "detailScreenBookingSuccess": "Réservation réussie !", "detailScreenBookingFailed": "Échec de la réservation. Veuillez réessayer.", "detailScreenErrorOccurred": "Une erreur s'est produite : {error}", "detailScreenPointsRequired": "Ce service nécessite {points} points pour réserver.", "detailScreenNoPhoneNumber": "Numéro de téléphone non disponible.", "detailScreenCannotLaunchCall": "Impossible de lancer l'appel téléphonique.", "detailScreenServiceDetail": "Service :", "detailScreenQueueDetail": "File d'attente :", "detailScreenDateDetail": "Date :", "detailScreenTimeDetail": "Heure :", "detailScreenBook": "Réserver : {time} le {date}", "settingsSecurityTitle": "Sécurité", "settingsHelpTitle": "Aide et Support", "settingsPrivacyTitle": "Politique de Confidentialité", "settingsTermsTitle": "Conditions d'Utilisation", "settingsSecurityContent": "Votre sécurité est notre priorité absolue. Nous mettons en œuvre des mesures de sécurité conformes aux normes de l'industrie pour protéger vos données et garantir une expérience sûre lors de l'utilisation de nos services.", "settingsHelpContent": "Besoin d'aide ? Notre équipe de support est là pour vous aider avec toutes vos questions ou préoccupations concernant nos services.", "settingsPrivacyContent": "Nous valorisons votre vie privée. Cette politique décrit comment nous collectons, utilisons et protégeons vos informations personnelles lorsque vous utilisez nos services.", "settingsTermsContent": "En utilisant nos services, vous acceptez ces conditions. Veuillez les lire attentivement car elles régissent votre utilisation de notre plateforme et de nos services.", "notificationsTitle": "Notifications", "notificationsToday": "<PERSON><PERSON><PERSON>'hui", "notificationsYesterday": "<PERSON>er", "notificationsRetry": "<PERSON><PERSON><PERSON><PERSON>", "notificationsEmpty": "Pas encore de notifications !", "notificationsEmptyDesc": "Nous vous informerons quand quelque chose arrive.", "queueStatusUpcoming": "À venir", "queueStatusCheckedIn": "Enregistré", "queueStatusWaitingRoom": "Veuillez patienter confortablement", "queueStatusCalledIn": "On vous appelle !", "queueStatusInProgress": "Service en cours", "queueStatusCompleted": "<PERSON><PERSON><PERSON><PERSON>", "queueStatusSkipped": "<PERSON><PERSON><PERSON>", "queueStatusRequeued": "Remis en file d'attente", "queueStatusCanceled": "<PERSON><PERSON><PERSON>", "queueStatusUnknown": "Inconnu", "queueMessageUpcoming": "Vous êtes n°{position} dans la file. Soyez prêt.", "@queueMessageUpcoming": {"placeholders": {"position": {"type": "int"}}}, "queueMessageCheckedIn": "Prévenez-nous à votre arrivée !", "queueMessageWaitingRoom": "Votre prestataire vous appellera bient<PERSON>. Vous êtes n°{position} dans la file.", "@queueMessageWaitingRoom": {"placeholders": {"position": {"type": "int"}}}, "queueMessageCalledIn": "Veuillez vous diriger vers la zone de service.", "queueMessageInProgress": "Votre service est en cours.", "queueMessageCompleted": "Merci d'avoir utilisé notre service !", "queueMessageSkipped": "Vous avez manqué votre tour.", "queueMessageRequeued": "Vous avez été remis en file d'attente.", "queueMessageCanceled": "Votre réservation a été annulée.", "bookingDetailProviderDetails": "<PERSON><PERSON><PERSON> du prestataire :", "bookingDetailAppointmentFor": "<PERSON><PERSON>-vous pour :", "bookingDetailServicePrefix": "Service : ", "bookingDetailQueuePrefix": "File d'attente : ", "bookingDetailTimePrefix": "Heure : ", "bookingDetailMapButton": "<PERSON><PERSON>", "bookingDetailCallButton": "<PERSON><PERSON><PERSON>", "bookingDetailCancelButton": "Annuler", "bookingDetailCancelConfirmTitle": "<PERSON><PERSON>r le rendez-vous ?", "bookingDetailCancelConfirmMessage": "Êtes-vous sûr de vouloir annuler ce rendez-vous ? Cette action ne peut pas être annulée.", "bookingDetailCancelSuccess": "Rendez-vous annulé avec succès.", "bookingDetailCancelError": "Échec de l'annulation du rendez-vous. Veuillez réessayer.", "bookingDetailLocationError": "Erreur lors de l'obtention de la localisation : {error}", "bookingDetailLocationPermissionDenied": "Permission de localisation refusée", "bookingDetailLocationServiceDisabled": "Les services de localisation sont désactivés", "bookingDetailDirectionsError": "Erreur lors de l'obtention des directions : {error}", "bookingDetailDistance": "Distance : {distance}", "bookingDetailDuration": "Durée : {duration}", "bookingDetailOpenInMaps": "<PERSON><PERSON><PERSON><PERSON><PERSON> dans Maps", "bookingDetailGetDirections": "Obt<PERSON><PERSON> les directions", "bookingDetailFetchingLocation": "Récupération de la localisation...", "bookingDetailCalculatingRoutes": "Calcul des itinéraires...", "queueLiveStatusTitle": "Statut de la file d'attente en direct", "queueTimerPaused": "En pause", "queueLoadingService": "Chargement du service...", "queueLoadingDate": "Chargement de la date...", "queueLoadingTime": "Chargement de l'heure...", "queueFindingProfessional": "Recherche d’un professionnel...", "queueYouAreNext": "Vous ê<PERSON> le prochain", "queueYourTurn": "C'est votre tour", "queueYourPosition": "Votre position {position}", "queueTimerPausedMessage": "Minuteur en pause : en attente du début du service.", "queueViewQRCode": "Voir le code QR", "queueCheckInDetails": "Détails de l'enregistrement", "queueDone": "<PERSON><PERSON><PERSON><PERSON>", "queueEmptyOrNotListed": "La file est actuellement vide ou votre place n'est pas répertoriée.", "queueIncomingSwapRequests": "Demandes d'échange entrantes", "queueSwapRequestsFor": "pour votre place à {time}", "queueSwapStatus": "Statut : {status}", "queueSwapRequested": "Demandé : {dateTime}", "queueReject": "Refuser", "queueAccept": "Accepter", "queueSwapResponseSent": "Réponse à l'échange envoyée : {action}", "queueSwapResponseAccepted": "Acceptée", "queueSwapResponseRejected": "<PERSON><PERSON><PERSON><PERSON>", "queueNotConnected": "Non connecté. Impossible de répondre à l'échange.", "queueMemberLabelYouInProgress": "Votre tour (En cours) - Pos {position}", "@queueMemberLabelYouInProgress": {"placeholders": {"position": {}}}, "queueMemberLabelYouCompleted": "Vous - Service terminé - Pos {position}", "@queueMemberLabelYouCompleted": {"placeholders": {"position": {}}}, "queueMemberLabelYouStartingSoon": "Vous - <PERSON><PERSON><PERSON> bi<PERSON> - <PERSON> {position}", "@queueMemberLabelYouStartingSoon": {"placeholders": {"position": {}}}, "queueMemberLabelYouPosition": "Vous - Position {position}", "@queueMemberLabelYouPosition": {"placeholders": {"position": {}}}, "queueMemberLabelOtherStartingSoon": "{name} - Commence bi<PERSON><PERSON><PERSON> - <PERSON><PERSON> {position}", "@queueMemberLabelOtherStartingSoon": {"placeholders": {"name": {}, "position": {}}}, "queueMemberLabelOtherPosition": "{name} - Pos {position}", "@queueMemberLabelOtherPosition": {"placeholders": {"name": {}, "position": {}}}, "queueMemberLabelSuffixInProgress": " (En cours)", "queueMemberLabelSuffixCompleted": " (<PERSON><PERSON><PERSON><PERSON>)"}