{"helloWorld": "Hello World!", "welcomeMessage": "Welcome to our App", "chooseLanguage": "Choose Your Language", "english": "English", "arabic": "Arabic", "french": "French", "nextButtonText": "Next", "skipButtonText": "<PERSON><PERSON>", "intro1Title": "Intro Slide 1 Title (EN)", "intro1Description": "Description for intro slide 1 (EN).", "intro2Title": "Intro Slide 2 Title (EN)", "intro2Description": "Description for intro slide 2 (EN).", "intro3Title": "Intro Slide 3 Title (EN)", "intro3Description": "Description for intro slide 3 (EN).", "preferredMethodTitle": "Choose Your Preferred Method", "preferredMethodSubtitle": "Select how you'd like to sign up or log in.", "useEmailButton": "Use Email", "useMobileButton": "Use Mobile Number", "goBackButton": "Go Back", "loginWithEmailTitle": "Login with <PERSON><PERSON>", "loginWithMobileTitle": "Login with Mobile", "loginSubtitle": "Glad to meet you again!", "emailLabel": "Email", "phoneNumberLabel": "Phone Number", "passwordLabel": "Password", "forgotPasswordButton": "Forgot Password?", "loginButton": "<PERSON><PERSON>", "dontHaveAccountText": "Don't have an account?", "signUpButtonLoginScreen": " Sign Up", "errorPhoneNumberEmpty": "Phone number cannot be empty.", "errorEmailPasswordEmpty": "Email and Password cannot be empty.", "errorPhonePasswordEmpty": "Phone number and Password cannot be empty.", "errorLoginInvalidResponse": "Login failed: Invalid server response. Status: {statusCode}", "loginSuccess": "Login Successful!", "errorLoginNoSessionId": "<PERSON><PERSON> failed: Session ID not received.", "errorLoginWithMessageStatus": "Login failed: {message} (Status: {statusCode})", "errorLoginGeneric": "An error occurred during login: {errorDetails}", "signUpTitle": "Sign Up", "signUpSubtitleEmail": "Sign up using your Email.", "signUpSubtitleMobile": "Sign up using your Mobile Number.", "signUpSubtitleGeneric": "Enter your details for sign up!", "firstNameLabel": "First Name", "lastNameLabel": "Last Name", "agreeTermsPrivacyText": "I agree with Terms & Privacy", "signUpButton": "Sign Up", "alreadyHaveAccountText": "Already have an account?", "loginButtonSignUpScreen": " <PERSON><PERSON>", "errorAgreeTerms": "Please agree to the Terms & Privacy.", "errorEmailEmptyPreferred": "Email cannot be empty as it's your preferred method.", "errorMobileEmptyPreferred": "Phone number cannot be empty as it's your preferred method.", "errorProvideEmailOrPhone": "Please provide either an Email or a Phone Number.", "errorProvideSignUpDetails": "Please provide details for sign up.", "errorUserExists": "{fieldInUse} already in use.", "errorUserExistsConflict": "{fieldInUse} already in use (conflict).", "errorCheckingUser": "Error checking user: {statusCode} {serverMessage}", "errorFailedCheckUserExists": "Failed to check user existence: {errorDetails}", "errorNoValidOtpIdentifier": "Cannot proceed without a valid email or phone for OTP.", "forgotPasswordTitle": "Forgot Password?", "forgotPasswordSubtitleEmail": "Enter your email to reset your password.", "forgotPasswordSubtitleMobile": "Enter your phone number to reset your password.", "phoneNumberWithCodeLabel": "Phone Number (Requires Country Code)", "submitButton": "Submit", "resetPasswordTitle": "Reset Password", "resetPasswordSubtitle": "Enter your new password details below.", "oldPasswordLabel": "Old Password", "newPasswordLabel": "New Password", "confirmPasswordLabel": "Confirm Password", "setLocationDefault": "Set Location", "searchHintText": "Search...", "homeBannerTitle": "Wall Painting Service", "homeBannerSubtitle": "Make your wall stylish", "bookNowButton": "Book Now", "categoriesTitle": "Categories", "seeAllButton": "See All", "errorLoadingCategories": "Error: {errorMessage}", "noCategoriesFound": "No categories found.", "noParentCategoriesFound": "No parent categories found.", "profileLoading": "Loading...", "profileUserFallback": "User", "profileErrorDisplayDetails": "Error displaying details", "profileGuestUser": "Guest", "profilePleaseLogIn": "Please log in", "profileTabTitle": "Profile", "profileLogoutButton": "Logout", "profileSettingsButton": "Settings", "profileMyAddressButton": "My Address", "profileMyCardsButton": "My Cards", "profileMyProfileButton": "My Profile", "bookingsTabTitle": "Bookings", "bookingsTabAll": "All", "bookingsTabActive": "Active", "bookingsTabCompleted": "Completed", "bookingsTabCancelled": "Cancelled", "messagesTabTitle": "Messages", "messagesSearchHint": "Search messages...", "messagesEmptyTitle": "No Messages", "messagesEmptySubtitle": "Your message list is currently empty.", "doctorPrefix": "Dr. ", "unnamedProvider": "Unnamed Provider", "defaultSpecialization": "General", "naHospital": "N/A Hospital", "slotInfoUnavailable": "Slot information unavailable", "errorFailedToFetchProviders": "Failed to fetch providers (Status: {statusCode})", "errorFetchingData": "Error fetching data: {errorDetails}", "searchResultCountForQuery": "{count,plural, =0{No results} =1{{count} result} other{{count} results}} for ''{query}''", "searchResultCountForQueryInCategory": "{count,plural, =0{No results} =1{{count} result} other{{count} results}} for ''{query}'' in {category}", "searchResultCountForQueryInCity": "{count,plural, =0{No results} =1{{count} result} other{{count} results}} for ''{query}'' in {city}", "searchResultCountForQueryInCategoryInCity": "{count,plural, =0{No results} =1{{count} result} other{{count} results}} for ''{query}'' in {category} in {city}", "noProvidersFoundForQuery": "No providers found for ''{query}''.", "noProvidersFoundForQueryWithCategory": "No providers found for ''{query}''\nCategory: {category}", "noProvidersFoundForQueryWithCity": "No providers found for ''{query}''\nCity: {city}", "noProvidersFoundForQueryWithCategoryAndCity": "No providers found for ''{query}''\nCategory: {category}\nCity: {city}", "searchScreenTitle": "Search", "searchByKeywordHint": "Search by keyword", "hospitalPrefix": "at ", "experienceInYears": "Exp. {years} years", "feesDisplay": "Fees {currencySymbol}{amount}", "reviewCountDisplay": "({count} reviews)", "nextAvailableSlotTitle": "Next Available Slot", "filtersModalTitle": "Filters", "filtersCategorySectionTitle": "Service Type (Category)", "filtersSelectCategoryPlaceholder": "Select Category", "filtersCitySectionTitle": "City (Wilaya)", "filtersEnterCityHint": "Enter city or Wilaya", "filtersClearButton": "Clear Filters", "filtersApplyButton": "Apply Filters", "filtersAllCategoriesTitle": "All Categories", "filtersNoSubcategories": "No sub-categories.", "filtersNoCategoriesAvailable": "No categories available.", "errorFetchingCategories": "Error fetching categories: {errorDetails}", "detailScreenCannotSelectPastDate": "Cannot select a past date.", "detailScreenBookAppointment": "Book an Appointment", "detailScreenServiceLabel": "Service:", "detailScreenNoServices": "No services available.", "detailScreenQueueLabel": "Queue:", "detailScreenGeneralAvailability": "General availability for this service.", "detailScreenSelectDate": "Select Date", "detailScreenSelectTime": "Select Time", "detailScreenPleaseSelectDate": "Please select a date.", "detailScreenNoSlotsFound": "No slots found for {date}.", "detailScreenAllSlotsBooked": "All slots are booked for {date}.", "detailScreenConfirmBooking": "Confirm Booking", "detailScreenCancel": "Cancel", "detailScreenConfirm": "Confirm", "detailScreenIncompleteDetails": "Booking details are incomplete.", "detailScreenPleaseLogin": "Please log in to make a booking.", "detailScreenBookingSuccess": "Booking Successful!", "detailScreenBookingFailed": "Booking failed. Please try again.", "detailScreenErrorOccurred": "An error occurred: {error}", "detailScreenPointsRequired": "This service requires {points} points to book.", "detailScreenNoPhoneNumber": "Phone number is not available.", "detailScreenCannotLaunchCall": "Could not launch phone call.", "detailScreenServiceDetail": "Service:", "detailScreenQueueDetail": "Queue:", "detailScreenDateDetail": "Date:", "detailScreenTimeDetail": "Time:", "detailScreenBook": "Book: {time} on {date}", "settingsSecurityTitle": "Security", "settingsHelpTitle": "Help & Support", "settingsPrivacyTitle": "Privacy Policy", "settingsTermsTitle": "Terms of Service", "settingsSecurityContent": "Your security is our top priority. We implement industry-standard security measures to protect your data and ensure a safe experience while using our services.", "settingsHelpContent": "Need assistance? Our support team is here to help you with any questions or concerns you may have about our services.", "settingsPrivacyContent": "We value your privacy. This policy outlines how we collect, use, and protect your personal information when you use our services.", "settingsTermsContent": "By using our services, you agree to these terms. Please read them carefully as they govern your use of our platform and services.", "notificationsTitle": "Notifications", "notificationsToday": "Today", "notificationsYesterday": "Yesterday", "notificationsRetry": "Retry", "notificationsEmpty": "No Notifications Yet!", "notificationsEmptyDesc": "We'll notify you when something arrives.", "queueStatusUpcoming": "Upcoming", "queueStatusCheckedIn": "Checked In", "queueStatusWaitingRoom": "Please wait comfortably", "queueStatusCalledIn": "You're being called in!", "queueStatusInProgress": "Service in Progress", "queueStatusCompleted": "Completed", "queueStatusSkipped": "Skipped", "queueStatusRequeued": "Re-queued", "queueStatusCanceled": "Canceled", "queueStatusUnknown": "Unknown", "queueMessageUpcoming": "You are #{position} in line. Please be ready.", "@queueMessageUpcoming": {"placeholders": {"position": {"type": "int"}}}, "queueMessageCheckedIn": "Let us know when you arrive!", "queueMessageWaitingRoom": "Your provider will call you soon. You're #{position} in line.", "@queueMessageWaitingRoom": {"placeholders": {"position": {"type": "int"}}}, "queueMessageCalledIn": "Please proceed to the service area.", "queueMessageInProgress": "Your service is in progress.", "queueMessageCompleted": "Thank you for using our service!", "queueMessageSkipped": "You missed your turn.", "queueMessageRequeued": "You have been re-added to the queue.", "queueMessageCanceled": "Your booking was canceled.", "bookingDetailProviderDetails": "Provider Details:", "bookingDetailAppointmentFor": "Appointment For:", "bookingDetailServicePrefix": "Service: ", "bookingDetailQueuePrefix": "Queue: ", "bookingDetailTimePrefix": "Time: ", "bookingDetailMapButton": "Map", "bookingDetailCallButton": "Call", "bookingDetailCancelButton": "Cancel", "bookingDetailCancelConfirmTitle": "Cancel Appointment?", "bookingDetailCancelConfirmMessage": "Are you sure you want to cancel this appointment? This action cannot be undone.", "bookingDetailCancelSuccess": "Appointment cancelled successfully.", "bookingDetailCancelError": "Failed to cancel appointment. Please try again.", "bookingDetailLocationError": "Error getting location: {error}", "bookingDetailLocationPermissionDenied": "Location permission denied", "bookingDetailLocationServiceDisabled": "Location services are disabled", "bookingDetailDirectionsError": "Error getting directions: {error}", "bookingDetailDistance": "Distance: {distance}", "bookingDetailDuration": "Duration: {duration}", "bookingDetailOpenInMaps": "Open in Maps", "bookingDetailGetDirections": "Get Directions", "bookingDetailFetchingLocation": "Fetching location...", "bookingDetailCalculatingRoutes": "Calculating routes...", "queueLiveStatusTitle": "Live Queue Status", "queueTimerPaused": "Paused", "queueLoadingService": "Loading service...", "queueLoadingDate": "Loading date...", "queueLoadingTime": "Loading time...", "queueFindingProfessional": "Finding professional...", "queueYouAreNext": "You Are Next", "queueYourTurn": "Your Turn", "queueYourPosition": "Your Position {position}", "queueTimerPausedMessage": "Timer Paused: Waiting for service to start.", "queueViewQRCode": "View QRCode", "queueCheckInDetails": "Check-In Details", "queueDone": "Done", "queueEmptyOrNotListed": "Queue is currently empty or your spot is not listed.", "queueIncomingSwapRequests": "Incoming Swap Requests", "queueSwapRequestsFor": "for your spot at {time}", "queueSwapStatus": "Status: {status}", "queueSwapRequested": "Requested: {dateTime}", "queueReject": "Reject", "queueAccept": "Accept", "queueSwapResponseSent": "Swap response sent: {action}", "queueSwapResponseAccepted": "Accepted", "queueSwapResponseRejected": "Rejected", "queueNotConnected": "Not connected. Cannot respond to swap.", "queueMemberLabelYouInProgress": "Your Turn (In Progress) - Pos {position}", "@queueMemberLabelYouInProgress": {"placeholders": {"position": {}}}, "queueMemberLabelYouCompleted": "You - Service Completed - Pos {position}", "@queueMemberLabelYouCompleted": {"placeholders": {"position": {}}}, "queueMemberLabelYouStartingSoon": "You - Starting Soon - <PERSON><PERSON> {position}", "@queueMemberLabelYouStartingSoon": {"placeholders": {"position": {}}}, "queueMemberLabelYouPosition": "You - Position {position}", "@queueMemberLabelYouPosition": {"placeholders": {"position": {}}}, "queueMemberLabelOtherStartingSoon": "{name} - Starting Soon - Pos {position}", "@queueMemberLabelOtherStartingSoon": {"placeholders": {"name": {}, "position": {}}}, "queueMemberLabelOtherPosition": "{name} - Pos {position}", "@queueMemberLabelOtherPosition": {"placeholders": {"name": {}, "position": {}}}, "queueMemberLabelSuffixInProgress": " (In Progress)", "queueMemberLabelSuffixCompleted": " (Completed)"}