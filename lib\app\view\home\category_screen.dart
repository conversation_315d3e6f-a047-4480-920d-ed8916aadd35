import 'package:dalti/app/data/data_file.dart';
import 'package:dalti/app/models/model_category.dart';
import 'package:dalti/base/constant.dart';
import 'package:dalti/base/device_util.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';

import '../../../base/color_data.dart';
import '../../routes/app_routes.dart';
import 'package:dalti/app/utils/category_utils.dart';

class CategoryScreen extends StatefulWidget {
  final ModelCategory? initialSelectedParent;

  const CategoryScreen({Key? key, this.initialSelectedParent})
    : super(key: key);

  @override
  State<CategoryScreen> createState() => _CategoryScreenState();
}

class _CategoryScreenState extends State<CategoryScreen> {
  Future<List<ModelCategory>>? _categoriesFuture;
  List<ModelCategory> _allCategories = [];
  List<ModelCategory> _topLevelCategories = [];
  ModelCategory? _selectedParentCategory;
  List<ModelCategory> _childCategoriesToDisplay = [];

  var noOfGrid = 3;

  @override
  void initState() {
    super.initState();
    _categoriesFuture = fetchCategories();
  }

  void _processCategories(List<ModelCategory> fetchedCategories) {
    if (!mounted) return;

    _allCategories = fetchedCategories;
    _topLevelCategories = ModelCategory.buildHierarchy(_allCategories);

    if (widget.initialSelectedParent != null &&
        _selectedParentCategory == null) {
      ModelCategory? matchingParentFromFetched;
      for (var cat in _topLevelCategories) {
        if (cat.id == widget.initialSelectedParent!.id) {
          matchingParentFromFetched = cat;
          break;
        }
      }
      if (matchingParentFromFetched == null) {
        for (var cat in _allCategories) {
          if (cat.id == widget.initialSelectedParent!.id) {
            matchingParentFromFetched = cat;
            break;
          }
        }
      }

      if (matchingParentFromFetched != null &&
          matchingParentFromFetched.children.isNotEmpty) {
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              _selectedParentCategory = matchingParentFromFetched;
              _childCategoriesToDisplay = matchingParentFromFetched!.children;
            });
          }
        });
      } else if (matchingParentFromFetched != null &&
          matchingParentFromFetched.children.isEmpty) {
        print(
          "Initial parent category '${widget.initialSelectedParent!.title}' has no children. Navigating to search.",
        );
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            Navigator.pushReplacementNamed(
              context,
              Routes.searchRoute,
              arguments: {'category': matchingParentFromFetched},
            );
          }
        });
      }
    }
  }

  void _handleCategoryTap(ModelCategory category) {
    if (widget.initialSelectedParent != null) {
      // SCENARIO 1: Opened with an initial parent (e.g., from TabHome shortcut)
      // Any tap here goes to SearchScreen.
      Navigator.pushNamed(
        context,
        Routes.searchRoute,
        arguments: {'category': category},
      );
    } else {
      // SCENARIO 2: Opened without an initial parent (e.g., "See All Categories" button or Filter Modal)
      if (_selectedParentCategory == null) {
        // Currently viewing TOP-LEVEL categories
        if (category.children.isNotEmpty) {
          // Tapped on a top-level category WITH children: Drill down
          setState(() {
            _selectedParentCategory = category;
            _childCategoriesToDisplay = category.children;
          });
        } else {
          // Tapped on a top-level LEAF category:
          // This implies it's for filter selection or a direct navigation if not used as a modal.
          // If CategoryScreen can also be used to directly navigate to search from "See All" path:
          Navigator.pushNamed(
            context,
            Routes.searchRoute,
            arguments: {'category': category},
          );
          // If it's strictly for filter modals when a leaf is tapped at top level:
          // Navigator.pop(context, category);
        }
      } else {
        // Currently viewing CHILD categories (because _selectedParentCategory is not null after "See All" -> Parent)
        // Tapping any child category in this view should go to SearchScreen.
        Navigator.pushNamed(
          context,
          Routes.searchRoute,
          arguments: {'category': category},
        );
      }
    }
  }

  void _handleBackButton() {
    if (_selectedParentCategory != null &&
        widget.initialSelectedParent == null) {
      // We are in "See All" flow and drilled down. Go back to parent list.
      setState(() {
        _selectedParentCategory = null;
        _childCategoriesToDisplay = [];
      });
    } else {
      // - We are at top-level in "See All" flow OR
      // - CategoryScreen was opened with an initialSelectedParent (any level).
      // In these cases, pop the screen.
      Navigator.pop(context);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (DeviceUtil.isTablet) {
      noOfGrid = 6;
    }
    FetchPixels(context);

    final ModalRoute? modalRoute = ModalRoute.of(context);
    ModelCategory? routeArgumentCategory;
    // This logic seems redundant if initialSelectedParent is used consistently.
    // Keeping for now, but might be simplified if initialSelectedParent covers all entry points.
    if (widget.initialSelectedParent == null &&
        modalRoute != null &&
        modalRoute.settings.arguments != null) {
      if (modalRoute.settings.arguments is ModelCategory) {
        routeArgumentCategory = modalRoute.settings.arguments as ModelCategory?;
        // If routeArgumentCategory is not null and _selectedParentCategory is null, it implies initial selection.
        // However, _processCategories already handles widget.initialSelectedParent.
        // This might be for cases where CategoryScreen is pushed without initialSelectedParent but with arguments.
        // For simplicity, relying on widget.initialSelectedParent and _processCategories for now.
      }
    }

    String currentTitle =
        _selectedParentCategory == null
            ? "Select Main Category"
            : _selectedParentCategory!.title;

    // If initialSelectedParent is set and _selectedParentCategory is not (meaning we are showing its children),
    // the title should reflect the initial parent.
    if (widget.initialSelectedParent != null &&
        _selectedParentCategory != null &&
        widget.initialSelectedParent!.id == _selectedParentCategory!.id) {
      currentTitle = widget.initialSelectedParent!.title;
    } else if (widget.initialSelectedParent != null &&
        _selectedParentCategory == null) {
      //This case implies initialSelectedParent was a leaf, and _processCategories should have navigated.
      // If somehow we are still here, show its title.
      currentTitle = widget.initialSelectedParent!.title;
    }

    return WillPopScope(
      onWillPop: () async {
        _handleBackButton();
        return false;
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: daltiBackground, // Applied daltiBackground
        body: SafeArea(
          child: Column(
            children: [
              getVerSpace(FetchPixels.getPixelHeight(20)),
              getPaddingWidget(
                EdgeInsets.symmetric(horizontal: FetchPixels.getPixelWidth(20)),
                gettoolbarMenu(
                  context,
                  "back.svg",
                  _handleBackButton,
                  istext: true,
                  title: currentTitle,
                  fontsize: 24,
                  weight: FontWeight.w800,
                  textColor: daltiTextHeadline, // Applied daltiTextHeadline
                  // Assuming gettoolbarMenu or getSvgImage handles icon color internally or allows passing it
                  // For now, let's assume the back.svg will use daltiIconDefault if not colored explicitly by gettoolbarMenu
                ),
              ),
              getVerSpace(FetchPixels.getPixelHeight(32)),
              Expanded(
                child: FutureBuilder<List<ModelCategory>>(
                  future: _categoriesFuture,
                  builder: (context, snapshot) {
                    if (snapshot.connectionState == ConnectionState.waiting &&
                        _topLevelCategories.isEmpty) {
                      return Center(
                        child: CircularProgressIndicator(color: daltiPrimary),
                      ); // Applied daltiPrimary
                    }
                    if (snapshot.hasError && _topLevelCategories.isEmpty) {
                      return Center(
                        child: Text(
                          'Error: ${snapshot.error}',
                          style: TextStyle(color: daltiErrorRed),
                        ),
                      ); // Applied daltiErrorRed
                    }

                    if (snapshot.hasData && _allCategories.isEmpty) {
                      _processCategories(snapshot.data!);
                    }

                    if (_topLevelCategories.isEmpty &&
                        !snapshot.hasData &&
                        !snapshot.hasError &&
                        !(snapshot.connectionState ==
                            ConnectionState.waiting)) {
                      return Center(
                        child: Text(
                          'No categories found.', // Simplified message
                          style: TextStyle(
                            color: daltiTextMuted,
                          ), // Applied daltiTextMuted
                        ),
                      );
                    }

                    final categoriesToDisplay =
                        _selectedParentCategory == null &&
                                widget.initialSelectedParent == null
                            ? _topLevelCategories
                            : _childCategoriesToDisplay;

                    // If initialSelectedParent is provided, and we haven't drilled down further (_selectedParentCategory is still the initial or null)
                    // then categoriesToDisplay should be children of initialSelectedParent.
                    // The _processCategories and _handleCategoryTap logic should manage _childCategoriesToDisplay correctly.

                    if (categoriesToDisplay.isEmpty &&
                        _selectedParentCategory != null) {
                      return Center(
                        child: Text(
                          'No sub-categories found for ${_selectedParentCategory!.title}.',
                          style: TextStyle(
                            color: daltiTextMuted,
                          ), // Applied daltiTextMuted
                        ),
                      );
                    }
                    if (categoriesToDisplay.isEmpty &&
                        _selectedParentCategory == null &&
                        widget.initialSelectedParent == null &&
                        (_allCategories.isNotEmpty || snapshot.hasData)) {
                      return Center(
                        child: Text(
                          'No main categories found.',
                          style: TextStyle(color: daltiTextMuted),
                        ), // Applied daltiTextMuted
                      );
                    }

                    return categoryView(categoriesToDisplay);
                  },
                ),
                flex: 1,
              ),
            ],
          ),
        ),
      ),
    );
  }

  AnimationLimiter categoryView(List<ModelCategory> categoryListsToDisplay) {
    return AnimationLimiter(
      child: GridView.builder(
        padding: EdgeInsets.symmetric(
          horizontal: FetchPixels.getPixelWidth(20),
          vertical: FetchPixels.getPixelHeight(10),
        ),
        primary: true,
        itemCount: categoryListsToDisplay.length,
        itemBuilder: (context, index) {
          ModelCategory modelCategory = categoryListsToDisplay[index];
          bool isParentCategoryView =
              _selectedParentCategory == null &&
              widget.initialSelectedParent == null;
          return GestureDetector(
            onTap: () => _handleCategoryTap(modelCategory),
            child: AnimationConfiguration.staggeredGrid(
              position: index,
              duration: const Duration(milliseconds: 300),
              columnCount: noOfGrid,
              child: ScaleAnimation(
                child: FadeInAnimation(
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      vertical: FetchPixels.getPixelHeight(16),
                      horizontal: FetchPixels.getPixelWidth(8),
                    ),
                    decoration: BoxDecoration(
                      color: daltiCard,
                      boxShadow: [
                        BoxShadow(
                          color: daltiDividerLine.withOpacity(0.4),
                          blurRadius: 6,
                          offset: const Offset(0.0, 2.0),
                        ),
                      ],
                      borderRadius: BorderRadius.circular(
                        FetchPixels.getPixelHeight(12),
                      ),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          modelCategory.children.isNotEmpty &&
                                  isParentCategoryView
                              ? Icons.folder_copy_outlined
                              : Icons.category_rounded,
                          size: FetchPixels.getPixelHeight(40),
                          color: daltiPrimary,
                        ),
                        getVerSpace(FetchPixels.getPixelHeight(12)),
                        Flexible(
                          child: getCustomFont(
                            modelCategory.title,
                            13.5,
                            daltiTextBody,
                            2,
                            fontWeight: FontWeight.w500,
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          );
        },
        gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: noOfGrid,
          crossAxisSpacing: FetchPixels.getPixelWidth(16),
          mainAxisSpacing: FetchPixels.getPixelHeight(16),
          childAspectRatio: 0.95,
        ),
      ),
    );
  }
}
