import 'package:dalti/app/routes/app_routes.dart';
import 'package:dalti/base/color_data.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:flutter/material.dart';
import 'package:dalti/base/constant.dart';
import 'package:dalti/l10n/app_localizations.dart';
import 'package:dalti/services/mobile_messaging_service.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';

// Model for the message sender
class MessageSender {
  final String id;
  final String name;

  MessageSender({required this.id, required this.name});

  factory MessageSender.fromJson(Map<String, dynamic> json) {
    return MessageSender(
      id: json['id'] as String,
      name: json['name'] as String,
    );
  }
}

// Model for individual messages from the API
class MobileMessage {
  final int id;
  final String content;
  final DateTime createdAt;
  final MessageSender sender;
  final String status; // 'SENT', 'DELIVERED', 'READ'

  MobileMessage({
    required this.id,
    required this.content,
    required this.createdAt,
    required this.sender,
    required this.status,
  });

  factory MobileMessage.fromJson(Map<String, dynamic> json) {
    return MobileMessage(
      id: json['id'] as int,
      content: json['content'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      sender: MessageSender.fromJson(json['sender'] as Map<String, dynamic>),
      status: json['status'] as String,
    );
  }
}

// Model for the UI display (conversation list item)
class ModelMessage {
  final String id; // Conversation ID
  final String senderName; // Participant's display name
  final String? senderAvatarUrl; // Participant's display image
  final String lastMessage;
  final String timestamp;
  final int unreadCount;
  final int conversationId; // Same as id for navigation
  final String? senderId; // ID of the sender of the last message, if available
  final String? status; // Status of the last message, if available

  ModelMessage({
    required this.id,
    required this.senderName,
    this.senderAvatarUrl,
    required this.lastMessage,
    required this.timestamp,
    required this.unreadCount,
    required this.conversationId,
    this.senderId,
    this.status,
  });

  factory ModelMessage.fromConversation(ConversationModel conv) {
    return ModelMessage(
      id: conv.id.toString(),
      senderName: conv.displayName, // Use displayName from ConversationModel
      senderAvatarUrl:
          conv.displayImage, // Use displayImage from ConversationModel
      lastMessage: conv.lastMessage?.content ?? 'No messages yet',
      timestamp: _formatTimestamp(
        conv.updatedAt,
      ), // updatedAt for conversation sorting
      unreadCount: conv.unreadCount,
      conversationId: conv.id,
      // lastMessage.sender.id is for the sender of that specific last message.
      // For MessageScreen, we usually need the ID of the *other* participant.
      // This might need further refinement based on how recipient ID is used in MessageScreen.
      // If conv.displayName is unique and can serve as a stable ID for the recipient, that's one option.
      // For now, we'll use the last message's sender ID if available.
      senderId:
          conv.lastMessage?.sender?.id ??
          conv
              .lastMessage
              ?.senderName, // Fallback to senderName if ID is not in preview
      status: conv.lastMessage?.status,
    );
  }

  static String _formatTimestamp(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'Just now';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} min';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}h';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}d';
    } else {
      final weeks = difference.inDays ~/ 7;
      return '${weeks}w';
    }
  }
}

class TabMessages extends StatefulWidget {
  const TabMessages({Key? key}) : super(key: key);

  @override
  State<TabMessages> createState() => _TabMessagesState();
}

class _TabMessagesState extends State<TabMessages> {
  final TextEditingController _searchController = TextEditingController();
  final MobileMessagingService _messagingService = MobileMessagingService();

  List<ModelMessage> _messages = [];
  List<ModelMessage> _filteredMessages = [];
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_filterMessages);
    _loadConversations();
  }

  Future<bool> _checkAuthentication() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final sessionId = prefs.getString('session_id');
      return sessionId != null && sessionId.isNotEmpty;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking authentication: $e');
      }
      return false;
    }
  }

  Future<void> _loadConversations() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final isAuthenticated = await _checkAuthentication();
      if (!isAuthenticated) {
        setState(() {
          _error = 'Please log in to view your messages';
          _isLoading = false;
        });
        return;
      }

      // Get the conversations from the API
      final conversations = await _messagingService.getConversations();

      if (mounted) {
        setState(() {
          _messages =
              conversations
                  .map((conv) => ModelMessage.fromConversation(conv))
                  .toList();

          // Sort conversations by latest message
          _messages.sort((a, b) {
            final aTime = DateTime.parse(a.timestamp);
            final bTime = DateTime.parse(b.timestamp);
            return bTime.compareTo(aTime);
          });

          _filteredMessages = _messages;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error loading conversations: $e');
      }
      if (mounted) {
        setState(() {
          _error = 'Unable to load messages. Please try again.';
          _isLoading = false;
        });
      }
    }
  }

  @override
  void dispose() {
    _searchController.removeListener(_filterMessages);
    _searchController.dispose();
    super.dispose();
  }

  void _filterMessages() {
    final query = _searchController.text.toLowerCase();
    if (query.isEmpty) {
      setState(() {
        _filteredMessages = _messages;
      });
    } else {
      setState(() {
        _filteredMessages =
            _messages
                .where(
                  (message) =>
                      message.senderName.toLowerCase().contains(query) ||
                      message.lastMessage.toLowerCase().contains(query),
                )
                .toList();
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    final localizations = AppLocalizations.of(context)!;

    return Scaffold(
      backgroundColor: daltiBackground,
      body: SafeArea(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: EdgeInsets.only(
                left: FetchPixels.getPixelWidth(20),
                right: FetchPixels.getPixelWidth(20),
                top: FetchPixels.getPixelHeight(25),
                bottom: FetchPixels.getPixelHeight(15),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: getCustomFont(
                      localizations.messagesTabTitle,
                      28,
                      daltiTextHeadline,
                      1,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  if (_isLoading)
                    SizedBox(
                      height: 24,
                      width: 24,
                      child: CircularProgressIndicator(strokeWidth: 2.5),
                    )
                  else if (_error != null)
                    IconButton(
                      icon: Icon(Icons.refresh),
                      onPressed: _loadConversations,
                      color: daltiPrimary,
                    ),
                ],
              ),
            ),
            _buildSearchBar(localizations),
            getVerSpace(FetchPixels.getPixelHeight(10)),
            if (_error != null)
              Container(
                margin: EdgeInsets.symmetric(
                  horizontal: FetchPixels.getPixelWidth(20),
                  vertical: FetchPixels.getPixelHeight(8),
                ),
                padding: EdgeInsets.all(FetchPixels.getPixelHeight(12)),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(Icons.error_outline, color: Colors.red, size: 20),
                    SizedBox(width: 8),
                    Expanded(
                      child: Text(_error!, style: TextStyle(color: Colors.red)),
                    ),
                  ],
                ),
              ),
            Expanded(
              child:
                  _isLoading
                      ? Center(child: CircularProgressIndicator())
                      : _filteredMessages.isEmpty
                      ? _buildEmptyState(localizations)
                      : _buildMessagesList(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchBar(AppLocalizations localizations) {
    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: FetchPixels.getPixelWidth(20),
        vertical: FetchPixels.getPixelHeight(5),
      ),
      child: Container(
        height: FetchPixels.getPixelHeight(52),
        decoration: BoxDecoration(
          color: daltiCard,
          borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(26)),
          border: Border.all(
            color: daltiDividerLine.withOpacity(0.7),
            width: 0.8,
          ),
        ),
        child: TextField(
          controller: _searchController,
          textAlignVertical: TextAlignVertical.center,
          decoration: InputDecoration(
            hintText: localizations.messagesSearchHint,
            hintStyle: TextStyle(
              color: daltiTextMuted,
              fontSize: 15.5,
              fontWeight: FontWeight.w400,
              fontFamily: Constant.fontsFamily,
            ),
            border: InputBorder.none,
            prefixIcon: Icon(
              Icons.search_rounded,
              color: daltiIconDefault.withOpacity(0.8),
              size: FetchPixels.getPixelHeight(24),
            ),
            contentPadding: EdgeInsets.only(
              left: FetchPixels.getPixelWidth(15),
              right: FetchPixels.getPixelWidth(15),
              top: FetchPixels.getPixelHeight(15),
              bottom: FetchPixels.getPixelHeight(15),
            ),
          ),
          style: TextStyle(
            color: daltiTextBody,
            fontWeight: FontWeight.w400,
            fontSize: 15.5,
            fontFamily: Constant.fontsFamily,
          ),
        ),
      ),
    );
  }

  Widget _buildMessagesList() {
    return RefreshIndicator(
      onRefresh: _loadConversations,
      child: ListView.builder(
        padding: EdgeInsets.symmetric(
          horizontal: FetchPixels.getPixelWidth(16),
          vertical: FetchPixels.getPixelHeight(10),
        ),
        itemCount: _filteredMessages.length,
        itemBuilder: (context, index) {
          final message = _filteredMessages[index];
          return Card(
            elevation: 0.5,
            margin: EdgeInsets.symmetric(
              vertical: FetchPixels.getPixelHeight(6),
            ),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                FetchPixels.getPixelHeight(12),
              ),
            ),
            color: daltiCard,
            child: InkWell(
              onTap: () {
                Navigator.pushNamed(
                  context,
                  Routes.messageScreenRoute,
                  arguments: message,
                ).then((_) => _loadConversations());
              },
              borderRadius: BorderRadius.circular(
                FetchPixels.getPixelHeight(12),
              ),
              child: Padding(
                padding: EdgeInsets.all(FetchPixels.getPixelHeight(12)),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    _buildAvatar(message),
                    getHorSpace(FetchPixels.getPixelWidth(16)),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            message.senderName,
                            style: TextStyle(
                              color: daltiTextHeadline,
                              fontSize: 16.5,
                              fontWeight:
                                  message.unreadCount > 0
                                      ? FontWeight.bold
                                      : FontWeight.w600,
                            ),
                            overflow: TextOverflow.ellipsis,
                          ),
                          getVerSpace(FetchPixels.getPixelHeight(5)),
                          Text(
                            message.lastMessage,
                            style: TextStyle(
                              color:
                                  message.unreadCount > 0
                                      ? daltiTextBody
                                      : daltiTextMuted,
                              fontSize: 14.5,
                              fontWeight:
                                  message.unreadCount > 0
                                      ? FontWeight.w500
                                      : FontWeight.normal,
                            ),
                            overflow: TextOverflow.ellipsis,
                            maxLines: 1,
                          ),
                        ],
                      ),
                    ),
                    getHorSpace(FetchPixels.getPixelWidth(12)),
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.end,
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          message.timestamp,
                          style: TextStyle(
                            color:
                                message.unreadCount > 0
                                    ? daltiPrimary
                                    : daltiTextMuted.withOpacity(0.8),
                            fontSize: 12.5,
                            fontWeight:
                                message.unreadCount > 0
                                    ? FontWeight.w500
                                    : FontWeight.normal,
                          ),
                        ),
                        if (message.unreadCount > 0)
                          Container(
                            margin: EdgeInsets.only(
                              top: FetchPixels.getPixelHeight(6),
                            ),
                            padding: EdgeInsets.symmetric(
                              horizontal: FetchPixels.getPixelWidth(7),
                              vertical: FetchPixels.getPixelHeight(3),
                            ),
                            decoration: BoxDecoration(
                              color: daltiPrimary,
                              borderRadius: BorderRadius.circular(
                                FetchPixels.getPixelHeight(10),
                              ),
                            ),
                            child: Text(
                              message.unreadCount.toString(),
                              style: TextStyle(
                                color: daltiTextOnPrimary,
                                fontSize: 11.5,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        if (message.status != null &&
                            message.senderId == _currentUserId())
                          Padding(
                            padding: EdgeInsets.only(
                              top: FetchPixels.getPixelHeight(4),
                            ),
                            child: Icon(
                              message.status == 'READ'
                                  ? Icons.done_all
                                  : (message.status == 'DELIVERED'
                                      ? Icons.done_all
                                      : Icons.done),
                              size: 14,
                              color:
                                  message.status == 'READ'
                                      ? daltiPrimary
                                      : daltiTextMuted.withOpacity(0.8),
                            ),
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildAvatar(ModelMessage message) {
    if (message.senderAvatarUrl != null &&
        message.senderAvatarUrl!.startsWith('http')) {
      return CircleAvatar(
        radius: FetchPixels.getPixelHeight(28),
        backgroundImage: NetworkImage(message.senderAvatarUrl!),
        onBackgroundImageError: (exception, stackTrace) {
          if (kDebugMode) {
            print('Error loading network image for avatar: $exception');
          }
        },
        backgroundColor: daltiPrimaryLight.withOpacity(0.5),
        child:
            message.senderAvatarUrl == null
                ? Icon(
                  Icons.person,
                  color: daltiPrimary,
                  size: FetchPixels.getPixelHeight(30),
                )
                : null,
      );
    } else if (message.senderAvatarUrl != null &&
        message.senderAvatarUrl!.isNotEmpty) {
      return CircleAvatar(
        radius: FetchPixels.getPixelHeight(28),
        backgroundImage: AssetImage(message.senderAvatarUrl!),
        onBackgroundImageError: (exception, stackTrace) {
          if (kDebugMode) {
            print('Error loading asset image for avatar: $exception');
          }
        },
        backgroundColor: daltiPrimaryLight.withOpacity(0.5),
        child: Icon(
          Icons.person,
          color: daltiPrimary,
          size: FetchPixels.getPixelHeight(30),
        ),
      );
    }
    return CircleAvatar(
      radius: FetchPixels.getPixelHeight(28),
      backgroundColor: daltiPrimaryLight.withOpacity(0.5),
      child: Text(
        message.senderName.isNotEmpty
            ? message.senderName[0].toUpperCase()
            : "?",
        style: TextStyle(
          fontSize: 20,
          color: daltiPrimary,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildEmptyState(AppLocalizations localizations) {
    if (_error != null) {
      return Center(
        child: Padding(
          padding: EdgeInsets.all(FetchPixels.getPixelHeight(20)),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.error_outline,
                size: FetchPixels.getPixelHeight(60),
                color: Colors.red.withOpacity(0.7),
              ),
              getVerSpace(FetchPixels.getPixelHeight(16)),
              Text(
                _error!,
                style: TextStyle(
                  color: daltiTextMuted,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
                textAlign: TextAlign.center,
              ),
              getVerSpace(FetchPixels.getPixelHeight(20)),
              TextButton.icon(
                onPressed: _loadConversations,
                icon: Icon(Icons.refresh),
                label: Text('Retry'),
                style: TextButton.styleFrom(foregroundColor: daltiPrimary),
              ),
            ],
          ),
        ),
      );
    }

    return Center(
      child: Padding(
        padding: EdgeInsets.all(FetchPixels.getPixelHeight(20)),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.message_outlined,
              size: FetchPixels.getPixelHeight(80),
              color: daltiIconDefault.withOpacity(0.5),
            ),
            getVerSpace(FetchPixels.getPixelHeight(20)),
            Text(
              localizations.messagesEmptyTitle,
              style: TextStyle(
                color: daltiTextMuted,
                fontSize: 18,
                fontWeight: FontWeight.w600,
              ),
              textAlign: TextAlign.center,
            ),
            getVerSpace(FetchPixels.getPixelHeight(8)),
            Text(
              localizations.messagesEmptySubtitle,
              style: TextStyle(color: daltiTextMuted, fontSize: 14),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  String? _currentUserId() {
    return "current_user_placeholder_id";
  }
}

// Placeholder for getDecorationAssetImage if it's not available globally or via widget_utils
// This is just for the dummy data avatar part, ideally, your actual app has this.
// DecorationImage getDecorationAssetImage(BuildContext context, String assetName, {BoxFit fit = BoxFit.cover}) {
//   return DecorationImage(
//     image: AssetImage(assetName), // Assuming assetName includes full path like 'assets/images/...'
//     fit: fit,
//   );
// }
