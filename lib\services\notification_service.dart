import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart'; // For kDebugMode

// Assuming these constants are available, e.g., from a common constants file
// import 'package:dalti/base/constant.dart'; // Ideal: if prefsKeySessionId is here
const String prefsKeySessionId = 'session_id'; // Placeholder: Replace or import
const String baseApiUrl =
    "https://dapi-test.adscloud.org:8443"; // From your auth_utils

// Actor model for notifications
class ActorModel {
  final String id;
  final String username;
  final String firstName;
  final String lastName;

  ActorModel({
    required this.id,
    required this.username,
    required this.firstName,
    required this.lastName,
  });

  factory ActorModel.fromJson(Map<String, dynamic> json) {
    return ActorModel(
      id: json['id'] as String,
      username: json['username'] as String? ?? '',
      firstName: json['firstName'] as String? ?? '',
      lastName: json['lastName'] as String? ?? '',
    );
  }

  String get displayName => '$firstName $lastName'.trim();
}

// Updated Notification model based on the new API response
class NotificationModel {
  final String id;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String userId;
  final String type;
  final String title;
  final String message;
  final bool isRead;
  final DateTime? readAt;
  final String? link;
  final String? actorId;
  final ActorModel? actor;

  NotificationModel({
    required this.id,
    required this.createdAt,
    required this.updatedAt,
    required this.userId,
    required this.type,
    required this.title,
    required this.message,
    required this.isRead,
    this.readAt,
    this.link,
    this.actorId,
    this.actor,
  });

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      userId: json['userId'] as String,
      type: json['type'] as String,
      title: json['title'] as String,
      message: json['message'] as String,
      isRead: json['isRead'] as bool,
      readAt:
          json['readAt'] != null
              ? DateTime.parse(json['readAt'] as String)
              : null,
      link: json['link'] as String?,
      actorId: json['actorId'] as String?,
      actor:
          json['actor'] != null
              ? ActorModel.fromJson(json['actor'] as Map<String, dynamic>)
              : null,
    );
  }
}

class NotificationApiService {
  Future<Map<String, String>> _getHeaders() async {
    final prefs = await SharedPreferences.getInstance();
    final sessionId = prefs.getString(prefsKeySessionId);
    if (sessionId == null || sessionId.isEmpty) {
      throw Exception('User not authenticated. Session ID is missing.');
    }
    return {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer $sessionId',
    };
  }

  Future<List<NotificationModel>> getUserNotifications({
    int? limit,
    int? offset,
    bool? unreadOnly,
  }) async {
    final Map<String, String> queryParams = {};
    if (limit != null) queryParams['limit'] = limit.toString();
    if (offset != null) queryParams['offset'] = offset.toString();
    if (unreadOnly != null) queryParams['unreadOnly'] = unreadOnly.toString();

    final uri = Uri.parse(
      '$baseApiUrl/api/auth/notifications/mobile/list',
    ).replace(queryParameters: queryParams.isNotEmpty ? queryParams : null);

    if (kDebugMode) {
      print("Fetching notifications from: $uri");
    }

    try {
      final headers = await _getHeaders();
      final response = await http.get(uri, headers: headers);

      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);
        return responseData
            .map(
              (data) =>
                  NotificationModel.fromJson(data as Map<String, dynamic>),
            )
            .toList();
      } else if (response.statusCode == 401) {
        if (kDebugMode) {
          print(
            "Error fetching notifications: ${response.statusCode} ${response.body}",
          );
        }
        throw Exception('User not authenticated.');
      } else {
        if (kDebugMode) {
          print(
            "Error fetching notifications: ${response.statusCode} ${response.body}",
          );
        }
        throw Exception(
          'Failed to get notifications. Status: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print("Exception fetching notifications: $e");
      }
      // Rethrowing allows the UI layer to handle specific error types if needed
      // Or, return an empty list or a custom error object
      throw Exception('Failed to get notifications: $e');
    }
  }

  Future<NotificationModel> markNotificationAsRead(
    String notificationId,
  ) async {
    final uri = Uri.parse(
      '$baseApiUrl/api/auth/notifications/mobile/mark-as-read',
    );
    final requestBody = jsonEncode({'notificationId': notificationId});

    if (kDebugMode) {
      print("Marking notification $notificationId as read at: $uri");
    }

    try {
      final headers = await _getHeaders();
      final response = await http.post(
        uri,
        headers: headers,
        body: requestBody,
      );

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        return NotificationModel.fromJson(responseData);
      } else if (response.statusCode == 400) {
        throw Exception(
          'Bad Request: notificationId is likely missing or invalid.',
        );
      } else if (response.statusCode == 401) {
        throw Exception('User not authenticated.');
      } else if (response.statusCode == 403) {
        throw Exception(
          'Forbidden: User not authorized to update this notification.',
        );
      } else if (response.statusCode == 404) {
        throw Exception('Notification not found.');
      } else {
        if (kDebugMode) {
          print(
            "Error marking notification as read: ${response.statusCode} ${response.body}",
          );
        }
        throw Exception(
          'Failed to mark notification as read. Status: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print("Exception marking notification as read: $e");
      }
      // Rethrow specific exceptions if caught, or the generic one
      if (e is Exception) rethrow;
      throw Exception('Failed to mark notification as read: $e');
    }
  }

  Future<Map<String, dynamic>> markAllNotificationsAsRead() async {
    final uri = Uri.parse(
      '$baseApiUrl/api/auth/notifications/mobile/mark-all-as-read',
    );

    if (kDebugMode) {
      print("Marking all notifications as read at: $uri");
    }

    try {
      final headers = await _getHeaders();
      // API expects no body for this POST request
      final response = await http.post(uri, headers: headers);

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);
        // Expects: {"count": number}
        if (kDebugMode) {
          print("Mark all as read successful: ${response.body}");
        }
        return responseData;
      } else if (response.statusCode == 401) {
        throw Exception('User not authenticated.');
      } else {
        if (kDebugMode) {
          print(
            "Error marking all notifications as read: ${response.statusCode} ${response.body}",
          );
        }
        throw Exception(
          'Failed to mark all notifications as read. Status: ${response.statusCode}',
        );
      }
    } catch (e) {
      if (kDebugMode) {
        print("Exception marking all notifications as read: $e");
      }
      if (e is Exception) rethrow;
      throw Exception('Failed to mark all notifications as read: $e');
    }
  }
}
