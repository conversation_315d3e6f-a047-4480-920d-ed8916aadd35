import 'dart:convert';
import 'package:dalti/base/auth_utils.dart';
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:dalti/app/routes/app_routes.dart';
import 'package:dalti/base/constant.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:flutter/material.dart';
import 'package:dalti/l10n/app_localizations.dart';

import '../../../../base/color_data.dart';
import '../../../../base/widget_utils.dart';

// Define the key consistent with login_screen.dart
// const String prefsKeyUserProfile = 'user_profile'; // No longer needed here, auth_utils handles it
// const String prefsKeySessionId = 'session_id'; // No longer needed here, auth_utils handles it

class TabProfile extends StatefulWidget {
  const TabProfile({Key? key}) : super(key: key);

  @override
  State<TabProfile> createState() => _TabProfileState();
}

class _TabProfileState extends State<TabProfile> {
  bool _isLoggingOut = false; // For loading indicator on logout button

  String _userFirstName = ""; // New state variable
  String _userLastName = ""; // New state variable
  String _userEmailOrPhone = " "; // Default, will be updated
  String? _userProfileImageUrl;
  bool _isLoadingProfile = true; // Start in loading state
  String _currentDisplayName = ""; // Will hold the text for the name field

  @override
  void initState() {
    super.initState();
    // Do not use AppLocalizations here
    _loadUserProfileData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // AppLocalizations are available here. Set initial loading text.
    if (_isLoadingProfile && _currentDisplayName.isEmpty) {
      // Check _currentDisplayName.isEmpty to prevent resetting if already set by a completed _loadUserProfileData
      final localizations = AppLocalizations.of(context)!;
      setState(() {
        _currentDisplayName = localizations.profileLoading;
      });
    }
  }

  // Accepts parsed profile data directly
  Future<void> _parseAndSetProfileData(
    Map<String, dynamic> profileDataJSON,
    AppLocalizations localizations, // Receive localizations
  ) async {
    try {
      Map<String, dynamic> profileData = profileDataJSON['json'];
      String firstName = profileData['firstName'] as String? ?? '';
      String lastName = profileData['lastName'] as String? ?? '';
      String email = profileData['email'] as String? ?? '';
      String phoneNumber = profileData['mobileNumber'] as String? ?? '';
      // String? avatarUrl = profileData['avatarUrl'] as String?; // Example

      String displayNameValue = "$firstName $lastName".trim();
      if (displayNameValue.isEmpty) {
        displayNameValue =
            profileData['username'] as String? ??
            localizations.profileUserFallback;
      }

      String displayContact = email.isNotEmpty ? email : phoneNumber;
      if (displayContact.isEmpty) {
        displayContact =
            profileData['username'] as String? ??
            localizations.profileErrorDisplayDetails;
      }

      if (mounted) {
        setState(() {
          _userFirstName = firstName;
          _userLastName = lastName;
          _currentDisplayName = displayNameValue;
          _userEmailOrPhone = displayContact;
          // _userProfileImageUrl = avatarUrl;
          _isLoadingProfile = false;
        });
      }
    } catch (e) {
      print("Error processing profile data in TabProfile: $e");
      if (mounted) {
        setState(() {
          _userFirstName = "";
          _userLastName = "";
          _currentDisplayName = localizations.profileUserFallback;
          _userEmailOrPhone = localizations.profileErrorDisplayDetails;
          _isLoadingProfile = false;
        });
      }
    }
  }

  Future<void> _loadUserProfileData() async {
    if (mounted) {
      setState(() {
        _isLoadingProfile = true;
        // Initial _currentDisplayName for loading is set in didChangeDependencies
      });
    }

    // Use the utility function from auth_utils.dart
    final profileData = await checkUserSessionAndFetchProfile(context);
    print("profileData: $profileData");

    if (mounted) {
      final localizations = AppLocalizations.of(context)!;
      if (profileData != null) {
        // Profile data fetched successfully (or was already in prefs and validated)
        await _parseAndSetProfileData(profileData, localizations);
      } else {
        // checkUserSessionAndFetchProfile handles navigation to login if session is invalid or fetch fails.
        // If it returns null, it means user is likely being/has been redirected.
        // We can set a state that reflects this, though the screen might not be visible for long.
        setState(() {
          _userFirstName = "";
          _userLastName = "";
          _currentDisplayName = localizations.profileGuestUser;
          _userEmailOrPhone = localizations.profilePleaseLogIn;
          _isLoadingProfile = false;
        });
      }
    }
  }

  Future<void> _performLogout(BuildContext context) async {
    if (!mounted) return;
    final localizations = AppLocalizations.of(context)!;
    setState(() {
      _isLoggingOut = true;
    });

    // Simply call the utility from auth_utils.dart
    await clearAuthDataAndNavigateToLogin(context);

    // It's possible this widget is unmounted after the navigation above.
    // If there's any chance it isn't, or for robustness:
    if (mounted) {
      setState(() {
        _isLoggingOut = false;
        // Reset profile display as user is logged out
        _userFirstName = "";
        _userLastName = "";
        _currentDisplayName = localizations.profileGuestUser;
        _userEmailOrPhone = localizations.profilePleaseLogIn;
        _userProfileImageUrl = null;
        _isLoadingProfile = false; // Hide loading if it was somehow stuck
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    final localizations = AppLocalizations.of(context)!;

    // If didChangeDependencies hasn't run yet or some other edge case, ensure loading text.
    if (_isLoadingProfile && _currentDisplayName.isEmpty) {
      _currentDisplayName = localizations.profileLoading;
    }

    return Container(
      color: daltiBackground,
      padding: EdgeInsets.symmetric(horizontal: FetchPixels.getPixelWidth(20)),
      child: Column(
        children: [
          getVerSpace(FetchPixels.getPixelHeight(20)),
          withoutleftIconToolbar(
            context,
            isrightimage: true,
            title: localizations.profileTabTitle,
            weight: FontWeight.w800,
            textColor: daltiTextHeadline,
            fontsize: 24,
            istext: true,
            rightimage: "notification.svg",
          ),
          getVerSpace(FetchPixels.getPixelHeight(30)),
          _isLoadingProfile
              ? Padding(
                padding: EdgeInsets.symmetric(
                  vertical: FetchPixels.getPixelHeight(50),
                ),
                child: Column(
                  // Show loading text and spinner
                  children: [
                    CircularProgressIndicator(color: daltiPrimary),
                    getVerSpace(FetchPixels.getPixelHeight(10)),
                    getCustomFont(_currentDisplayName, 16, daltiTextMuted, 1),
                  ],
                ),
              )
              : profilePictureView(context, localizations),
          getVerSpace(FetchPixels.getPixelHeight(40)),
          Expanded(
            flex: 1,
            child: ListView(
              primary: true,
              shrinkWrap: true,
              physics: const BouncingScrollPhysics(),
              children: [
                myProfileButton(context, localizations),
                getVerSpace(FetchPixels.getPixelHeight(20)),
                myCardButton(context, localizations),
                getVerSpace(FetchPixels.getPixelHeight(20)),
                myAddressButton(context, localizations),
                getVerSpace(FetchPixels.getPixelHeight(20)),
                settingButton(context, localizations),
                getVerSpace(FetchPixels.getPixelHeight(30)),
                logoutButton(context, localizations),
                getVerSpace(FetchPixels.getPixelHeight(20)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget logoutButton(BuildContext context, AppLocalizations localizations) {
    return _isLoggingOut
        ? Center(child: CircularProgressIndicator(color: daltiPrimary))
        : getButton(
          context,
          daltiPrimary,
          localizations.profileLogoutButton,
          daltiTextOnPrimary,
          () async {
            await _performLogout(context);
          },
          18,
          weight: FontWeight.w600,
          borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(14)),
          buttonHeight: FetchPixels.getPixelHeight(60),
        );
  }

  Widget settingButton(BuildContext context, AppLocalizations localizations) {
    return getButtonWithIcon(
      context,
      daltiCard,
      localizations.profileSettingsButton,
      daltiTextBody,
      () {
        Constant.sendToNext(context, Routes.settingRoute);
      },
      16,
      weight: FontWeight.w400,
      buttonHeight: FetchPixels.getPixelHeight(60),
      borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
      boxShadow: [
        BoxShadow(
          color: daltiDividerLine.withOpacity(0.4),
          blurRadius: 8,
          offset: const Offset(0.0, 2.0),
        ),
      ],
      prefixIcon: true,
      prefixImage: "setting.svg",
      imageColor: daltiIconDefault,
      sufixIcon: true,
      suffixImage: "arrow_right.svg",
    );
  }

  Widget myAddressButton(BuildContext context, AppLocalizations localizations) {
    return getButtonWithIcon(
      context,
      daltiCard,
      localizations.profileMyAddressButton,
      daltiTextBody,
      () {
        Constant.sendToNext(context, Routes.myAddressRoute);
      },
      16,
      weight: FontWeight.w400,
      buttonHeight: FetchPixels.getPixelHeight(60),
      borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
      boxShadow: [
        BoxShadow(
          color: daltiDividerLine.withOpacity(0.4),
          blurRadius: 8,
          offset: const Offset(0.0, 2.0),
        ),
      ],
      prefixIcon: true,
      prefixImage: "location.svg",
      imageColor: daltiIconDefault,
      sufixIcon: true,
      suffixImage: "arrow_right.svg",
    );
  }

  Widget myCardButton(BuildContext context, AppLocalizations localizations) {
    return getButtonWithIcon(
      context,
      daltiCard,
      localizations.profileMyCardsButton,
      daltiTextBody,
      () {
        Constant.sendToNext(context, Routes.cardRoute);
      },
      16,
      weight: FontWeight.w400,
      buttonHeight: FetchPixels.getPixelHeight(60),
      borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
      boxShadow: [
        BoxShadow(
          color: daltiDividerLine.withOpacity(0.4),
          blurRadius: 8,
          offset: const Offset(0.0, 2.0),
        ),
      ],
      prefixIcon: true,
      prefixImage: "wallet.svg",
      imageColor: daltiIconDefault,
      sufixIcon: true,
      suffixImage: "arrow_right.svg",
    );
  }

  Widget myProfileButton(BuildContext context, AppLocalizations localizations) {
    return getButtonWithIcon(
      context,
      daltiCard,
      localizations.profileMyProfileButton,
      daltiTextBody,
      () {
        Constant.sendToNext(context, Routes.profileRoute);
      },
      16,
      weight: FontWeight.w400,
      buttonHeight: FetchPixels.getPixelHeight(60),
      borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
      boxShadow: [
        BoxShadow(
          color: daltiDividerLine.withOpacity(0.4),
          blurRadius: 8,
          offset: const Offset(0.0, 2.0),
        ),
      ],
      prefixIcon: true,
      prefixImage: "profile.svg",
      imageColor: daltiIconDefault,
      sufixIcon: true,
      suffixImage: "arrow_right.svg",
    );
  }

  Widget profilePictureView(
    BuildContext context,
    AppLocalizations localizations,
  ) {
    return Column(
      children: [
        Stack(
          clipBehavior: Clip.none,
          alignment: Alignment.center,
          children: [
            Container(
              height: FetchPixels.getPixelHeight(100),
              width: FetchPixels.getPixelHeight(100),
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: daltiPrimaryLight,
                image:
                    _userProfileImageUrl != null
                        ? DecorationImage(
                          image: NetworkImage(_userProfileImageUrl!),
                          fit: BoxFit.cover,
                        )
                        : null,
              ),
              child:
                  _userProfileImageUrl == null
                      ? Center(
                        child: getSvgImage(
                          "user.svg",
                          width: FetchPixels.getPixelHeight(50),
                          height: FetchPixels.getPixelHeight(50),
                          color: daltiPrimary,
                        ),
                      )
                      : null,
            ),
            Positioned(
              bottom: FetchPixels.getPixelHeight(0),
              right: FetchPixels.getPixelWidth(0),
              child: InkWell(
                onTap: () {
                  Constant.sendToNext(context, Routes.editProfileRoute);
                },
                child: Container(
                  height: FetchPixels.getPixelHeight(34),
                  width: FetchPixels.getPixelHeight(34),
                  padding: EdgeInsets.all(FetchPixels.getPixelHeight(8)),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: daltiCard,
                    boxShadow: [
                      BoxShadow(
                        color: daltiDividerLine.withOpacity(0.5),
                        blurRadius: 5,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: getSvgImage("edit.svg", color: daltiPrimary),
                ),
              ),
            ),
          ],
        ),
        getVerSpace(FetchPixels.getPixelHeight(16)),
        getCustomFont(
          _currentDisplayName, // Use the new state variable for display name
          20,
          daltiTextHeadline,
          1,
          fontWeight: FontWeight.bold,
        ),
        getVerSpace(FetchPixels.getPixelHeight(6)),
        getCustomFont(
          _userEmailOrPhone,
          14,
          daltiTextMuted,
          1,
          fontWeight: FontWeight.normal,
        ),
      ],
    );
  }
}
