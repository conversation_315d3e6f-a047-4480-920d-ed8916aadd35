import 'dart:convert';
import 'package:dalti/base/auth_utils.dart'; // Import auth_utils
import 'package:http/http.dart' as http;
import 'package:dalti/app/models/model_address.dart'; // Assuming ModelAddress exists and is correctly pathed

class AddressService {
  final String _apiBaseUrl =
      "https://dapi-test.adscloud.org:8443"; // Example, replace with your actual base URL if different
  final String _customerAddressPath = "/api/auth/customer/addresses";

  Future<String> _getAuthToken() async {
    String? sessionId = await getSessionId();
    if (sessionId == null || sessionId.isEmpty) {
      throw Exception('Authentication token not found. Please log in.');
    }
    return sessionId;
  }

  // Method to get customer addresses
  Future<List<ModelAddress>> getCustomerAddresses() async {
    final token = await _getAuthToken();
    final response = await http.get(
      Uri.parse('$_apiBaseUrl$_customerAddressPath'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 200) {
      List<dynamic> body = jsonDecode(response.body);
      List<ModelAddress> addresses =
          body.map((dynamic item) => ModelAddress.fromJson(item)).toList();
      return addresses;
    } else if (response.statusCode == 401) {
      throw Exception(
        'Unauthorized. Please log in again.',
      ); // Specific error for 401
    } else {
      // You could parse error message from response.body if API provides one
      throw Exception(
        'Failed to load addresses. Status: ${response.statusCode}',
      );
    }
  }

  // Method to create customer address
  Future<ModelAddress> createCustomerAddress(ModelAddress address) async {
    final token = await _getAuthToken();
    final response = await http.post(
      Uri.parse('$_apiBaseUrl$_customerAddressPath'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode(address.toJson()),
    );

    if (response.statusCode == 201) {
      return ModelAddress.fromJson(jsonDecode(response.body));
    } else if (response.statusCode == 401) {
      throw Exception('Unauthorized. Please log in again.');
    } else {
      throw Exception(
        'Failed to create address. Status: ${response.statusCode}',
      );
    }
  }

  // Method to update customer address
  Future<ModelAddress> updateCustomerAddress(
    String
    addressId, // addressId should come from the address object itself if possible, e.g. address.id
    ModelAddress address,
  ) async {
    final token = await _getAuthToken();
    // Ensure addressId is not null or empty
    if (addressId.isEmpty) {
      throw Exception('Address ID cannot be empty for update.');
    }
    final response = await http.put(
      Uri.parse('$_apiBaseUrl$_customerAddressPath/$addressId'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
      body: jsonEncode(address.toJson()),
    );

    if (response.statusCode == 200) {
      return ModelAddress.fromJson(jsonDecode(response.body));
    } else if (response.statusCode == 401) {
      throw Exception('Unauthorized. Please log in again.');
    } else {
      throw Exception(
        'Failed to update address. Status: ${response.statusCode}',
      );
    }
  }

  // Method to delete customer address
  Future<void> deleteCustomerAddress(String addressId) async {
    final token = await _getAuthToken();
    if (addressId.isEmpty) {
      throw Exception('Address ID cannot be empty for delete.');
    }
    final response = await http.delete(
      Uri.parse('$_apiBaseUrl$_customerAddressPath/$addressId'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $token',
      },
    );

    if (response.statusCode == 204) {
      return; // Success
    } else if (response.statusCode == 401) {
      throw Exception('Unauthorized. Please log in again.');
    } else {
      throw Exception(
        'Failed to delete address. Status: ${response.statusCode}',
      );
    }
  }
}
