pluginManagement {
    def flutterSdkPath = {
        def properties = new java.util.Properties()
        file("local.properties").newInputStream().withCloseable { properties.load(it) }
        def sdkPath = properties.getProperty("flutter.sdk")
        if (sdkPath == null) {
            throw new GradleException("flutter.sdk not set in local.properties")
        }
        return sdkPath
    }()

    includeBuild("$flutterSdkPath/packages/flutter_tools/gradle")

    repositories {
        google()
        mavenCentral()
        gradlePluginPortal()
    }
}

plugins {
    id "dev.flutter.flutter-plugin-loader" version "1.0.0"
    id "com.android.application" version "8.7.0" apply false
    id "org.jetbrains.kotlin.android" version "2.0.0" apply false
}

include(":app")
