import 'package:dalti/app/routes/app_routes.dart';
import 'package:dalti/base/color_data.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:flutter/material.dart';
import 'package:dalti/l10n/app_localizations.dart';

import '../../../base/constant.dart';

class ForgotPassword extends StatefulWidget {
  final String? preferredMethod;

  const ForgotPassword({Key? key, this.preferredMethod}) : super(key: key);

  @override
  State<ForgotPassword> createState() => _ForgotPasswordState();
}

class _ForgotPasswordState extends State<ForgotPassword> {
  void finish() {
    Constant.backToPrev(context);
  }

  TextEditingController emailOrPhoneController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    final localizations = AppLocalizations.of(context)!;
    bool isEmailPreferred = widget.preferredMethod == 'email';
    bool useEmailField = isEmailPreferred || widget.preferredMethod != 'mobile';

    String subtitle;
    if (useEmailField) {
      subtitle = localizations.forgotPasswordSubtitleEmail;
    } else {
      subtitle = localizations.forgotPasswordSubtitleMobile;
    }

    return WillPopScope(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: daltiBackground,
        body: SafeArea(
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: FetchPixels.getPixelWidth(20),
            ),
            child: Column(
              children: [
                getVerSpace(FetchPixels.getPixelHeight(26)),
                gettoolbarMenu(context, "back.svg", () {
                  finish();
                }),
                getVerSpace(FetchPixels.getPixelHeight(20)),
                getCustomFont(
                  localizations.forgotPasswordTitle,
                  24,
                  daltiTextHeadline,
                  1,
                  fontWeight: FontWeight.w800,
                ),
                getVerSpace(FetchPixels.getPixelHeight(10)),
                getPaddingWidget(
                  EdgeInsets.symmetric(
                    horizontal: FetchPixels.getPixelWidth(
                      useEmailField ? 20 : 40,
                    ),
                  ),
                  getMultilineCustomFont(
                    subtitle,
                    16,
                    daltiTextBody,
                    fontWeight: FontWeight.w400,
                    textAlign: TextAlign.center,
                    txtHeight: FetchPixels.getPixelHeight(1.3),
                  ),
                ),
                getVerSpace(FetchPixels.getPixelHeight(30)),
                if (useEmailField)
                  getDefaultTextFiledWithLabel(
                    context,
                    localizations.emailLabel,
                    emailOrPhoneController,
                    daltiTextMuted,
                    function: () {},
                    height: FetchPixels.getPixelHeight(60),
                    isEnable: false,
                    withprefix: true,
                    image: "message.svg",
                    imageWidth: FetchPixels.getPixelWidth(19),
                    imageHeight: FetchPixels.getPixelHeight(17.66),
                  )
                else
                  getDefaultTextFiledWithLabel(
                    context,
                    localizations.phoneNumberWithCodeLabel,
                    emailOrPhoneController,
                    daltiTextMuted,
                    function: () {},
                    height: FetchPixels.getPixelHeight(60),
                    isEnable: false,
                    withprefix: true,
                    image: "phone.svg",
                    imageWidth: FetchPixels.getPixelWidth(19),
                    imageHeight: FetchPixels.getPixelHeight(17.66),
                  ),
                getVerSpace(FetchPixels.getPixelHeight(50)),
                getButton(
                  context,
                  daltiPrimary,
                  localizations.submitButton,
                  daltiTextOnPrimary,
                  () {
                    // Redirect to new password reset flow
                    Constant.sendToNext(
                      context,
                      Routes.passwordResetRequestRoute,
                    );
                  },
                  18,
                  weight: FontWeight.w600,
                  buttonHeight: FetchPixels.getPixelHeight(60),
                  borderRadius: BorderRadius.circular(
                    FetchPixels.getPixelHeight(15),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      onWillPop: () async {
        finish();
        return false;
      },
    );
  }
}
