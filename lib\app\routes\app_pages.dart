import 'package:dalti/app/view/address/edit_address_screen.dart';
import 'package:dalti/app/view/address/my_address_screen.dart';
import 'package:dalti/app/view/bookings/booking_detail.dart';
import 'package:dalti/app/view/card/card_screen.dart';
import 'package:dalti/app/view/chat/message_screen.dart';
import 'package:dalti/app/view/home/<USER>';
import 'package:dalti/app/view/home/<USER>';
import 'package:dalti/app/view/home/<USER>';
import 'package:dalti/app/view/home/<USER>';
import 'package:dalti/app/view/home/<USER>';
import 'package:dalti/app/view/home/<USER>';
import 'package:dalti/app/view/home/<USER>';
import 'package:dalti/app/view/home/<USER>';
import 'package:dalti/app/view/intro/intro_screen.dart';
import 'package:dalti/app/view/login/forgot_password.dart';
import 'package:dalti/app/view/login/login_screen.dart';
import 'package:dalti/app/view/login/prefered_method.dart';
import 'package:dalti/app/view/login/reset_password.dart';
import 'package:dalti/app/view/login/password_reset_request_screen.dart';
import 'package:dalti/app/view/login/verify_reset_otp_screen.dart';
import 'package:dalti/app/view/login/new_password_screen.dart';
import 'package:dalti/app/view/notification_screen.dart';
import 'package:dalti/app/view/profile/edit_profile_screen.dart';
import 'package:dalti/app/view/profile/profile_screen.dart';
import 'package:dalti/app/view/search/search_screen.dart';
import 'package:dalti/app/view/setting/help_screen.dart';
import 'package:dalti/app/view/setting/privacy_screen.dart';
import 'package:dalti/app/view/setting/security_screen.dart';
import 'package:dalti/app/view/setting/setting_screen.dart';
import 'package:dalti/app/view/setting/term_of_service_screen.dart';
import 'package:dalti/app/view/signup/select_country.dart';
import 'package:dalti/app/view/signup/signup_screen.dart';
import 'package:dalti/app/view/signup/verify_screen.dart';

import 'package:flutter/material.dart';
import 'package:dalti/app/models/model_category.dart';
import 'package:dalti/app/models/model_doctor.dart';
import 'package:dalti/app/models/model_appointment.dart';
import 'package:dalti/app/view/home/<USER>/tab_messages.dart';
import 'package:dalti/app/view/queue/queue_management_screen.dart';
import '../view/splash_screen.dart';

import 'app_routes.dart';

class AppPages {
  static const initialRoute = Routes.homeRoute;

  static Route<dynamic> onGenerateRoute(RouteSettings settings) {
    switch (settings.name) {
      case Routes.homeRoute:
        return MaterialPageRoute(builder: (_) => const SplashScreen());
      case Routes.introRoute:
        return MaterialPageRoute(builder: (_) => const IntroScreen());
      case Routes.loginRoute:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder:
              (_) => LoginScreen(
                preferredMethod: args?['preferred_method'] as String?,
              ),
        );
      case Routes.forgotRoute:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder:
              (_) => ForgotPassword(
                preferredMethod: args?['preferred_method'] as String?,
              ),
        );
      case Routes.resetRoute:
        return MaterialPageRoute(builder: (_) => const ResetPassword());
      case Routes.passwordResetRequestRoute:
        return MaterialPageRoute(
          builder: (_) => const PasswordResetRequestScreen(),
        );
      case Routes.verifyResetOtpRoute:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder: (_) => VerifyResetOtpScreen(email: args['email'] as String),
        );
      case Routes.newPasswordRoute:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder:
              (_) => NewPasswordScreen(
                resetToken: args['resetToken'] as String,
                email: args['email'] as String,
              ),
        );
      case Routes.signupRoute:
        final args = settings.arguments as Map<String, dynamic>?;
        return MaterialPageRoute(
          builder:
              (_) => SignUpScreen(
                preferredMethod: args?['preferred_method'] as String?,
              ),
        );
      case Routes.selectCountryRoute:
        return MaterialPageRoute(builder: (_) => const SelectCountry());
      case Routes.verifyRoute:
        final args = settings.arguments as Map<String, dynamic>;
        return MaterialPageRoute(
          builder:
              (_) => VerifyScreen(
                firstName: args['firstName'] as String,
                lastName: args['lastName'] as String,
                email: args['email'] as String?,
                phoneNumber: args['phoneNumber'] as String,
                password: args['password'] as String,
                preferredMethodForOtp:
                    args['preferred_method_for_otp'] as String?,
              ),
        );
      case Routes.preferredMethodRoute:
        return MaterialPageRoute(builder: (_) => const PreferredMethodScreen());
      case Routes.homeScreenRoute:
        final Object? args = settings.arguments;
        int initialIndex = 0; // Default to 0
        if (args is int) {
          initialIndex = args;
        }
        return MaterialPageRoute(builder: (_) => HomeScreen(initialIndex));
      case Routes.categoryRoute:
        ModelCategory? initialParent;
        if (settings.arguments is ModelCategory) {
          initialParent = settings.arguments as ModelCategory;
        }
        return MaterialPageRoute(
          builder: (_) => CategoryScreen(initialSelectedParent: initialParent),
        );
      case Routes.detailRoute:
        if (settings.arguments is ModelDoctor) {
          final doctor = settings.arguments as ModelDoctor;
          return MaterialPageRoute(
            builder: (_) => DetailScreen(doctor: doctor),
          );
        }
        return _errorRoute(settings.name);
      case Routes.cartRoute:
        return MaterialPageRoute(builder: (_) => const CartScreen());
      case Routes.addressRoute:
        return MaterialPageRoute(builder: (_) => const AddressScreen());
      case Routes.dateTimeRoute:
        return MaterialPageRoute(builder: (_) => const DateTimeScreen());
      case Routes.paymentRoute:
        return MaterialPageRoute(builder: (_) => const PaymentScreen());
      case Routes.orderDetailRoute:
        return MaterialPageRoute(builder: (_) => const OrderDetail());
      case Routes.profileRoute:
        return MaterialPageRoute(builder: (_) => const ProfileScreen());
      case Routes.editProfileRoute:
        return MaterialPageRoute(builder: (_) => const EditProfileScreen());
      case Routes.myAddressRoute:
        return MaterialPageRoute(builder: (_) => const MyAddressScreen());
      case Routes.editAddressRoute:
        return MaterialPageRoute(builder: (_) => const EditAddressScreen());
      case Routes.cardRoute:
        return MaterialPageRoute(builder: (_) => const CardScreen());
      case Routes.settingRoute:
        return MaterialPageRoute(builder: (_) => const SettingScreen());
      case Routes.notificationRoutes:
        return MaterialPageRoute(builder: (_) => const NotificationScreen());
      case Routes.searchRoute:
        ModelCategory? initialCategory;
        if (settings.arguments is Map<String, dynamic>) {
          final argsMap = settings.arguments as Map<String, dynamic>;
          if (argsMap.containsKey('category') &&
              argsMap['category'] is ModelCategory) {
            initialCategory = argsMap['category'] as ModelCategory?;
          }
        } else if (settings.arguments is ModelCategory) {
          initialCategory = settings.arguments as ModelCategory?;
        }
        return MaterialPageRoute(
          builder: (_) => SearchScreen(initialCategory: initialCategory),
        );
      case Routes.bookingRoute:
        if (settings.arguments is ModelAppointment) {
          final appointment = settings.arguments as ModelAppointment;
          return MaterialPageRoute(
            builder: (_) => BookingDetail(appointment: appointment),
          );
        }
        return _errorRoute(settings.name);
      case Routes.messageScreenRoute:
        if (settings.arguments is ModelMessage) {
          final recipient = settings.arguments as ModelMessage;
          return MaterialPageRoute(
            builder:
                (_) => MessageScreen(
                  recipient: recipient,
                  conversationId: recipient.conversationId,
                ),
          );
        }
        return _errorRoute(settings.name);
      case Routes.helpRoute:
        return MaterialPageRoute(builder: (_) => const HelpScreen());
      case Routes.privacyRoute:
        return MaterialPageRoute(builder: (_) => const PrivacyScreen());
      case Routes.securityRoute:
        return MaterialPageRoute(builder: (_) => const SecurityScreen());
      case Routes.termOfServiceRoute:
        return MaterialPageRoute(builder: (_) => const TermOfServiceScreen());
      case Routes.queueManagementRoute:
        return MaterialPageRoute(builder: (_) => const QueueManagementScreen());
      default:
        return _errorRoute(settings.name);
    }
  }

  static Route<dynamic> _errorRoute(String? routeName) {
    return MaterialPageRoute(
      builder:
          (_) => Scaffold(
            appBar: AppBar(title: const Text('Error')),
            body: Center(child: Text('Route not found: $routeName')),
          ),
    );
  }
}
