// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get helloWorld => 'Hello World!';

  @override
  String get welcomeMessage => 'Welcome to our App';

  @override
  String get chooseLanguage => 'Choose Your Language';

  @override
  String get english => 'English';

  @override
  String get arabic => 'Arabic';

  @override
  String get french => 'French';

  @override
  String get nextButtonText => 'Next';

  @override
  String get skipButtonText => 'Skip';

  @override
  String get intro1Title => 'Intro Slide 1 Title (EN)';

  @override
  String get intro1Description => 'Description for intro slide 1 (EN).';

  @override
  String get intro2Title => 'Intro Slide 2 Title (EN)';

  @override
  String get intro2Description => 'Description for intro slide 2 (EN).';

  @override
  String get intro3Title => 'Intro Slide 3 Title (EN)';

  @override
  String get intro3Description => 'Description for intro slide 3 (EN).';

  @override
  String get preferredMethodTitle => 'Choose Your Preferred Method';

  @override
  String get preferredMethodSubtitle =>
      'Select how you\'d like to sign up or log in.';

  @override
  String get useEmailButton => 'Use Email';

  @override
  String get useMobileButton => 'Use Mobile Number';

  @override
  String get goBackButton => 'Go Back';

  @override
  String get loginWithEmailTitle => 'Login with Email';

  @override
  String get loginWithMobileTitle => 'Login with Mobile';

  @override
  String get loginSubtitle => 'Glad to meet you again!';

  @override
  String get emailLabel => 'Email';

  @override
  String get phoneNumberLabel => 'Phone Number';

  @override
  String get passwordLabel => 'Password';

  @override
  String get forgotPasswordButton => 'Forgot Password?';

  @override
  String get loginButton => 'Login';

  @override
  String get dontHaveAccountText => 'Don\'t have an account?';

  @override
  String get signUpButtonLoginScreen => ' Sign Up';

  @override
  String get errorPhoneNumberEmpty => 'Phone number cannot be empty.';

  @override
  String get errorEmailPasswordEmpty => 'Email and Password cannot be empty.';

  @override
  String get errorPhonePasswordEmpty =>
      'Phone number and Password cannot be empty.';

  @override
  String errorLoginInvalidResponse(Object statusCode) {
    return 'Login failed: Invalid server response. Status: $statusCode';
  }

  @override
  String get loginSuccess => 'Login Successful!';

  @override
  String get errorLoginNoSessionId => 'Login failed: Session ID not received.';

  @override
  String errorLoginWithMessageStatus(Object message, Object statusCode) {
    return 'Login failed: $message (Status: $statusCode)';
  }

  @override
  String errorLoginGeneric(Object errorDetails) {
    return 'An error occurred during login: $errorDetails';
  }

  @override
  String get signUpTitle => 'Sign Up';

  @override
  String get signUpSubtitleEmail => 'Sign up using your Email.';

  @override
  String get signUpSubtitleMobile => 'Sign up using your Mobile Number.';

  @override
  String get signUpSubtitleGeneric => 'Enter your details for sign up!';

  @override
  String get firstNameLabel => 'First Name';

  @override
  String get lastNameLabel => 'Last Name';

  @override
  String get agreeTermsPrivacyText => 'I agree with Terms & Privacy';

  @override
  String get signUpButton => 'Sign Up';

  @override
  String get alreadyHaveAccountText => 'Already have an account?';

  @override
  String get loginButtonSignUpScreen => ' Login';

  @override
  String get errorAgreeTerms => 'Please agree to the Terms & Privacy.';

  @override
  String get errorEmailEmptyPreferred =>
      'Email cannot be empty as it\'s your preferred method.';

  @override
  String get errorMobileEmptyPreferred =>
      'Phone number cannot be empty as it\'s your preferred method.';

  @override
  String get errorProvideEmailOrPhone =>
      'Please provide either an Email or a Phone Number.';

  @override
  String get errorProvideSignUpDetails => 'Please provide details for sign up.';

  @override
  String errorUserExists(Object fieldInUse) {
    return '$fieldInUse already in use.';
  }

  @override
  String errorUserExistsConflict(Object fieldInUse) {
    return '$fieldInUse already in use (conflict).';
  }

  @override
  String errorCheckingUser(Object serverMessage, Object statusCode) {
    return 'Error checking user: $statusCode $serverMessage';
  }

  @override
  String errorFailedCheckUserExists(Object errorDetails) {
    return 'Failed to check user existence: $errorDetails';
  }

  @override
  String get errorNoValidOtpIdentifier =>
      'Cannot proceed without a valid email or phone for OTP.';

  @override
  String get forgotPasswordTitle => 'Forgot Password?';

  @override
  String get forgotPasswordSubtitleEmail =>
      'Enter your email to reset your password.';

  @override
  String get forgotPasswordSubtitleMobile =>
      'Enter your phone number to reset your password.';

  @override
  String get phoneNumberWithCodeLabel => 'Phone Number (Requires Country Code)';

  @override
  String get submitButton => 'Submit';

  @override
  String get resetPasswordTitle => 'Reset Password';

  @override
  String get resetPasswordSubtitle => 'Enter your new password details below.';

  @override
  String get oldPasswordLabel => 'Old Password';

  @override
  String get newPasswordLabel => 'New Password';

  @override
  String get confirmPasswordLabel => 'Confirm Password';

  @override
  String get setLocationDefault => 'Set Location';

  @override
  String get searchHintText => 'Search...';

  @override
  String get homeBannerTitle => 'Wall Painting Service';

  @override
  String get homeBannerSubtitle => 'Make your wall stylish';

  @override
  String get bookNowButton => 'Book Now';

  @override
  String get categoriesTitle => 'Categories';

  @override
  String get seeAllButton => 'See All';

  @override
  String errorLoadingCategories(Object errorMessage) {
    return 'Error: $errorMessage';
  }

  @override
  String get noCategoriesFound => 'No categories found.';

  @override
  String get noParentCategoriesFound => 'No parent categories found.';

  @override
  String get profileLoading => 'Loading...';

  @override
  String get profileUserFallback => 'User';

  @override
  String get profileErrorDisplayDetails => 'Error displaying details';

  @override
  String get profileGuestUser => 'Guest';

  @override
  String get profilePleaseLogIn => 'Please log in';

  @override
  String get profileTabTitle => 'Profile';

  @override
  String get profileLogoutButton => 'Logout';

  @override
  String get profileSettingsButton => 'Settings';

  @override
  String get profileMyAddressButton => 'My Address';

  @override
  String get profileMyCardsButton => 'My Cards';

  @override
  String get profileMyProfileButton => 'My Profile';

  @override
  String get bookingsTabTitle => 'Bookings';

  @override
  String get bookingsTabAll => 'All';

  @override
  String get bookingsTabActive => 'Active';

  @override
  String get bookingsTabCompleted => 'Completed';

  @override
  String get bookingsTabCancelled => 'Cancelled';

  @override
  String get messagesTabTitle => 'Messages';

  @override
  String get messagesSearchHint => 'Search messages...';

  @override
  String get messagesEmptyTitle => 'No Messages';

  @override
  String get messagesEmptySubtitle => 'Your message list is currently empty.';

  @override
  String get doctorPrefix => 'Dr. ';

  @override
  String get unnamedProvider => 'Unnamed Provider';

  @override
  String get defaultSpecialization => 'General';

  @override
  String get naHospital => 'N/A Hospital';

  @override
  String get slotInfoUnavailable => 'Slot information unavailable';

  @override
  String errorFailedToFetchProviders(Object statusCode) {
    return 'Failed to fetch providers (Status: $statusCode)';
  }

  @override
  String errorFetchingData(Object errorDetails) {
    return 'Error fetching data: $errorDetails';
  }

  @override
  String searchResultCountForQuery(num count, Object query) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count results',
      one: '$count result',
      zero: 'No results',
    );
    return '$_temp0 for \'\'$query\'\'';
  }

  @override
  String searchResultCountForQueryInCategory(
    Object category,
    num count,
    Object query,
  ) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count results',
      one: '$count result',
      zero: 'No results',
    );
    return '$_temp0 for \'\'$query\'\' in $category';
  }

  @override
  String searchResultCountForQueryInCity(Object city, num count, Object query) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count results',
      one: '$count result',
      zero: 'No results',
    );
    return '$_temp0 for \'\'$query\'\' in $city';
  }

  @override
  String searchResultCountForQueryInCategoryInCity(
    Object category,
    Object city,
    num count,
    Object query,
  ) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count results',
      one: '$count result',
      zero: 'No results',
    );
    return '$_temp0 for \'\'$query\'\' in $category in $city';
  }

  @override
  String noProvidersFoundForQuery(Object query) {
    return 'No providers found for \'\'$query\'\'.';
  }

  @override
  String noProvidersFoundForQueryWithCategory(Object category, Object query) {
    return 'No providers found for \'\'$query\'\'\nCategory: $category';
  }

  @override
  String noProvidersFoundForQueryWithCity(Object city, Object query) {
    return 'No providers found for \'\'$query\'\'\nCity: $city';
  }

  @override
  String noProvidersFoundForQueryWithCategoryAndCity(
    Object category,
    Object city,
    Object query,
  ) {
    return 'No providers found for \'\'$query\'\'\nCategory: $category\nCity: $city';
  }

  @override
  String get searchScreenTitle => 'Search';

  @override
  String get searchByKeywordHint => 'Search by keyword';

  @override
  String get hospitalPrefix => 'at ';

  @override
  String experienceInYears(Object years) {
    return 'Exp. $years years';
  }

  @override
  String feesDisplay(Object amount, Object currencySymbol) {
    return 'Fees $currencySymbol$amount';
  }

  @override
  String reviewCountDisplay(Object count) {
    return '($count reviews)';
  }

  @override
  String get nextAvailableSlotTitle => 'Next Available Slot';

  @override
  String get filtersModalTitle => 'Filters';

  @override
  String get filtersCategorySectionTitle => 'Service Type (Category)';

  @override
  String get filtersSelectCategoryPlaceholder => 'Select Category';

  @override
  String get filtersCitySectionTitle => 'City (Wilaya)';

  @override
  String get filtersEnterCityHint => 'Enter city or Wilaya';

  @override
  String get filtersClearButton => 'Clear Filters';

  @override
  String get filtersApplyButton => 'Apply Filters';

  @override
  String get filtersAllCategoriesTitle => 'All Categories';

  @override
  String get filtersNoSubcategories => 'No sub-categories.';

  @override
  String get filtersNoCategoriesAvailable => 'No categories available.';

  @override
  String errorFetchingCategories(Object errorDetails) {
    return 'Error fetching categories: $errorDetails';
  }

  @override
  String get detailScreenCannotSelectPastDate => 'Cannot select a past date.';

  @override
  String get detailScreenBookAppointment => 'Book an Appointment';

  @override
  String get detailScreenServiceLabel => 'Service:';

  @override
  String get detailScreenNoServices => 'No services available.';

  @override
  String get detailScreenQueueLabel => 'Queue:';

  @override
  String get detailScreenGeneralAvailability =>
      'General availability for this service.';

  @override
  String get detailScreenSelectDate => 'Select Date';

  @override
  String get detailScreenSelectTime => 'Select Time';

  @override
  String get detailScreenPleaseSelectDate => 'Please select a date.';

  @override
  String detailScreenNoSlotsFound(Object date) {
    return 'No slots found for $date.';
  }

  @override
  String detailScreenAllSlotsBooked(Object date) {
    return 'All slots are booked for $date.';
  }

  @override
  String get detailScreenConfirmBooking => 'Confirm Booking';

  @override
  String get detailScreenCancel => 'Cancel';

  @override
  String get detailScreenConfirm => 'Confirm';

  @override
  String get detailScreenIncompleteDetails => 'Booking details are incomplete.';

  @override
  String get detailScreenPleaseLogin => 'Please log in to make a booking.';

  @override
  String get detailScreenBookingSuccess => 'Booking Successful!';

  @override
  String get detailScreenBookingFailed => 'Booking failed. Please try again.';

  @override
  String detailScreenErrorOccurred(Object error) {
    return 'An error occurred: $error';
  }

  @override
  String detailScreenPointsRequired(Object points) {
    return 'This service requires $points points to book.';
  }

  @override
  String get detailScreenNoPhoneNumber => 'Phone number is not available.';

  @override
  String get detailScreenCannotLaunchCall => 'Could not launch phone call.';

  @override
  String get detailScreenServiceDetail => 'Service:';

  @override
  String get detailScreenQueueDetail => 'Queue:';

  @override
  String get detailScreenDateDetail => 'Date:';

  @override
  String get detailScreenTimeDetail => 'Time:';

  @override
  String detailScreenBook(Object date, Object time) {
    return 'Book: $time on $date';
  }

  @override
  String get settingsSecurityTitle => 'Security';

  @override
  String get settingsHelpTitle => 'Help & Support';

  @override
  String get settingsPrivacyTitle => 'Privacy Policy';

  @override
  String get settingsTermsTitle => 'Terms of Service';

  @override
  String get settingsSecurityContent =>
      'Your security is our top priority. We implement industry-standard security measures to protect your data and ensure a safe experience while using our services.';

  @override
  String get settingsHelpContent =>
      'Need assistance? Our support team is here to help you with any questions or concerns you may have about our services.';

  @override
  String get settingsPrivacyContent =>
      'We value your privacy. This policy outlines how we collect, use, and protect your personal information when you use our services.';

  @override
  String get settingsTermsContent =>
      'By using our services, you agree to these terms. Please read them carefully as they govern your use of our platform and services.';

  @override
  String get notificationsTitle => 'Notifications';

  @override
  String get notificationsToday => 'Today';

  @override
  String get notificationsYesterday => 'Yesterday';

  @override
  String get notificationsRetry => 'Retry';

  @override
  String get notificationsEmpty => 'No Notifications Yet!';

  @override
  String get notificationsEmptyDesc =>
      'We\'ll notify you when something arrives.';

  @override
  String get queueStatusUpcoming => 'Upcoming';

  @override
  String get queueStatusCheckedIn => 'Checked In';

  @override
  String get queueStatusWaitingRoom => 'Please wait comfortably';

  @override
  String get queueStatusCalledIn => 'You\'re being called in!';

  @override
  String get queueStatusInProgress => 'Service in Progress';

  @override
  String get queueStatusCompleted => 'Completed';

  @override
  String get queueStatusSkipped => 'Skipped';

  @override
  String get queueStatusRequeued => 'Re-queued';

  @override
  String get queueStatusCanceled => 'Canceled';

  @override
  String get queueStatusUnknown => 'Unknown';

  @override
  String queueMessageUpcoming(int position) {
    return 'You are #$position in line. Please be ready.';
  }

  @override
  String get queueMessageCheckedIn => 'Let us know when you arrive!';

  @override
  String queueMessageWaitingRoom(int position) {
    return 'Your provider will call you soon. You\'re #$position in line.';
  }

  @override
  String get queueMessageCalledIn => 'Please proceed to the service area.';

  @override
  String get queueMessageInProgress => 'Your service is in progress.';

  @override
  String get queueMessageCompleted => 'Thank you for using our service!';

  @override
  String get queueMessageSkipped => 'You missed your turn.';

  @override
  String get queueMessageRequeued => 'You have been re-added to the queue.';

  @override
  String get queueMessageCanceled => 'Your booking was canceled.';

  @override
  String get bookingDetailProviderDetails => 'Provider Details:';

  @override
  String get bookingDetailAppointmentFor => 'Appointment For:';

  @override
  String get bookingDetailServicePrefix => 'Service: ';

  @override
  String get bookingDetailQueuePrefix => 'Queue: ';

  @override
  String get bookingDetailTimePrefix => 'Time: ';

  @override
  String get bookingDetailMapButton => 'Map';

  @override
  String get bookingDetailCallButton => 'Call';

  @override
  String get bookingDetailCancelButton => 'Cancel';

  @override
  String get bookingDetailCancelConfirmTitle => 'Cancel Appointment?';

  @override
  String get bookingDetailCancelConfirmMessage =>
      'Are you sure you want to cancel this appointment? This action cannot be undone.';

  @override
  String get bookingDetailCancelSuccess =>
      'Appointment cancelled successfully.';

  @override
  String get bookingDetailCancelError =>
      'Failed to cancel appointment. Please try again.';

  @override
  String bookingDetailLocationError(Object error) {
    return 'Error getting location: $error';
  }

  @override
  String get bookingDetailLocationPermissionDenied =>
      'Location permission denied';

  @override
  String get bookingDetailLocationServiceDisabled =>
      'Location services are disabled';

  @override
  String bookingDetailDirectionsError(Object error) {
    return 'Error getting directions: $error';
  }

  @override
  String bookingDetailDistance(Object distance) {
    return 'Distance: $distance';
  }

  @override
  String bookingDetailDuration(Object duration) {
    return 'Duration: $duration';
  }

  @override
  String get bookingDetailOpenInMaps => 'Open in Maps';

  @override
  String get bookingDetailGetDirections => 'Get Directions';

  @override
  String get bookingDetailFetchingLocation => 'Fetching location...';

  @override
  String get bookingDetailCalculatingRoutes => 'Calculating routes...';

  @override
  String get queueLiveStatusTitle => 'Live Queue Status';

  @override
  String get queueTimerPaused => 'Paused';

  @override
  String get queueLoadingService => 'Loading service...';

  @override
  String get queueLoadingDate => 'Loading date...';

  @override
  String get queueLoadingTime => 'Loading time...';

  @override
  String get queueFindingProfessional => 'Finding professional...';

  @override
  String get queueYouAreNext => 'You Are Next';

  @override
  String get queueYourTurn => 'Your Turn';

  @override
  String queueYourPosition(Object position) {
    return 'Your Position $position';
  }

  @override
  String get queueTimerPausedMessage =>
      'Timer Paused: Waiting for service to start.';

  @override
  String get queueViewQRCode => 'View QRCode';

  @override
  String get queueCheckInDetails => 'Check-In Details';

  @override
  String get queueDone => 'Done';

  @override
  String get queueEmptyOrNotListed =>
      'Queue is currently empty or your spot is not listed.';

  @override
  String get queueIncomingSwapRequests => 'Incoming Swap Requests';

  @override
  String queueSwapRequestsFor(Object time) {
    return 'for your spot at $time';
  }

  @override
  String queueSwapStatus(Object status) {
    return 'Status: $status';
  }

  @override
  String queueSwapRequested(Object dateTime) {
    return 'Requested: $dateTime';
  }

  @override
  String get queueReject => 'Reject';

  @override
  String get queueAccept => 'Accept';

  @override
  String queueSwapResponseSent(Object action) {
    return 'Swap response sent: $action';
  }

  @override
  String get queueSwapResponseAccepted => 'Accepted';

  @override
  String get queueSwapResponseRejected => 'Rejected';

  @override
  String get queueNotConnected => 'Not connected. Cannot respond to swap.';

  @override
  String queueMemberLabelYouInProgress(Object position) {
    return 'Your Turn (In Progress) - Pos $position';
  }

  @override
  String queueMemberLabelYouCompleted(Object position) {
    return 'You - Service Completed - Pos $position';
  }

  @override
  String queueMemberLabelYouStartingSoon(Object position) {
    return 'You - Starting Soon - Pos $position';
  }

  @override
  String queueMemberLabelYouPosition(Object position) {
    return 'You - Position $position';
  }

  @override
  String queueMemberLabelOtherStartingSoon(Object name, Object position) {
    return '$name - Starting Soon - Pos $position';
  }

  @override
  String queueMemberLabelOtherPosition(Object name, Object position) {
    return '$name - Pos $position';
  }

  @override
  String get queueMemberLabelSuffixInProgress => ' (In Progress)';

  @override
  String get queueMemberLabelSuffixCompleted => ' (Completed)';
}
