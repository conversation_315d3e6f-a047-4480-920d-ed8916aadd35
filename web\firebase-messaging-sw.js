// public/firebase-messaging-sw.js

// Scripts for firebase and firebase messaging
importScripts('https://www.gstatic.com/firebasejs/9.15.0/firebase-app-compat.js'); // Use a recent, compatible version
importScripts('https://www.gstatic.com/firebasejs/9.15.0/firebase-messaging-compat.js');

// Initialize the Firebase app in the service worker
// Be sure to replace the config values below with your app's Firebase config.
// IMPORTANT: This configuration MUST match the one in your main app.
const firebaseConfig = {
  apiKey: "AIzaSyDDcsbxchQIzUvlxyr_vddecMVPhxbd6Lg",
  authDomain: "dalti-prod.firebaseapp.com",
  projectId: "dalti-prod",
  storageBucket: "dalti-prod.firebasestorage.app",
  messagingSenderId: "1060372851323",
  appId: "1:1060372851323:web:customer_web_app_id", // You'll need to get the actual web app ID from Firebase Console
  // measurementId is not typically needed in the service worker
};

firebase.initializeApp(firebaseConfig);

// Retrieve an instance of Firebase Messaging so that it can handle background messages.
const messaging = firebase.messaging();

messaging.onBackgroundMessage((payload) => {
  console.log('[firebase-messaging-sw.js] Received background message ', payload);
  // Customize notification here
  const notificationTitle = payload.notification.title || 'New Notification';
  const notificationOptions = {
    body: payload.notification.body || 'You have a new message.',
    icon: payload.notification.icon || '/logo.png', // Path to an icon in your public folder
    data: payload.data // Any custom data you send with the notification
  };

  self.registration.showNotification(notificationTitle, notificationOptions);
});