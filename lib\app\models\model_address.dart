class ModelAddress {
  int? id;
  String? name;
  String? address;
  String? phone;
  String? street;
  String? city;
  String? postalCode;
  String? country;
  double? latitude;
  double? longitude;
  String? description;
  bool? isPrimary;

  ModelAddress({
    this.id,
    this.name,
    this.address,
    this.phone,
    this.street,
    this.city,
    this.postalCode,
    this.country,
    this.latitude,
    this.longitude,
    this.description,
    this.isPrimary,
  });

  factory ModelAddress.fromJson(Map<String, dynamic> json) {
    return ModelAddress(
      id: json['id'],
      name: json['name'],
      address: json['address'],
      phone: json['phone'],
      street: json['street'],
      city: json['city'],
      postalCode: json['postalCode'],
      country: json['country'],
      latitude: json['latitude']?.toDouble(),
      longitude: json['longitude']?.toDouble(),
      description: json['description'],
      isPrimary: json['isPrimary'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'address': address,
      'phone': phone,
      'street': street,
      'city': city,
      'postalCode': postalCode,
      'country': country,
      'latitude': latitude,
      'longitude': longitude,
      'description': description,
      'isPrimary': isPrimary,
    };
  }
}
