import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:dalti/app/models/model_category.dart';
import 'package:shared_preferences/shared_preferences.dart';

Future<List<ModelCategory>> fetchCategories() async {
  final prefs = await SharedPreferences.getInstance();
  final String? targetLanguage = prefs.getString('language_code');

  // Default to 'en' if no language code is found in SharedPreferences, or handle as needed
  final String languageQueryParam = targetLanguage?.toUpperCase() ?? 'EN';

  final String apiUrl =
      "https://dapi-test.adscloud.org:8443/api/provider-categories?targetLanguage=$languageQueryParam";
  try {
    final response = await http.get(Uri.parse(apiUrl));

    if (response.statusCode == 200) {
      final List<dynamic> responseData = jsonDecode(response.body);
      return responseData
          .map((data) => ModelCategory.fromJson(data as Map<String, dynamic>))
          .toList();
    } else {
      print(
        "Failed to fetch categories: ${response.statusCode} ${response.body}",
      );
      throw Exception(
        'Failed to load categories. Status code: ${response.statusCode}',
      );
    }
  } catch (e) {
    print("Error fetching categories: $e");
    throw Exception('Error fetching categories: $e');
  }
}
