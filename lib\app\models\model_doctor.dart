import 'package:dalti/app/models/booking_models.dart';
import 'package:flutter/material.dart';

class ModelDoctor {
  final int id;
  final String imageAsset;
  final String name;
  final String specialization;
  final String hospital;
  final int experience;
  final double fees;
  final double rating;
  final int reviewCount;
  final Color imageBackgroundColor;
  final String? nearestSlotDisplay;
  final int sProvidingPlaceId;
  final List<ModelService> services;
  final List<ModelQueue> queues;
  final String? phone;

  ModelDoctor({
    required this.id,
    required this.imageAsset,
    required this.name,
    required this.specialization,
    required this.hospital,
    required this.experience,
    required this.fees,
    required this.rating,
    required this.reviewCount,
    required this.imageBackgroundColor,
    this.nearestSlotDisplay,
    required this.sProvidingPlaceId,
    required this.services,
    required this.queues,
    this.phone,
  });
}
