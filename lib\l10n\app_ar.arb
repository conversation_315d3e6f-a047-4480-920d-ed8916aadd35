{"helloWorld": "مرحباً بالعالم!", "welcomeMessage": "أهلاً بك في تطبيقنا", "chooseLanguage": "اختر لغتك", "english": "الإنجليزية", "arabic": "العربية", "french": "الفرنسية", "nextButtonText": "التالي", "skipButtonText": "تخطى", "intro1Title": "عنوان الشريحة التعريفية 1 (AR)", "intro1Description": "وصف الشريحة التعريفية 1 (AR).", "intro2Title": "عنوان الشريحة التعريفية 2 (AR)", "intro2Description": "وصف الشريحة التعريفية 2 (AR).", "intro3Title": "عنوان الشريحة التعريفية 3 (AR)", "intro3Description": "وصف الشريحة التعريفية 3 (AR).", "preferredMethodTitle": "اختر طريقتك المفضلة", "preferredMethodSubtitle": "حدد كيف ترغب في التسجيل أو تسجيل الدخول.", "useEmailButton": "استخدام البريد الإلكتروني", "useMobileButton": "استخدام رقم الجوال", "goBackButton": "العودة", "loginWithEmailTitle": "تسجيل الدخول بالبريد الإلكتروني", "loginWithMobileTitle": "تسجيل الدخول برقم الجوال", "loginSubtitle": "سعداء بلقائك مرة أخرى!", "emailLabel": "الب<PERSON>يد الإلكتروني", "phoneNumberLabel": "رقم الجوال", "passwordLabel": "كلمة المرور", "forgotPasswordButton": "هل نسيت كلمة المرور؟", "loginButton": "تسجيل الدخول", "dontHaveAccountText": "ليس لديك حساب؟", "signUpButtonLoginScreen": " إنشاء حساب", "errorPhoneNumberEmpty": "لا يمكن ترك رقم الجوال فارغًا.", "errorEmailPasswordEmpty": "لا يمكن ترك البريد الإلكتروني وكلمة المرور فارغين.", "errorPhonePasswordEmpty": "لا يمكن ترك رقم الجوال وكلمة المرور فارغين.", "errorLoginInvalidResponse": "فشل تسجيل الدخول: استجابة الخادم غير صالحة. الحالة: {statusCode}", "loginSuccess": "تم تسجيل الدخول بنجاح!", "errorLoginNoSessionId": "فشل تسجيل الدخول: لم يتم استلام معرف الجلسة.", "errorLoginWithMessageStatus": "فشل تسجيل الدخول: {message} (الحالة: {statusCode})", "errorLoginGeneric": "حد<PERSON> خطأ أثناء تسجيل الدخول: {errorDetails}", "signUpTitle": "إنشاء حساب", "signUpSubtitleEmail": "قم بإنشاء حساب باستخدام بريدك الإلكتروني.", "signUpSubtitleMobile": "قم بإنشاء حساب باستخدام رقم جوالك.", "signUpSubtitleGeneric": "أدخل بياناتك لإنشاء الحساب!", "firstNameLabel": "الاسم الأول", "lastNameLabel": "الاسم الأخير", "agreeTermsPrivacyText": "أوا<PERSON><PERSON> على الشروط والخصوصية", "signUpButton": "إنشاء حساب", "alreadyHaveAccountText": "هل لديك حساب بالفعل؟", "loginButtonSignUpScreen": " تسجيل الدخول", "errorAgreeTerms": "يرجى الموافقة على الشروط والخصوصية.", "errorEmailEmptyPreferred": "لا يمكن ترك البريد الإلكتروني فارغًا لأنه طريقتك المفضلة.", "errorMobileEmptyPreferred": "لا يمكن ترك رقم الجوال فارغًا لأنه طريقتك المفضلة.", "errorProvideEmailOrPhone": "يرجى تقديم بريد إلكتروني أو رقم جوال.", "errorProvideSignUpDetails": "يرجى تقديم تفاصيل لإنشاء الحساب.", "errorUserExists": "{fieldInUse} مستخدم بالفعل.", "errorUserExistsConflict": "{fieldInUse} مستخدم بالفعل (تعارض).", "errorCheckingUser": "خطأ في التحقق من المستخدم: {statusCode} {serverMessage}", "errorFailedCheckUserExists": "فشل التحقق من وجود المستخدم: {errorDetails}", "errorNoValidOtpIdentifier": "لا يمكن المتابعة بدون بريد إلكتروني أو هاتف صالح لـ OTP.", "forgotPasswordTitle": "هل نسيت كلمة المرور؟", "forgotPasswordSubtitleEmail": "أدخل بريدك الإلكتروني لإعادة تعيين كلمة المرور الخاصة بك.", "forgotPasswordSubtitleMobile": "أدخل رقم هاتفك لإعادة تعيين كلمة المرور الخاصة بك.", "phoneNumberWithCodeLabel": "رقم الجوال (يتطلب رمز الدولة)", "submitButton": "إرسال", "resetPasswordTitle": "إعادة تعيين كلمة المرور", "resetPasswordSubtitle": "أدخل تفاصيل كلمة المرور الجديدة أدناه.", "oldPasswordLabel": "كلمة المرور القديمة", "newPasswordLabel": "كلمة المرور الجديدة", "confirmPasswordLabel": "تأكيد كلمة المرور", "setLocationDefault": "تحديد الموقع", "searchHintText": "بحث...", "homeBannerTitle": "خدمة طلاء الجدران", "homeBannerSubtitle": "اجعل جدارك أنيقًا", "bookNowButton": "<PERSON><PERSON><PERSON><PERSON> الآن", "categoriesTitle": "الفئات", "seeAllButton": "عر<PERSON> الكل", "errorLoadingCategories": "خطأ: {errorMessage}", "noCategoriesFound": "لم يتم العثور على فئات.", "noParentCategoriesFound": "لم يتم العثور على فئات رئيسية.", "profileLoading": "جار التحميل...", "profileUserFallback": "مستخدم", "profileErrorDisplayDetails": "خطأ في عرض التفاصيل", "profileGuestUser": "زائر", "profilePleaseLogIn": "الرجاء تسجيل الدخول", "profileTabTitle": "الملف الشخصي", "profileLogoutButton": "تسجيل الخروج", "profileSettingsButton": "الإعدادات", "profileMyAddressButton": "عنواني", "profileMyCardsButton": "بطاقاتي", "profileMyProfileButton": "ملفي الشخصي", "bookingsTabTitle": "الحجوزات", "bookingsTabAll": "الكل", "bookingsTabActive": "نشط", "bookingsTabCompleted": "مكتمل", "bookingsTabCancelled": "ملغى", "messagesTabTitle": "الرسائل", "messagesSearchHint": "ابحث في الرسائل...", "messagesEmptyTitle": "لا توجد رسائل", "messagesEmptySubtitle": "قائمة رسائلك فارغة حاليًا.", "doctorPrefix": "د. ", "unnamedProvider": "مزو<PERSON> خدمة غير مسمى", "defaultSpecialization": "عام", "naHospital": "مستشفى غير متوفر", "slotInfoUnavailable": "معلومات الفتحة غير متوفرة", "errorFailedToFetchProviders": "فشل جلب مزودي الخدمة (الحالة: {statusCode})", "errorFetchingData": "خطأ في جلب البيانات: {errorDetails}", "searchResultCountForQuery": "{count,plural, =0{لا توجد نتائج} =1{نتيجة واحدة} other{{count} نتائج}} لـ ''{query}''", "searchResultCountForQueryInCategory": "{count,plural, =0{لا توجد نتائج} =1{نتيجة واحدة} other{{count} نتائج}} لـ ''{query}'' في {category}", "searchResultCountForQueryInCity": "{count,plural, =0{لا توجد نتائج} =1{نتيجة واحدة} other{{count} نتائج}} لـ ''{query}'' في {city}", "searchResultCountForQueryInCategoryInCity": "{count,plural, =0{لا توجد نتائج} =1{نتيجة واحدة} other{{count} نتائج}} لـ ''{query}'' في {category} في {city}", "noProvidersFoundForQuery": "لم يتم العثور على مزودي خدمة لـ ''{query}''.", "noProvidersFoundForQueryWithCategory": "لم يتم العثور على مزودي خدمة لـ ''{query}''\nالفئة: {category}", "noProvidersFoundForQueryWithCity": "لم يتم العثور على مزودي خدمة لـ ''{query}''\nالمدينة: {city}", "noProvidersFoundForQueryWithCategoryAndCity": "لم يتم العثور على مزودي خدمة لـ ''{query}''\nالفئة: {category}\nالمدينة: {city}", "searchScreenTitle": "ب<PERSON><PERSON>", "searchByKeywordHint": "البحث بالكلمة المفتاحية", "hospitalPrefix": "في ", "experienceInYears": "خبرة {years} سنوات", "feesDisplay": "الرسوم {currencySymbol}{amount}", "reviewCountDisplay": "({count} تقييمات)", "nextAvailableSlotTitle": "الفتحة المتاحة التالية", "filtersModalTitle": "عوامل التصفية", "filtersCategorySectionTitle": "نوع الخدمة (الفئة)", "filtersSelectCategoryPlaceholder": "اختر الفئة", "filtersCitySectionTitle": "المدينة (الولاية)", "filtersEnterCityHint": "أد<PERSON>ل المدينة أو الولاية", "filtersClearButton": "مس<PERSON> عوا<PERSON>ل التصفية", "filtersApplyButton": "تطبيق عوامل التصفية", "filtersAllCategoriesTitle": "جميع الفئات", "filtersNoSubcategories": "لا توجد فئات فرعية.", "filtersNoCategoriesAvailable": "لا توجد فئات متاحة.", "errorFetchingCategories": "خطأ في جلب الفئات: {errorDetails}", "detailScreenCannotSelectPastDate": "لا يمكن اختيار تاريخ سابق.", "detailScreenBookAppointment": "<PERSON><PERSON><PERSON> موعد", "detailScreenServiceLabel": "الخدمة:", "detailScreenNoServices": "لا توجد خدمات متاحة.", "detailScreenQueueLabel": "القائمة:", "detailScreenGeneralAvailability": "التوفر العام لهذه الخدمة.", "detailScreenSelectDate": "اختر التاريخ", "detailScreenSelectTime": "اختر الوقت", "detailScreenPleaseSelectDate": "الرجاء اختيار تاريخ.", "detailScreenNoSlotsFound": "لا توجد مواعيد متاحة في {date}.", "detailScreenAllSlotsBooked": "جميع المواعيد محجوزة في {date}.", "detailScreenConfirmBooking": "ت<PERSON><PERSON>ي<PERSON> الحجز", "detailScreenCancel": "إلغاء", "detailScreenConfirm": "تأكيد", "detailScreenIncompleteDetails": "تفاصيل الحجز غير مكتملة.", "detailScreenPleaseLogin": "الرجاء تسجيل الدخول لإجراء الحجز.", "detailScreenBookingSuccess": "تم الحجز بنجاح!", "detailScreenBookingFailed": "فشل الحجز. الرجاء المحاولة مرة أخرى.", "detailScreenErrorOccurred": "حد<PERSON> خطأ: {error}", "detailScreenPointsRequired": "تتطلب هذه الخدمة {points} نقطة للحجز.", "detailScreenNoPhoneNumber": "رقم الهاتف غير متوفر.", "detailScreenCannotLaunchCall": "لا يمكن بدء المكالمة الهاتفية.", "detailScreenServiceDetail": "الخدمة:", "detailScreenQueueDetail": "القائمة:", "detailScreenDateDetail": "التاريخ:", "detailScreenTimeDetail": "الوقت:", "detailScreenBook": "حجز: {time} في {date}", "settingsSecurityTitle": "الأمان", "settingsHelpTitle": "المساعدة والدعم", "settingsPrivacyTitle": "سياسة الخصوصية", "settingsTermsTitle": "شروط الخدمة", "settingsSecurityContent": "أمانك هو أولويتنا القصوى. نحن نطبق إجراءات أمنية وفقاً لمعايير الصناعة لحماية بياناتك وضمان تجربة آمنة أثناء استخدام خدماتنا.", "settingsHelpContent": "هل تحتاج إلى مساعدة؟ فريق الدعم لدينا موجود لمساعدتك في أي أسئلة أو استفسارات قد تكون لديك حول خدماتنا.", "settingsPrivacyContent": "نحن نقدر خصوصيتك. توضح هذه السياسة كيفية جمع واستخدام وحماية معلوماتك الشخصية عند استخدام خدماتنا.", "settingsTermsContent": "باستخدام خدماتنا، فإنك توافق على هذه الشروط. يرجى قراءتها بعناية حيث أنها تحكم استخدامك لمنصتنا وخدماتنا.", "notificationsTitle": "الإشعارات", "notificationsToday": "اليوم", "notificationsYesterday": "الأمس", "notificationsRetry": "إعادة المحاولة", "notificationsEmpty": "لا توجد إشعارات حتى الآن!", "notificationsEmptyDesc": "سنخبرك عندما يصل شيء ما.", "queueStatusUpcoming": "قادم", "queueStatusCheckedIn": "تم تسجيل الوصول", "queueStatusWaitingRoom": "ير<PERSON>ى الانتظار بشكل مريح", "queueStatusCalledIn": "يتم استدعاؤك!", "queueStatusInProgress": "الخدمة قيد التنفيذ", "queueStatusCompleted": "مكتمل", "queueStatusSkipped": "تم التخطي", "queueStatusRequeued": "تمت إعادة الإضافة للقائمة", "queueStatusCanceled": "ملغى", "queueStatusUnknown": "غير معروف", "queueMessageUpcoming": "أنت رقم {position} في القائمة. يرجى الاستعداد.", "@queueMessageUpcoming": {"placeholders": {"position": {"type": "int"}}}, "queueMessageCheckedIn": "أخبرنا عند وصولك!", "queueMessageWaitingRoom": "سيتصل بك مقدم الخدمة قريباً. أنت رقم {position} في القائمة.", "@queueMessageWaitingRoom": {"placeholders": {"position": {"type": "int"}}}, "queueMessageCalledIn": "يرجى التوجه إلى منطقة الخدمة.", "queueMessageInProgress": "خدمتك قيد التنفيذ.", "queueMessageCompleted": "شكراً لاستخدام خدمتنا!", "queueMessageSkipped": "لقد فاتك دورك.", "queueMessageRequeued": "تمت إعادة إضافتك إلى القائمة.", "queueMessageCanceled": "تم إلغاء حجزك.", "bookingDetailProviderDetails": "تفاصيل مقدم الخدمة:", "bookingDetailAppointmentFor": "الموعد لـ:", "bookingDetailServicePrefix": "الخدمة: ", "bookingDetailQueuePrefix": "القائمة: ", "bookingDetailTimePrefix": "الوقت: ", "bookingDetailMapButton": "الخريطة", "bookingDetailCallButton": "اتصال", "bookingDetailCancelButton": "إلغاء", "bookingDetailCancelConfirmTitle": "إلغاء الموعد؟", "bookingDetailCancelConfirmMessage": "هل أنت متأكد من رغبتك في إلغاء هذا الموعد؟ لا يمكن التراجع عن هذا الإجراء.", "bookingDetailCancelSuccess": "تم إلغاء الموعد بنجاح.", "bookingDetailCancelError": "فشل إلغاء الموعد. يرجى المحاولة مرة أخرى.", "bookingDetailLocationError": "خطأ في الحصول على الموقع: {error}", "bookingDetailLocationPermissionDenied": "تم رفض إذن الموقع", "bookingDetailLocationServiceDisabled": "خدمات الموقع معطلة", "bookingDetailDirectionsError": "خطأ في الحصول على الاتجاهات: {error}", "bookingDetailDistance": "المسافة: {distance}", "bookingDetailDuration": "المدة: {duration}", "bookingDetailOpenInMaps": "فتح في الخرائط", "bookingDetailGetDirections": "الحصول على الاتجاهات", "bookingDetailFetchingLocation": "جارٍ جلب الموقع...", "bookingDetailCalculatingRoutes": "جارٍ حساب المسارات...", "queueLiveStatusTitle": "حالة الطابور المباشرة", "queueTimerPaused": "متوقف مؤقتًا", "queueLoadingService": "جارٍ تحميل الخدمة...", "queueLoadingDate": "جارٍ تحميل التاريخ...", "queueLoadingTime": "جارٍ تحميل الوقت...", "queueFindingProfessional": "جارٍ البحث عن متخصص...", "queueYouAreNext": "أنت التالي", "queueYourTurn": "دورك الآن", "queueYourPosition": "موقعك في الطابور {position}", "queueTimerPausedMessage": "المؤقت متوقف: في انتظار بدء الخدمة.", "queueViewQRCode": "عرض رمز الاستجابة السريعة", "queueCheckInDetails": "تفاصيل تسجيل الدخول", "queueDone": "تم", "queueEmptyOrNotListed": "الطابور فارغ حالياً أو لم يتم تسجيل دورك.", "queueIncomingSwapRequests": "طلبات التبديل الواردة", "queueSwapRequestsFor": "لمكانك في الساعة {time}", "queueSwapStatus": "الحالة: {status}", "queueSwapRequested": "تم الطلب في: {dateTime}", "queueReject": "<PERSON><PERSON><PERSON>", "queueAccept": "قبول", "queueSwapResponseSent": "تم إرسال رد التبديل: {action}", "queueSwapResponseAccepted": "تم القبول", "queueSwapResponseRejected": "تم الرفض", "queueNotConnected": "غير متصل. لا يمكن الرد على التبديل.", "queueMemberLabelYouInProgress": "دورك (قيد التنفيذ) - الموضع {position}", "@queueMemberLabelYouInProgress": {"placeholders": {"position": {}}}, "queueMemberLabelYouCompleted": "أنت - تم الانتهاء من الخدمة - الموضع {position}", "@queueMemberLabelYouCompleted": {"placeholders": {"position": {}}}, "queueMemberLabelYouStartingSoon": "أنت - سيبدأ قريباً - الموضع {position}", "@queueMemberLabelYouStartingSoon": {"placeholders": {"position": {}}}, "queueMemberLabelYouPosition": "أنت - الموضع {position}", "@queueMemberLabelYouPosition": {"placeholders": {"position": {}}}, "queueMemberLabelOtherStartingSoon": "{name} - سيبدأ قريباً - الموضع {position}", "@queueMemberLabelOtherStartingSoon": {"placeholders": {"name": {}, "position": {}}}, "queueMemberLabelOtherPosition": "{name} - الموضع {position}", "@queueMemberLabelOtherPosition": {"placeholders": {"name": {}, "position": {}}}, "queueMemberLabelSuffixInProgress": " (قيد التنفيذ)", "queueMemberLabelSuffixCompleted": " (تم الانتهاء)"}