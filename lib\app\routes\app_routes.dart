abstract class Routes {
  static const homeRoute = Paths.homepath;
  static const introRoute = Paths.introPath;
  static const loginRoute = Paths.loginPath;
  static const forgotRoute = Paths.forgotPath;
  static const resetRoute = Paths.resetPath;
  static const signupRoute = Paths.signUpPath;
  static const selectCountryRoute = Paths.selectCountryPath;
  static const verifyRoute = Paths.verifyPath;
  static const preferredMethodRoute = Paths.preferredMethodPath;
  static const homeScreenRoute = Paths.homescreenPath;
  static const categoryRoute = Paths.categoryPath;
  static const detailRoute = Paths.detailPath;
  static const cartRoute = Paths.cartPath;
  static const addressRoute = Paths.addressPath;
  static const dateTimeRoute = Paths.dateTimePath;
  static const paymentRoute = Paths.paymentPath;
  static const orderDetailRoute = Paths.orderDetailPath;
  static const profileRoute = Paths.profilePath;
  static const editProfileRoute = Paths.editProfilePath;
  static const myAddressRoute = Paths.myAddressPath;
  static const editAddressRoute = Paths.editAddressPath;
  static const cardRoute = Paths.cardPath;
  static const settingRoute = Paths.settingPath;
  static const notificationRoutes = Paths.notificationPath;
  static const searchRoute = Paths.searchPath;
  static const bookingRoute = Paths.bookingPath;
  static const helpRoute = Paths.helpPath;
  static const privacyRoute = Paths.privacyPath;
  static const securityRoute = Paths.securityPath;
  static const termOfServiceRoute = Paths.termOfServicePath;
  static const messageScreenRoute = Paths.messageScreenPath;
  static const passwordResetRequestRoute = Paths.passwordResetRequestPath;
  static const verifyResetOtpRoute = Paths.verifyResetOtpPath;
  static const newPasswordRoute = Paths.newPasswordPath;
  static const queueManagementRoute = Paths.queueManagementRoute;
}

abstract class Paths {
  static const homepath = "/";
  static const introPath = "/IntroScreen";
  static const loginPath = "/LoginScreen";
  static const forgotPath = "/ForgotPassword";
  static const resetPath = "/ResetPassword";
  static const signUpPath = "/SignUpScreen";
  static const selectCountryPath = "/SelectCountry";
  static const verifyPath = "/VerifyScreen";
  static const preferredMethodPath = "/PreferredMethodScreen";
  static const homescreenPath = "/HomeScreen";
  static const categoryPath = "/CategoryScreen";
  static const detailPath = "/DetailScreen";
  static const cartPath = "/CartScreen";
  static const addressPath = "/AddressScreen";
  static const dateTimePath = "/DateTimeScreen";
  static const paymentPath = "/PaymentScreen";
  static const orderDetailPath = "/OrderDetail";
  static const profilePath = "/ProfileScreen";
  static const editProfilePath = "/EditProfileScreen";
  static const myAddressPath = "/MyAddressScreen";
  static const editAddressPath = "/EditAddressScreen";
  static const cardPath = "/CardScreen";
  static const settingPath = "/SettingScreen";
  static const notificationPath = "/NotificationScreen";
  static const searchPath = "/SearchScreen";
  static const bookingPath = "/BookingDetail";
  static const helpPath = "/HelpScreen";
  static const privacyPath = "/PrivacyScreen";
  static const securityPath = "/SecurityScreen";
  static const termOfServicePath = "/TermOfServiceScreen";
  static const messageScreenPath = "/MessageScreen";
  static const passwordResetRequestPath = "/PasswordResetRequestScreen";
  static const verifyResetOtpPath = "/VerifyResetOtpScreen";
  static const newPasswordPath = "/NewPasswordScreen";
  static const queueManagementRoute = "/queueManagement";
}
