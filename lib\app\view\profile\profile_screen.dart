import 'package:dalti/app/routes/app_routes.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:flutter/material.dart';

import '../../../base/color_data.dart';
import '../../../base/constant.dart';
import '../../../base/widget_utils.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({Key? key}) : super(key: key);

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    return WillPopScope(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: daltiBackground,
        bottomNavigationBar: editProfileButton(context),
        body: SafeArea(
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: FetchPixels.getPixelWidth(20),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                getVerSpace(FetchPixels.getPixelHeight(20)),
                gettoolbarMenu(
                  context,
                  "back.svg",
                  () {
                    Constant.backToPrev(context);
                  },
                  istext: true,
                  title: "Profile",
                  weight: FontWeight.w800,
                  fontsize: 24,
                  textColor: daltiTextHeadline,
                ),
                getVerSpace(FetchPixels.getPixelHeight(30)),
                profilePicture(context),
                getVerSpace(FetchPixels.getPixelHeight(40)),
                getCustomFont(
                  "First Name",
                  16,
                  daltiTextMuted,
                  1,
                  fontWeight: FontWeight.w400,
                ),
                getVerSpace(FetchPixels.getPixelHeight(6)),
                getCustomFont(
                  "Alena",
                  16,
                  daltiTextBody,
                  1,
                  fontWeight: FontWeight.w400,
                ),
                getVerSpace(FetchPixels.getPixelHeight(20)),
                getDivider(daltiDividerLine, 0, 1),
                getVerSpace(FetchPixels.getPixelHeight(20)),
                getCustomFont(
                  "Last Name",
                  16,
                  daltiTextMuted,
                  1,
                  fontWeight: FontWeight.w400,
                ),
                getVerSpace(FetchPixels.getPixelHeight(6)),
                getCustomFont(
                  "Gomez",
                  16,
                  daltiTextBody,
                  1,
                  fontWeight: FontWeight.w400,
                ),
                getVerSpace(FetchPixels.getPixelHeight(20)),
                getDivider(daltiDividerLine, 0, 1),
                getVerSpace(FetchPixels.getPixelHeight(20)),
                getCustomFont(
                  "Email",
                  16,
                  daltiTextMuted,
                  1,
                  fontWeight: FontWeight.w400,
                ),
                getVerSpace(FetchPixels.getPixelHeight(6)),
                getCustomFont(
                  "<EMAIL>",
                  16,
                  daltiTextBody,
                  1,
                  fontWeight: FontWeight.w400,
                ),
                getVerSpace(FetchPixels.getPixelHeight(20)),
                getDivider(daltiDividerLine, 0, 1),
                getVerSpace(FetchPixels.getPixelHeight(20)),
                getCustomFont(
                  "Phone No",
                  16,
                  daltiTextMuted,
                  1,
                  fontWeight: FontWeight.w400,
                ),
                getVerSpace(FetchPixels.getPixelHeight(6)),
                getCustomFont(
                  "(*************",
                  16,
                  daltiTextBody,
                  1,
                  fontWeight: FontWeight.w400,
                ),
              ],
            ),
          ),
        ),
      ),
      onWillPop: () async {
        Constant.backToPrev(context);
        return false;
      },
    );
  }

  Align profilePicture(BuildContext context) {
    return Align(
      alignment: Alignment.topCenter,
      child: Container(
        height: FetchPixels.getPixelHeight(100),
        width: FetchPixels.getPixelHeight(100),
        decoration: BoxDecoration(
          image: getDecorationAssetImage(context, "profile_picture.png"),
        ),
      ),
    );
  }

  Container editProfileButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: daltiCard,
        boxShadow: [
          BoxShadow(
            color: daltiDividerLine.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: Offset(0, -2),
          ),
        ],
      ),
      padding: EdgeInsets.only(
        left: FetchPixels.getPixelWidth(20),
        right: FetchPixels.getPixelWidth(20),
        bottom: FetchPixels.getPixelHeight(30),
        top: FetchPixels.getPixelHeight(15),
      ),
      child: getButton(
        context,
        daltiPrimary,
        "Edit Profile",
        daltiTextOnPrimary,
        () {
          Constant.sendToNext(context, Routes.editProfileRoute);
        },
        18,
        weight: FontWeight.w600,
        buttonHeight: FetchPixels.getPixelHeight(60),
        borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(14)),
      ),
    );
  }
}
