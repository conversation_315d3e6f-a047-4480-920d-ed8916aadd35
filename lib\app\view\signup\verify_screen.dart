import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:dalti/app/view/dialog/verify_dialog.dart';
import 'package:dalti/base/color_data.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:flutter/material.dart';
import 'package:pinput/pinput.dart';

import '../../../base/constant.dart';
import '../../../base/widget_utils.dart';
import '../../routes/app_routes.dart'; // For navigation if needed on back

class VerifyScreen extends StatefulWidget {
  final String firstName;
  final String lastName;
  final String? email;
  final String phoneNumber;
  final String password;
  final String? preferredMethodForOtp; // New argument

  const VerifyScreen({
    Key? key,
    required this.firstName,
    required this.lastName,
    this.email,
    required this.phoneNumber,
    required this.password,
    this.preferredMethodForOtp, // Initialize
  }) : super(key: key);

  @override
  State<VerifyScreen> createState() => _VerifyScreenState();
}

class _VerifyScreenState extends State<VerifyScreen> {
  bool? _otpViaEmail; // Remains: true for email, false for mobile
  bool _selectionMade = false; // To track if OTP process has started
  String _enteredOtp = "";
  bool _isLoading = false;

  Timer? _timer;
  int _countdownTime = 60;
  bool _isResendEnabled = false;

  @override
  void initState() {
    super.initState();
    _initializeOtpPreference();
  }

  void _initializeOtpPreference() {
    // Prioritize preferredMethodForOtp from arguments
    if (widget.preferredMethodForOtp != null) {
      if (widget.preferredMethodForOtp == 'email' &&
          widget.email != null &&
          widget.email!.isNotEmpty) {
        _otpViaEmail = true;
        _selectionMade = true;
        _requestOtp(viaEmail: true);
      } else if (widget.preferredMethodForOtp == 'mobile' &&
          widget.phoneNumber.isNotEmpty) {
        _otpViaEmail = false;
        _selectionMade = true;
        _requestOtp(viaEmail: false);
      }
    }

    // Fallback logic if preferredMethodForOtp is null or not applicable (e.g., email not provided)
    if (!_selectionMade) {
      bool canUseEmail = widget.email != null && widget.email!.isNotEmpty;
      bool canUsePhone = widget.phoneNumber.isNotEmpty;

      if (canUseEmail && !canUsePhone) {
        // Only email available
        _otpViaEmail = true;
        _selectionMade = true;
        _requestOtp(viaEmail: true);
      } else if (!canUseEmail && canUsePhone) {
        // Only phone available
        _otpViaEmail = false;
        _selectionMade = true;
        _requestOtp(viaEmail: false);
      }
      // If both or neither are clearly preferred/available initially, user will see selection UI (if applicable)
      // or the UI will guide them based on _otpViaEmail's null state.
      // The build method will handle showing selection if _selectionMade is false and both options are valid.
    }
    if (_selectionMade && _otpViaEmail == null) {
      // This case means a preference was set, but the chosen method was invalid (e.g. preferred 'email' but no email provided)
      // Default to phone if available, otherwise keep selectionMade false to show choices or an error.
      if (widget.phoneNumber.isNotEmpty) {
        _otpViaEmail = false;
        _requestOtp(viaEmail: false);
      } else if (widget.email != null && widget.email!.isNotEmpty) {
        _otpViaEmail = true;
        _requestOtp(viaEmail: true);
      } else {
        _selectionMade = false; // Revert to let user choose or show error
      }
    }
  }

  @override
  void dispose() {
    _timer?.cancel();
    super.dispose();
  }

  void startResendTimer() {
    _timer?.cancel(); // Cancel any existing timer
    _countdownTime = 60;
    _isResendEnabled = false;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_countdownTime > 0) {
          _countdownTime--;
        } else {
          _timer?.cancel();
          _isResendEnabled = true;
        }
      });
    });
  }

  void finishView() {
    // Navigate back to SignUpScreen, passing the original preferred method.
    // This requires SignUpScreen to handle this argument if it comes back.
    // Or, more simply, navigate to login with the main preference from shared_prefs.
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    String? originalPreferredMethod =
        args?['preferred_method_for_otp']; // This was the method for OTP

    // We should ideally pop back to the screen that pushed VerifyScreen,
    // which is SignUpScreen. SignUpScreen's onWillPop already handles going to LoginScreen.
    Constant.backToPrev(context);
  }

  Future<void> _requestOtp({required bool viaEmail}) async {
    setState(() {
      _isLoading = true;
      _isResendEnabled = false;
    });

    String apiUrl;
    Map<String, dynamic> body;

    if (viaEmail) {
      if (widget.email == null || widget.email!.isEmpty) {
        showCustomSnackBar(
          context,
          "Email address is not available.",
          CustomSnackBarType.error,
        );
        setState(() {
          _isLoading = false;
          _selectionMade = false;
          _otpViaEmail = null;
        });
        return;
      }
      apiUrl = 'https://dapi-test.adscloud.org:8443/api/auth/request-email-otp';
      body = {
        'email': widget.email!,
        'firstName': widget.firstName,
        'lastName': widget.lastName,
        'password':
            widget
                .password, // Password might not be needed for OTP request, check API
      };
    } else {
      if (widget.phoneNumber.isEmpty) {
        showCustomSnackBar(
          context,
          "Phone number is not available.",
          CustomSnackBarType.error,
        );
        setState(() {
          _isLoading = false;
          _selectionMade = false;
          _otpViaEmail = null;
        });
        return;
      }
      apiUrl = 'https://dapi-test.adscloud.org:8443/api/auth/request-otp';
      body = {'phoneNumber': widget.phoneNumber};
    }

    try {
      final response = await http.post(
        Uri.parse(apiUrl),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(body),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        showCustomSnackBar(
          context,
          viaEmail
              ? "OTP sent to ${widget.email}"
              : "OTP sent to ${widget.phoneNumber}",
          CustomSnackBarType.success,
        );
        startResendTimer();
      } else if (viaEmail && response.statusCode == 429) {
        showCustomSnackBar(
          context,
          "Too many requests. Please wait before trying again.",
          CustomSnackBarType.warning,
        );
        // Re-enable resend immediately if it's a cooldown issue, as timer won't start
        setState(() {
          _isResendEnabled = true;
        });
      } else {
        String errorMessage = "Failed to send OTP.";
        try {
          final responseData = jsonDecode(response.body);
          if (responseData['message'] != null) {
            errorMessage = responseData['message'];
          } else {
            errorMessage = "Failed to send OTP: ${response.body}";
          }
        } catch (_) {
          errorMessage = "Failed to send OTP: ${response.statusCode}";
        }
        showCustomSnackBar(context, errorMessage, CustomSnackBarType.error);

        // If OTP request fails, allow user to retry or go back
        setState(() {
          // if user had a choice, go back to selection.
          if (widget.email != null &&
              widget.email!.isNotEmpty &&
              widget.phoneNumber.isNotEmpty) {
            // Check if both options were potentially available
            _selectionMade = false;
            _otpViaEmail = null;
          } else {
            // If only one option was available (e.g. only phone, or only email after selection),
            // keep them on the OTP entry screen but enable resend.
            _isResendEnabled = true;
          }
        });
      }
    } catch (e) {
      showCustomSnackBar(
        context,
        "Error sending OTP: $e",
        CustomSnackBarType.error,
      );
      setState(() {
        if (widget.email != null &&
            widget.email!.isNotEmpty &&
            widget.phoneNumber.isNotEmpty) {
          _selectionMade = false;
          _otpViaEmail = null;
        } else {
          _isResendEnabled =
              true; // Allow retry if there was no choice screen to go back to
        }
      });
    }

    setState(() {
      _isLoading = false;
    });
  }

  Future<void> _verifyOtpAndRegister() async {
    if (_enteredOtp.length != 6) {
      showCustomSnackBar(
        context,
        "Please enter a 6-digit OTP.",
        CustomSnackBarType.warning,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    String identifier;
    String? emailForAuthIdentity;

    // Determine identifier and emailForAuthIdentity based on _otpViaEmail
    // This part remains largely the same as OTP verification itself depends on the method chosen for OTP.
    if (_otpViaEmail == true) {
      // OTP was sent via Email
      identifier = widget.email!;
      emailForAuthIdentity = widget.email!;
    } else {
      // OTP was sent via Phone
      identifier = widget.phoneNumber;
      // If OTP was via phone, still include email in registration if provided
      if (widget.email != null && widget.email!.isNotEmpty) {
        emailForAuthIdentity = widget.email!;
      }
    }

    Map<String, dynamic> requestBody = {
      'otp': _enteredOtp,
      'identifier': identifier,
      'password': widget.password,
      'firstName': widget.firstName,
      'lastName': widget.lastName,
    };

    // Add the email to the request body only if it's available and relevant
    if (emailForAuthIdentity != null) {
      requestBody['email'] = emailForAuthIdentity;
    }

    try {
      final response = await http.post(
        Uri.parse(
          'https://dapi-test.adscloud.org:8443/api/auth/verify-otp-register',
        ),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode(requestBody),
      );

      Map<String, dynamic>? responseData;
      String errorMessage = "Verification Failed. Please try again.";
      try {
        responseData = jsonDecode(response.body);
        if (responseData != null && responseData['message'] is String) {
          errorMessage = responseData['message'];
        }
      } catch (e) {
        // If parsing fails, use status code for generic error
        errorMessage = "Verification Failed (Status: ${response.statusCode}).";
      }

      if (response.statusCode == 200 || response.statusCode == 201) {
        showDialog(
          barrierDismissible: false,
          builder: (context) {
            return const VerifyDialog();
          },
          context: context,
        );
      } else {
        // Use specific error messages based on status code or server message
        showCustomSnackBar(
          context,
          errorMessage, // Use parsed or default error message
          CustomSnackBarType.error,
        );
      }
    } catch (e) {
      showCustomSnackBar(
        context,
        "Error verifying OTP: $e",
        CustomSnackBarType.error,
      );
    }

    setState(() {
      _isLoading = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    final args =
        ModalRoute.of(context)?.settings.arguments as Map<String, dynamic>?;
    // If initState didn't set _otpViaEmail from args (e.g. context not ready), try again.
    // This is a bit redundant if initState works reliably with context for ModalRoute.
    if (!_selectionMade && args?['preferred_method_for_otp'] != null) {
      String preferred = args!['preferred_method_for_otp'];
      if (preferred == 'email' &&
          widget.email != null &&
          widget.email!.isNotEmpty) {
        if (_otpViaEmail != true) {
          // Check if already set to avoid loop
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              _otpViaEmail = true;
              _selectionMade = true;
            });
            _requestOtp(viaEmail: true);
          });
        }
      } else if (preferred == 'mobile' && widget.phoneNumber.isNotEmpty) {
        if (_otpViaEmail != false) {
          // Check if already set
          WidgetsBinding.instance.addPostFrameCallback((_) {
            setState(() {
              _otpViaEmail = false;
              _selectionMade = true;
            });
            _requestOtp(viaEmail: false);
          });
        }
      }
    }

    final defaultPinTheme = PinTheme(
      width: FetchPixels.getPixelWidth(60), // Adjusted for 6 pins
      height: FetchPixels.getPixelHeight(68),
      textStyle: TextStyle(
        fontSize: FetchPixels.getPixelHeight(24),
        color: daltiPrimary,
        fontWeight: FontWeight.w800,
      ),
      decoration: BoxDecoration(
        color: daltiCard,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0.0, 4.0),
          ),
        ],
        borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(15)),
      ),
    );

    FetchPixels(context);
    return WillPopScope(
      child: Scaffold(
        backgroundColor: daltiBackground,
        body: SafeArea(
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: FetchPixels.getPixelWidth(20),
            ),
            child:
                _isLoading
                    ? Center(
                      child: CircularProgressIndicator(color: daltiPrimary),
                    )
                    : Column(
                      children: [
                        getVerSpace(FetchPixels.getPixelHeight(26)),
                        gettoolbarMenu(context, "back.svg", () {
                          // If user is on OTP entry (_selectionMade == true) and had a choice (both email/phone available AND no initial preference forced one)
                          // then go back to OTP method selection within this screen.
                          bool canUseEmail =
                              widget.email != null && widget.email!.isNotEmpty;
                          bool canUsePhone = widget.phoneNumber.isNotEmpty;
                          bool hadChoiceScreenPotential =
                              canUseEmail &&
                              canUsePhone &&
                              widget.preferredMethodForOtp == null;

                          if (_selectionMade && hadChoiceScreenPotential) {
                            setState(() {
                              _selectionMade = false;
                              _otpViaEmail = null;
                              _timer?.cancel();
                              _isResendEnabled = false;
                              _countdownTime = 60;
                              _enteredOtp = "";
                            });
                          } else {
                            finishView(); // Otherwise, use the standard back navigation.
                          }
                        }),
                        getVerSpace(FetchPixels.getPixelHeight(22)),
                        getCustomFont(
                          "Verify Account",
                          24,
                          daltiTextHeadline,
                          1,
                          fontWeight: FontWeight.w800,
                        ),
                        getVerSpace(FetchPixels.getPixelHeight(10)),
                        if (!_selectionMade &&
                            (widget.email != null &&
                                widget.email!.isNotEmpty)) ...[
                          getCustomFont(
                            "Choose where to send the OTP:",
                            16,
                            daltiTextBody,
                            1,
                            fontWeight: FontWeight.w400,
                          ),
                          getVerSpace(FetchPixels.getPixelHeight(20)),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              _buildSelectionCard(
                                context: context,
                                icon: Icons.phone_android,
                                label: "Mobile",
                                isSelected: _otpViaEmail == false,
                                onTap: () {
                                  setState(() {
                                    _otpViaEmail = false; // Mobile
                                  });
                                },
                              ),
                              _buildSelectionCard(
                                context: context,
                                icon: Icons.email_outlined,
                                label: "Email",
                                isSelected: _otpViaEmail == true,
                                onTap: () {
                                  setState(() {
                                    _otpViaEmail = true; // Email
                                  });
                                },
                              ),
                            ],
                          ),
                          getVerSpace(FetchPixels.getPixelHeight(30)),
                          getButton(
                            context,
                            daltiPrimary,
                            "Continue",
                            daltiTextOnPrimary,
                            () {
                              if (_otpViaEmail != null) {
                                setState(() {
                                  _selectionMade = true;
                                });
                                _requestOtp(viaEmail: _otpViaEmail!);
                              } else {
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text(
                                      "Please select an option to send OTP.",
                                    ),
                                  ),
                                );
                              }
                            },
                            18,
                            weight: FontWeight.w600,
                            buttonHeight: FetchPixels.getPixelHeight(60),
                            borderRadius: BorderRadius.circular(
                              FetchPixels.getPixelHeight(15),
                            ),
                          ),
                        ] else ...[
                          // OTP form
                          getCustomFont(
                            _otpViaEmail ==
                                    true // Check if true, not null
                                ? "Enter 6-digit code sent to your email: ${widget.email ?? ''}!"
                                : "Enter 6-digit code sent to ${widget.phoneNumber}!",
                            16,
                            daltiTextBody,
                            1,
                            fontWeight: FontWeight.w400,
                          ),
                          getVerSpace(FetchPixels.getPixelHeight(42)),
                          Pinput(
                            length: 6, // OTP length set to 6
                            defaultPinTheme: defaultPinTheme,
                            pinputAutovalidateMode:
                                PinputAutovalidateMode.onSubmit,
                            showCursor: true,
                            onChanged: (pin) {
                              _enteredOtp = pin;
                            },
                            onCompleted: (pin) {
                              _enteredOtp = pin;
                              _verifyOtpAndRegister(); // Optionally auto-submit on completion
                            },
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          ),
                          getVerSpace(FetchPixels.getPixelHeight(50)),
                          getButton(
                            context,
                            daltiPrimary,
                            "Verify",
                            daltiTextOnPrimary,
                            _verifyOtpAndRegister, // Call the verification function
                            18,
                            weight: FontWeight.w600,
                            buttonHeight: FetchPixels.getPixelHeight(60),
                            borderRadius: BorderRadius.circular(
                              FetchPixels.getPixelHeight(15),
                            ),
                          ),
                          getVerSpace(FetchPixels.getPixelHeight(30)),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              getCustomFont(
                                "Didn't recieve code?",
                                14,
                                daltiTextBody,
                                1,
                                fontWeight: FontWeight.w400,
                              ),
                              InkWell(
                                onTap: () {
                                  if (_otpViaEmail != null) {
                                    _requestOtp(viaEmail: _otpViaEmail!);
                                  } else {
                                    // This case should ideally not happen if selection logic is correct
                                    // For example, if only phone was provided, _otpViaEmail would be false.
                                    // If both were provided, user must select one.
                                    // If only email was provided (hypothetical), user must select email.
                                    // However, as a fallback:
                                    if (!widget.phoneNumber.isEmpty &&
                                        (widget.email == null ||
                                            widget.email!.isEmpty)) {
                                      _requestOtp(
                                        viaEmail: false,
                                      ); // Default to phone if only phone available
                                    } else if (!(widget.email == null ||
                                            widget.email!.isEmpty) &&
                                        widget.phoneNumber.isEmpty) {
                                      _requestOtp(
                                        viaEmail: true,
                                      ); // Default to email if only email available
                                    } else {
                                      showCustomSnackBar(
                                        context,
                                        "Please select an OTP method first.",
                                        CustomSnackBarType.warning,
                                      );
                                    }
                                  }
                                },
                                child: getCustomFont(
                                  " Resend",
                                  16,
                                  daltiPrimary,
                                  1,
                                  fontWeight: FontWeight.w800,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ],
                    ),
          ),
        ),
      ),
      onWillPop: () async {
        // Logic similar to the toolbar's back button
        bool canUseEmail = widget.email != null && widget.email!.isNotEmpty;
        bool canUsePhone = widget.phoneNumber.isNotEmpty;
        bool hadChoiceScreenPotential =
            canUseEmail && canUsePhone && widget.preferredMethodForOtp == null;

        if (_selectionMade && hadChoiceScreenPotential) {
          _timer?.cancel();
          setState(() {
            _selectionMade = false;
            _otpViaEmail = null;
            _isLoading = false;
            _enteredOtp = "";
            _isResendEnabled = false;
            _countdownTime = 60;
          });
          return false; // Don't pop, stay on screen to show selection
        }
        finishView(); // Standard back which should lead to SignUp screen
        return true; // Allow pop if not handled above
      },
    );
  }
}

Widget _buildSelectionCard({
  required BuildContext context,
  required IconData icon,
  required String label,
  required bool isSelected,
  required VoidCallback onTap,
}) {
  return Expanded(
    child: InkWell(
      onTap: onTap,
      child: Card(
        color: daltiCard,
        elevation: isSelected ? 8.0 : 2.0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
          side: BorderSide(
            color: isSelected ? daltiPrimary : daltiDividerLine,
            width: isSelected ? 2.0 : 1.0,
          ),
        ),
        child: Container(
          padding: EdgeInsets.symmetric(
            vertical: FetchPixels.getPixelHeight(20),
            horizontal: FetchPixels.getPixelWidth(10),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                icon,
                size: FetchPixels.getPixelHeight(40),
                color: isSelected ? daltiPrimary : daltiIconDefault,
              ),
              getVerSpace(FetchPixels.getPixelHeight(10)),
              getCustomFont(
                label,
                16,
                isSelected ? daltiPrimary : daltiTextBody,
                1,
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
              ),
            ],
          ),
        ),
      ),
    ),
  );
}
