import 'dart:async';

import 'package:dalti/base/color_data.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:flutter/material.dart';

import '../../base/constant.dart';
import '../../base/pref_data.dart';
import '../routes/app_routes.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({Key? key}) : super(key: key);

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    PrefData.isLogIn().then((value) {
      Timer(const Duration(seconds: 3), () {
        if (mounted) {
          (value)
              ? Constant.sendToNext(context, Routes.homeScreenRoute)
              : Constant.sendToNext(context, Routes.introRoute);
        }
      });
    });
  }

  void backClick() {
    Constant.backToPrev(context);
  }

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    return WillPopScope(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Color(0xFF0D4A52), // darkest teal
                Color(0xFF145A63), // darker teal
                daltiPrimary, // #19727F - main teal
                Color(0xFF1E8A99), // slightly lighter than primary
              ],
              stops: [0.0, 0.3, 0.7, 1.0],
            ),
          ),
          child: Center(
            child: Container(
              child: Image.asset(
                "assets/images/logo-dark.png",
                width: FetchPixels.getPixelHeight(180),
                height: FetchPixels.getPixelHeight(180),
                fit: BoxFit.contain,
              ),
            ),
          ),
        ),
      ),
      onWillPop: () async {
        backClick();
        return false;
      },
    );
  }
}
