import 'package:dalti/services/notification_service.dart';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'package:dalti/l10n/app_localizations.dart';

import '../../base/color_data.dart';
import '../../base/constant.dart';
import '../../base/resizer/fetch_pixels.dart';
import '../../base/widget_utils.dart';

class NotificationScreen extends StatefulWidget {
  const NotificationScreen({Key? key}) : super(key: key);

  @override
  State<NotificationScreen> createState() => _NotificationScreenState();
}

class _NotificationScreenState extends State<NotificationScreen> {
  final NotificationApiService _apiService = NotificationApiService();
  List<NotificationModel> _notifications = [];
  bool _isLoading = true;
  String? _errorMessage;

  final DateFormat _timeFormat = DateFormat('hh:mm a');
  final DateFormat _headerDateFormat = DateFormat('dd MMMM, yyyy');

  @override
  void initState() {
    super.initState();
    _fetchNotifications();
  }

  Future<void> _fetchNotifications({bool unreadOnly = false}) async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });
    try {
      final notifications = await _apiService.getUserNotifications(
        unreadOnly: unreadOnly,
      );
      if (!mounted) return;
      setState(() {
        _notifications = notifications;
        _isLoading = false;
      });
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _errorMessage = e.toString();
        if (e.toString().contains("User not authenticated")) {
          // Consider calling your clearAuthDataAndNavigateToLogin(context) here
          // For now, just showing the error.
        }
      });
    }
  }

  String _getDateCategory(DateTime date) {
    final localizations = AppLocalizations.of(context)!;
    final now = DateTime.now();
    final today = DateTime(now.year, now.month, now.day);
    final yesterday = DateTime(now.year, now.month, now.day - 1);
    final checkDate = DateTime(date.year, date.month, date.day);

    if (checkDate == today) {
      return localizations.notificationsToday;
    } else if (checkDate == yesterday) {
      return localizations.notificationsYesterday;
    } else {
      return _headerDateFormat.format(date);
    }
  }

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    final localizations = AppLocalizations.of(context)!;
    return WillPopScope(
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: backGroundColor,
        body: SafeArea(
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: FetchPixels.getPixelWidth(20),
            ),
            child: Column(
              children: [
                getVerSpace(FetchPixels.getPixelHeight(20)),
                gettoolbarMenu(
                  context,
                  "back.svg",
                  () {
                    Constant.backToPrev(context);
                  },
                  istext: true,
                  title: localizations.notificationsTitle,
                  weight: FontWeight.w800,
                  fontsize: 24,
                  textColor: Colors.black,
                ),
                getVerSpace(FetchPixels.getPixelHeight(10)),
                Expanded(child: _buildBody()),
              ],
            ),
          ),
        ),
      ),
      onWillPop: () async {
        Constant.backToPrev(context);
        return false;
      },
    );
  }

  Widget _buildBody() {
    final localizations = AppLocalizations.of(context)!;
    if (_isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: Theme.of(context).colorScheme.secondary,
        ),
      );
    }
    if (_errorMessage != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                _errorMessage!,
                textAlign: TextAlign.center,
                style: TextStyle(color: Colors.red, fontSize: 16),
              ),
              getVerSpace(FetchPixels.getPixelHeight(20)),
              getButton(
                context,
                Theme.of(context).colorScheme.secondary,
                localizations.notificationsRetry,
                Colors.white,
                () {
                  _fetchNotifications();
                },
                18,
                weight: FontWeight.bold,
                buttonHeight: FetchPixels.getPixelHeight(60),
                borderRadius: BorderRadius.circular(
                  FetchPixels.getPixelHeight(12),
                ),
              ),
            ],
          ),
        ),
      );
    }
    if (_notifications.isEmpty) {
      return nullListView(context);
    }
    return notificationList();
  }

  Expanded nullListView(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Expanded(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            height: FetchPixels.getPixelHeight(124),
            width: FetchPixels.getPixelHeight(124),
            decoration: BoxDecoration(
              image: getDecorationAssetImage(context, "bell.png"),
            ),
          ),
          getVerSpace(FetchPixels.getPixelHeight(40)),
          getCustomFont(
            localizations.notificationsEmpty,
            20,
            Colors.black,
            1,
            fontWeight: FontWeight.w800,
          ),
          getVerSpace(FetchPixels.getPixelHeight(10)),
          getCustomFont(
            localizations.notificationsEmptyDesc,
            16,
            Colors.black,
            1,
            fontWeight: FontWeight.w400,
          ),
        ],
      ),
    );
  }

  Widget notificationList() {
    Map<String, List<NotificationModel>> groupedNotifications = {};
    for (var notification in _notifications) {
      String dateCategory = _getDateCategory(notification.createdAt);
      if (groupedNotifications[dateCategory] == null) {
        groupedNotifications[dateCategory] = [];
      }
      groupedNotifications[dateCategory]!.add(notification);
    }

    List<Widget> listItems = [];
    groupedNotifications.forEach((dateCategory, notificationsInGroup) {
      listItems.add(
        Padding(
          padding: EdgeInsets.only(
            top: FetchPixels.getPixelHeight(20),
            bottom: FetchPixels.getPixelHeight(10),
          ),
          child: getCustomFont(
            dateCategory,
            16,
            Colors.black,
            1,
            fontWeight: FontWeight.w400,
          ),
        ),
      );
      for (var notification in notificationsInGroup) {
        listItems.add(notificationItem(notification));
      }
    });

    return ListView(
      padding: EdgeInsets.zero,
      physics: const BouncingScrollPhysics(),
      shrinkWrap: true,
      primary: false,
      children: listItems,
    );
  }

  Widget notificationItem(NotificationModel notification) {
    return InkWell(
      onTap: () {
        if (!notification.isRead) {
          _markNotificationAsRead(notification.id);
        }
        if (notification.link != null && notification.link!.isNotEmpty) {
          print("Notification link tapped: ${notification.link}");
        }
      },
      child: Container(
        margin: EdgeInsets.only(bottom: FetchPixels.getPixelHeight(20)),
        padding: EdgeInsets.only(
          top: FetchPixels.getPixelHeight(20),
          bottom: FetchPixels.getPixelHeight(20),
          right: FetchPixels.getPixelWidth(20),
          left: FetchPixels.getPixelWidth(20),
        ),
        decoration: BoxDecoration(
          color:
              notification.isRead
                  ? Colors.white.withOpacity(0.7)
                  : Colors.white,
          borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
          boxShadow: const [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 10,
              offset: Offset(0.0, 4.0),
            ),
          ],
        ),
        child: Row(
          children: [
            Container(
              height: FetchPixels.getPixelHeight(50),
              width: FetchPixels.getPixelHeight(50),
              decoration: BoxDecoration(
                color: const Color(0xFFE4ECFF),
                borderRadius: BorderRadius.circular(
                  FetchPixels.getPixelHeight(12),
                ),
              ),
              child: getSvgImage(
                notification.isRead ? "eye_off.svg" : "clock.svg",
              ),
              padding: EdgeInsets.all(FetchPixels.getPixelHeight(13)),
            ),
            getHorSpace(FetchPixels.getPixelWidth(14)),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: getCustomFont(
                          notification.title,
                          16,
                          Colors.black,
                          1,
                          fontWeight:
                              notification.isRead
                                  ? FontWeight.w400
                                  : FontWeight.w800,
                        ),
                      ),
                      getCustomFont(
                        _timeFormat.format(notification.createdAt),
                        14,
                        textColor,
                        1,
                        fontWeight: FontWeight.w400,
                      ),
                    ],
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(7)),
                  getCustomFont(
                    notification.message,
                    15,
                    Colors.black.withOpacity(notification.isRead ? 0.6 : 1.0),
                    3,
                    fontWeight: FontWeight.w400,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _markNotificationAsRead(String notificationId) async {
    try {
      await _apiService.markNotificationAsRead(notificationId);
      _fetchNotifications();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to mark as read: ${e.toString()}')),
        );
      }
    }
  }
}
