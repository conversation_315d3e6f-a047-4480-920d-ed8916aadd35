import 'package:flutter/material.dart';

class ProviderProfileScreen extends StatelessWidget {
  const ProviderProfileScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          icon: const Icon(Icons.arrow_back),
          onPressed: () {
            // TODO: Implement navigation
          },
        ),
        title: const Text('Dr. <PERSON>'),
        backgroundColor: Colors.transparent,
        elevation: 0,
        foregroundColor: Colors.black,
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              // Profile Header
              Center(
                child: Column(
                  children: <Widget>[
                    const CircleAvatar(
                      radius: 50,
                      // backgroundImage: AssetImage('assets/doctor_avatar.png'), // Replace with actual image
                      backgroundColor: Colors.grey, // Placeholder
                    ),
                    const SizedBox(height: 16),
                    const Text(
                      'Dr. <PERSON>',
                      style: TextStyle(
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const Text(
                      'Dermatologist',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                    const Text(
                      '12 years experience',
                      style: TextStyle(fontSize: 16, color: Colors.grey),
                    ),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: <Widget>[
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              // TODO: Implement booking
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.grey[200],
                              foregroundColor: Colors.black,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: const Text('Book'),
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: ElevatedButton(
                            onPressed: () {
                              // TODO: Implement join queue
                            },
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.teal[400],
                              foregroundColor: Colors.white,
                              padding: const EdgeInsets.symmetric(vertical: 16),
                            ),
                            child: const Text('Join Queue'),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Tab Bar
              DefaultTabController(
                length: 3,
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    const TabBar(
                      labelColor: Colors.teal,
                      unselectedLabelColor: Colors.grey,
                      indicatorColor: Colors.teal,
                      tabs: [
                        Tab(text: 'Details'),
                        Tab(text: 'Services'),
                        Tab(text: 'Staff'),
                      ],
                    ),
                    SizedBox(
                      height: 200, // Adjust height as needed
                      child: TabBarView(
                        children: [
                          // Details Tab Content
                          _buildDetailsTab(),
                          // Services Tab Content
                          const Center(child: Text('Services Content')),
                          // Staff Tab Content
                          const Center(child: Text('Staff Content')),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  static Widget _buildDetailsTab() {
    return ListView(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      children: <Widget>[
        const Text(
          'About',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        const Text(
          'Dr. Sophia Bennett is a board-certified dermatologist with over 12 years of experience in treating a wide range of skin conditions. She is known for her compassionate approach and commitment to providing personalized care to each patient.',
          style: TextStyle(fontSize: 16),
        ),
        const SizedBox(height: 24),
        const Text(
          'Specialties',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8.0,
          runSpacing: 4.0,
          children: <Widget>[
            Chip(
              label: const Text('Acne Treatment'),
              backgroundColor: Colors.grey[200],
            ),
            Chip(
              label: const Text('Skin Cancer Screening'),
              backgroundColor: Colors.grey[200],
            ),
            Chip(
              label: const Text('Cosmetic Dermatology'),
              backgroundColor: Colors.grey[200],
            ),
          ],
        ),
        const SizedBox(height: 24),
        _buildReviewsSection(),
      ],
    );
  }

  static Widget _buildReviewsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        const Text(
          'Reviews',
          style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
        ),
        const SizedBox(height: 16),
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: <Widget>[
            const Text(
              '4.8',
              style: TextStyle(fontSize: 48, fontWeight: FontWeight.bold),
            ),
            const SizedBox(width: 8),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Row(
                  children: List.generate(5, (index) {
                    return Icon(
                      index < 4
                          ? Icons.star
                          : Icons.star_half, // Example: 4.5 stars
                      color: Colors.amber,
                      size: 20,
                    );
                  })..add(
                    const Icon(
                      Icons.star_border,
                      color: Colors.amber,
                      size: 20,
                    ),
                  ), // for the last empty star if rating is < 5
                ),
                const Text(
                  '125 reviews',
                  style: TextStyle(fontSize: 14, color: Colors.grey),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 16),
        // Rating bars
        _buildRatingBar('5', 0.70, Colors.teal),
        _buildRatingBar('4', 0.20, Colors.teal),
        _buildRatingBar('3', 0.05, Colors.blueGrey[300]!),
        _buildRatingBar('2', 0.03, Colors.blueGrey[200]!),
        _buildRatingBar('1', 0.02, Colors.blueGrey[100]!),
        const SizedBox(height: 24),
        // Individual Reviews
        _buildIndividualReview(
          avatarInitial: 'EH',
          name: 'Emma Hayes',
          timeAgo: '2 months ago',
          rating: 5,
          reviewText:
              'Dr. Bennett is amazing! She took the time to listen to my concerns and provided excellent care. I highly recommend her.',
          likes: 12,
          dislikes: 2,
        ),
        const Divider(height: 32),
        _buildIndividualReview(
          avatarInitial: 'LC',
          name: 'Liam Carter',
          timeAgo: '3 months ago',
          rating: 4,
          reviewText:
              'Dr. Bennett is knowledgeable and professional. I had a great experience during my appointment.',
          likes: 8,
          dislikes: 1,
        ),
      ],
    );
  }

  static Widget _buildRatingBar(
    String starRating,
    double percent,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        children: <Widget>[
          Text(starRating, style: const TextStyle(fontSize: 14)),
          const SizedBox(width: 8),
          Expanded(
            child: LinearProgressIndicator(
              value: percent,
              backgroundColor: Colors.grey[300],
              valueColor: AlwaysStoppedAnimation<Color>(color),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '${(percent * 100).toInt()}%',
            style: const TextStyle(fontSize: 14),
          ),
        ],
      ),
    );
  }

  static Widget _buildIndividualReview({
    required String avatarInitial,
    required String name,
    required String timeAgo,
    required int rating,
    required String reviewText,
    required int likes,
    required int dislikes,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Row(
          children: <Widget>[
            CircleAvatar(
              child: Text(avatarInitial),
              backgroundColor: Colors.grey[300],
            ),
            const SizedBox(width: 12),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  name,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                Text(
                  timeAgo,
                  style: const TextStyle(color: Colors.grey, fontSize: 12),
                ),
              ],
            ),
          ],
        ),
        const SizedBox(height: 8),
        Row(
          children: List.generate(5, (index) {
            return Icon(
              index < rating ? Icons.star : Icons.star_border,
              color: Colors.amber,
              size: 18,
            );
          }),
        ),
        const SizedBox(height: 8),
        Text(reviewText, style: const TextStyle(fontSize: 16)),
        const SizedBox(height: 8),
        Row(
          children: <Widget>[
            const Icon(
              Icons.thumb_up_alt_outlined,
              size: 18,
              color: Colors.grey,
            ),
            const SizedBox(width: 4),
            Text('$likes', style: const TextStyle(color: Colors.grey)),
            const SizedBox(width: 16),
            const Icon(
              Icons.thumb_down_alt_outlined,
              size: 18,
              color: Colors.grey,
            ),
            const SizedBox(width: 4),
            Text('$dislikes', style: const TextStyle(color: Colors.grey)),
          ],
        ),
      ],
    );
  }
}

// You might want to create separate widget files for better organization,
// for example, for _buildDetailsTab, _buildReviewsSection, etc.
// Also, the image for the CircleAvatar needs to be added to your assets.
// And navigation for the back button needs to be implemented.
