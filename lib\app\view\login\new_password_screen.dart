import 'dart:async';
import 'package:dalti/app/routes/app_routes.dart';
import 'package:dalti/base/color_data.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:dalti/services/password_reset_service.dart';
import 'package:dalti/utils/password_validation.dart';
import 'package:flutter/material.dart';
import 'package:dalti/l10n/app_localizations.dart';

import '../../../base/constant.dart';

class NewPasswordScreen extends StatefulWidget {
  final String resetToken;
  final String email;

  const NewPasswordScreen({
    Key? key,
    required this.resetToken,
    required this.email,
  }) : super(key: key);

  @override
  State<NewPasswordScreen> createState() => _NewPasswordScreenState();
}

class _NewPasswordScreenState extends State<NewPasswordScreen> {
  final TextEditingController _passwordController = TextEditingController();
  final TextEditingController _confirmPasswordController =
      TextEditingController();
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  bool _isLoading = false;
  bool _isPasswordVisible = false;
  bool _isConfirmPasswordVisible = false;
  PasswordValidationResult _passwordValidation = PasswordValidationResult(
    isValid: false,
    strength: PasswordStrength.weak,
    failedRequirements: [],
    passedRequirements: [],
  );
  bool _passwordsMatch = false;
  Timer? _tokenExpiryTimer;
  int _tokenExpiryCountdown = 1800; // 30 minutes

  @override
  void initState() {
    super.initState();
    _startTokenExpiryTimer();
    _passwordController.addListener(_validatePassword);
    _confirmPasswordController.addListener(_validatePasswordConfirmation);
  }

  @override
  void dispose() {
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _tokenExpiryTimer?.cancel();
    super.dispose();
  }

  void _startTokenExpiryTimer() {
    _tokenExpiryCountdown = 1800; // 30 minutes

    _tokenExpiryTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _tokenExpiryCountdown--;
          if (_tokenExpiryCountdown <= 0) {
            timer.cancel();
            _showTokenExpiredDialog();
          }
        });
      } else {
        timer.cancel();
      }
    });
  }

  void _showTokenExpiredDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                FetchPixels.getPixelHeight(20),
              ),
            ),
            backgroundColor: daltiCard,
            title: getCustomFont(
              "Session Expired",
              18,
              daltiTextHeadline,
              1,
              fontWeight: FontWeight.w700,
            ),
            content: getMultilineCustomFont(
              "Your password reset session has expired. Please start the password reset process again.",
              14,
              daltiTextBody,
              fontWeight: FontWeight.w400,
              txtHeight: 1.3,
            ),
            actions: [
              TextButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Constant.sendToNext(
                    context,
                    Routes.passwordResetRequestRoute,
                  );
                },
                child: getCustomFont(
                  "Start Over",
                  14,
                  daltiPrimary,
                  1,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
    );
  }

  void _validatePassword() {
    setState(() {
      _passwordValidation = PasswordValidator.validatePassword(
        _passwordController.text,
      );
    });
    _validatePasswordConfirmation();
  }

  void _validatePasswordConfirmation() {
    setState(() {
      _passwordsMatch = PasswordValidator.validatePasswordConfirmation(
        _passwordController.text,
        _confirmPasswordController.text,
      );
    });
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Future<void> _resetPassword() async {
    // Validate password manually since text fields don't support validators
    if (_passwordController.text.isEmpty) {
      showCustomSnackBar(
        context,
        "Please enter a password",
        CustomSnackBarType.warning,
        backgroundColor: daltiWarningYellow,
        textColor: daltiTextHeadline,
      );
      return;
    }

    if (_confirmPasswordController.text.isEmpty) {
      showCustomSnackBar(
        context,
        "Please confirm your password",
        CustomSnackBarType.warning,
        backgroundColor: daltiWarningYellow,
        textColor: daltiTextHeadline,
      );
      return;
    }

    if (!_passwordValidation.isValid) {
      showCustomSnackBar(
        context,
        "Please ensure your password meets all requirements",
        CustomSnackBarType.warning,
        backgroundColor: daltiWarningYellow,
        textColor: daltiTextHeadline,
      );
      return;
    }

    if (!_passwordsMatch) {
      showCustomSnackBar(
        context,
        "Passwords do not match",
        CustomSnackBarType.warning,
        backgroundColor: daltiWarningYellow,
        textColor: daltiTextHeadline,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      String message = await PasswordResetService.resetPassword(
        widget.resetToken,
        _passwordController.text,
      );

      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        _showSuccessDialog(message);
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });

        String errorMessage = e.toString();
        CustomSnackBarType errorType = CustomSnackBarType.error;
        Color backgroundColor = daltiErrorRed;

        if (e is PasswordResetException) {
          switch (e.type) {
            case PasswordResetErrorType.tokenExpired:
              errorType = CustomSnackBarType.warning;
              backgroundColor = daltiWarningYellow;
              // Show expired dialog after a short delay
              Future.delayed(const Duration(milliseconds: 500), () {
                if (mounted) _showTokenExpiredDialog();
              });
              break;
            case PasswordResetErrorType.invalidToken:
              errorType = CustomSnackBarType.warning;
              backgroundColor = daltiWarningYellow;
              break;
            default:
              break;
          }
        }

        showCustomSnackBar(
          context,
          errorMessage,
          errorType,
          backgroundColor: backgroundColor,
          textColor: daltiTextOnPrimary,
        );
      }
    }
  }

  void _showSuccessDialog(String message) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(
                FetchPixels.getPixelHeight(20),
              ),
            ),
            backgroundColor: daltiCard,
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                getVerSpace(FetchPixels.getPixelHeight(20)),
                Icon(
                  Icons.check_circle,
                  size: FetchPixels.getPixelHeight(64),
                  color: daltiSuccessGreen,
                ),
                getVerSpace(FetchPixels.getPixelHeight(20)),
                getCustomFont(
                  "Password Reset Successful",
                  20,
                  daltiTextHeadline,
                  1,
                  fontWeight: FontWeight.w800,
                  textAlign: TextAlign.center,
                ),
                getVerSpace(FetchPixels.getPixelHeight(10)),
                getMultilineCustomFont(
                  message,
                  14,
                  daltiTextBody,
                  fontWeight: FontWeight.w400,
                  txtHeight: 1.3,
                  textAlign: TextAlign.center,
                ),
                getVerSpace(FetchPixels.getPixelHeight(30)),
                getButton(
                  context,
                  daltiPrimary,
                  "Continue to Login",
                  daltiTextOnPrimary,
                  () {
                    Navigator.of(context).pop();
                    Constant.sendToNext(context, Routes.loginRoute);
                  },
                  16,
                  weight: FontWeight.w600,
                  buttonHeight: FetchPixels.getPixelHeight(50),
                  borderRadius: BorderRadius.circular(
                    FetchPixels.getPixelHeight(12),
                  ),
                ),
                getVerSpace(FetchPixels.getPixelHeight(20)),
              ],
            ),
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    final localizations = AppLocalizations.of(context)!;

    return WillPopScope(
      onWillPop: () async {
        Constant.backToPrev(context);
        return false;
      },
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        backgroundColor: daltiBackground,
        body: SafeArea(
          child: Container(
            width: double.infinity,
            height: double.infinity,
            padding: EdgeInsets.symmetric(
              horizontal: FetchPixels.getDefaultHorSpace(context),
            ),
            child: Form(
              key: _formKey,
              child: ListView(
                children: [
                  getVerSpace(FetchPixels.getPixelHeight(20)),
                  Align(
                    alignment: Alignment.topLeft,
                    child: IconButton(
                      icon: Icon(Icons.arrow_back, color: daltiIconDefault),
                      onPressed: () {
                        Constant.backToPrev(context);
                      },
                    ),
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(20)),
                  Align(
                    alignment: Alignment.topCenter,
                    child: getCustomFont(
                      "Create New Password",
                      24,
                      daltiTextHeadline,
                      1,
                      fontWeight: FontWeight.w800,
                    ),
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(10)),
                  Align(
                    alignment: Alignment.topCenter,
                    child: getMultilineCustomFont(
                      "Create a strong password for your account. Make sure it meets all the requirements below.",
                      16,
                      daltiTextBody,
                      fontWeight: FontWeight.w400,
                      txtHeight: 1.3,
                      textAlign: TextAlign.center,
                    ),
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(30)),

                  // Token expiry warning
                  if (_tokenExpiryCountdown <=
                      300) // Show warning when less than 5 minutes
                    Container(
                      padding: EdgeInsets.all(FetchPixels.getPixelHeight(12)),
                      margin: EdgeInsets.only(
                        bottom: FetchPixels.getPixelHeight(20),
                      ),
                      decoration: BoxDecoration(
                        color: daltiWarningYellow.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(
                          FetchPixels.getPixelHeight(12),
                        ),
                        border: Border.all(
                          color: daltiWarningYellow.withOpacity(0.3),
                        ),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.warning,
                            size: FetchPixels.getPixelHeight(18),
                            color: daltiWarningYellow,
                          ),
                          getHorSpace(FetchPixels.getPixelWidth(8)),
                          Expanded(
                            child: getCustomFont(
                              "Session expires in ${_formatTime(_tokenExpiryCountdown)}",
                              12,
                              daltiWarningYellow,
                              1,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),

                  // New password field
                  getDefaultTextFiledWithLabel(
                    context,
                    "New Password",
                    _passwordController,
                    daltiTextMuted,
                    function: () {},
                    height: FetchPixels.getPixelHeight(60),
                    isEnable: false,
                    withprefix: true,
                    image: "lock.svg",
                    isPass: !_isPasswordVisible,
                    withSufix: true,
                    suffiximage: _isPasswordVisible ? "eye_off.svg" : "eye.svg",
                    imagefunction: () {
                      setState(() {
                        _isPasswordVisible = !_isPasswordVisible;
                      });
                    },
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(16)),

                  // Password strength indicator
                  PasswordStrengthIndicator(
                    strength: _passwordValidation.strength,
                    password: _passwordController.text,
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(20)),

                  // Confirm password field
                  getDefaultTextFiledWithLabel(
                    context,
                    "Confirm New Password",
                    _confirmPasswordController,
                    daltiTextMuted,
                    function: () {},
                    height: FetchPixels.getPixelHeight(60),
                    isEnable: false,
                    withprefix: true,
                    image: "lock.svg",
                    isPass: !_isConfirmPasswordVisible,
                    withSufix: true,
                    suffiximage:
                        _isConfirmPasswordVisible ? "eye_off.svg" : "eye.svg",
                    imagefunction: () {
                      setState(() {
                        _isConfirmPasswordVisible = !_isConfirmPasswordVisible;
                      });
                    },
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(20)),

                  // Password requirements
                  PasswordRequirementsWidget(
                    validationResult: _passwordValidation,
                  ),
                  getVerSpace(FetchPixels.getPixelHeight(30)),

                  // Reset password button
                  _isLoading
                      ? Center(
                        child: CircularProgressIndicator(color: daltiPrimary),
                      )
                      : getButton(
                        context,
                        daltiPrimary,
                        "Reset Password",
                        daltiTextOnPrimary,
                        _resetPassword,
                        18,
                        weight: FontWeight.w600,
                        buttonHeight: FetchPixels.getPixelHeight(60),
                        borderRadius: BorderRadius.circular(
                          FetchPixels.getPixelHeight(15),
                        ),
                      ),
                  getVerSpace(FetchPixels.getPixelHeight(20)),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}
