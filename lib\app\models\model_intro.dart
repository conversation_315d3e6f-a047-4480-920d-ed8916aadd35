import 'dart:ui';

class ModelIntro {
  int? id;
  String titleKey;
  String descriptionKey;
  String? image;
  Color color;

  // New fields for dynamic content
  String? dynamicTitle;
  String? dynamicDescription;
  String? callToActionText;
  String? callToActionLink;
  bool isExternal;

  ModelIntro(
    this.id,
    this.titleKey,
    this.descriptionKey,
    this.image,
    this.color, {
    this.dynamicTitle,
    this.dynamicDescription,
    this.callToActionText,
    this.callToActionLink,
    this.isExternal = false,
  });

  // Helper method to get the title (dynamic or localized)
  String getTitle(String Function(String) localizer) {
    return dynamicTitle ?? localizer(titleKey);
  }

  // Helper method to get the description (dynamic or localized)
  String getDescription(String Function(String) localizer) {
    return dynamicDescription ?? localizer(descriptionKey);
  }
}
