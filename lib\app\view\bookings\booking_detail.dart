import 'dart:convert';
import 'dart:math' show cos, sqrt, asin, min, max;
import 'dart:ui' as ui; // For ImageFilter if we use BackdropFilter

import 'package:dalti/app/models/model_appointment.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart'; // Import for PlatformException
import 'package:geolocator/geolocator.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart'; // Added for launching maps
import 'package:dalti/l10n/app_localizations.dart';
import 'package:dalti/base/auth_utils.dart';
import 'package:dalti/app/view/home/<USER>';

import '../../../base/color_data.dart';
import '../../../base/constant.dart';
import '../../../app/routes/app_routes.dart';

const String prefsKeySessionId = 'session_id';
const String _googleApiKey =
    'AIzaSyCtTA3ffRdOxj6asEufx-edi-MsqFYfsGY'; // YOUR API KEY

class BookingDetail extends StatefulWidget {
  final ModelAppointment appointment;

  const BookingDetail({Key? key, required this.appointment}) : super(key: key);

  @override
  State<BookingDetail> createState() => _BookingDetailState();
}

class _BookingDetailState extends State<BookingDetail> {
  final DateFormat _dateFormat = DateFormat('dd MMMM, yyyy, hh:mm a');
  final DateFormat _timeFormat = DateFormat('hh:mm a');
  bool _isCancelling = false;
  late ModelAppointment _currentAppointment;

  GoogleMapController? _mapController;
  Position? _currentUserPosition;
  LatLng? _providerLatLng;
  Set<Marker> _markers = {};
  Set<Polyline> _polylines = {}; // Added for drawing routes

  // Updated state variables for API-driven data
  String? _apiDriveDistanceText;
  String? _apiDriveDurationText;
  String?
  _apiWalkDistanceText; // Optional: API also returns distance for walking
  String? _apiWalkDurationText;

  bool _isLoadingLocation = true; // Covers initial location fetching
  bool _isLoadingDirections = false; // Specific for Directions API calls

  bool _locationPermissionGranted = false;
  String? _locationError;
  String? _mapInitializationError;

  @override
  void initState() {
    super.initState();
    _currentAppointment = widget.appointment;
    _extractProviderLocation();
    _getUserLocationAndDistance();
  }

  @override
  void dispose() {
    _mapController?.dispose();
    super.dispose();
  }

  void _extractProviderLocation() {
    final lat = _currentAppointment.place.detailedAddress?.latitude;
    final lng = _currentAppointment.place.detailedAddress?.longitude;
    if (lat != null && lng != null) {
      if (mounted) {
        _providerLatLng = LatLng(lat, lng);
        _addMarkers(); // Add provider marker immediately
        // Directions will be fetched after user location is also available
      }
    }
  }

  Future<bool> _handleLocationPermission() async {
    bool serviceEnabled;
    LocationPermission permission;
    if (!mounted) return false;
    try {
      serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        if (mounted)
          setState(() => _locationError = 'Location services are disabled.');
        return false;
      }
      permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          if (mounted)
            setState(() => _locationError = 'Location permissions are denied.');
          return false;
        }
      }
      if (permission == LocationPermission.deniedForever) {
        if (mounted)
          setState(
            () =>
                _locationError = 'Location permissions are permanently denied.',
          );
        return false;
      }
      if (mounted)
        setState(() {
          _locationPermissionGranted = true;
          _locationError = null;
        });
      return true;
    } on PlatformException catch (e) {
      if (mounted)
        setState(() {
          _locationError = "Failed to check permissions: ${e.message}";
          _isLoadingLocation = false;
          _isLoadingDirections = false;
        });
      return false;
    } catch (e) {
      if (mounted)
        setState(() {
          _locationError = "Error checking permissions: ${e.toString()}";
          _isLoadingLocation = false;
          _isLoadingDirections = false;
        });
      return false;
    }
  }

  Future<void> _getUserLocationAndDistance() async {
    if (mounted) {
      setState(() {
        _isLoadingLocation = true;
        _locationError = null;
        _mapInitializationError = null;
        _apiDriveDistanceText = null;
        _apiDriveDurationText = null;
        _apiWalkDistanceText = null;
        _apiWalkDurationText = null;
        _polylines.clear(); // Clear existing polylines
      });
    }
    final hasPermission = await _handleLocationPermission();
    if (!hasPermission) {
      if (mounted) setState(() => _isLoadingLocation = false);
      return;
    }
    try {
      Position position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
      if (mounted) {
        _currentUserPosition = position;
        _addMarkers(); // Add user marker
        _isLoadingLocation = false; // User location fetched

        // Now fetch directions if both locations are available
        if (_currentUserPosition != null && _providerLatLng != null) {
          await _fetchDirectionsFromApi();
        } else {
          setState(
            () => _isLoadingDirections = false,
          ); // No directions to fetch
        }
        setState(() {}); // General update
      }
    } on PlatformException catch (e) {
      if (mounted) {
        setState(() {
          _locationError = "Could not get location: ${e.message}.";
          _isLoadingLocation = false;
          _isLoadingDirections = false;
          _polylines.clear();
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _locationError = "Error getting location: ${e.toString()}.";
          _isLoadingLocation = false;
          _isLoadingDirections = false;
          _polylines.clear();
        });
      }
    }
  }

  Future<void> _fetchDirectionsFromApi() async {
    if (_currentUserPosition == null || _providerLatLng == null) return;

    if (mounted) {
      setState(() {
        _isLoadingDirections = true;
        _locationError =
            null; // Clear previous location errors if we are now fetching directions
        _polylines.clear(); // Clear polylines for new fetch
      });
    }

    final originWaypoint = {
      "location": {
        "latLng": {
          "latitude": _currentUserPosition!.latitude,
          "longitude": _currentUserPosition!.longitude,
        },
      },
    };
    final destinationWaypoint = {
      "location": {
        "latLng": {
          "latitude": _providerLatLng!.latitude,
          "longitude": _providerLatLng!.longitude,
        },
      },
    };

    // Fetch Driving Directions
    await _fetchRouteDetails(originWaypoint, destinationWaypoint, 'DRIVE');
    // Fetch Walking Directions
    await _fetchRouteDetails(originWaypoint, destinationWaypoint, 'WALK');

    if (mounted) {
      setState(() {
        _isLoadingDirections = false;
      });
    }
  }

  // Helper function to decode polyline
  List<LatLng> _decodePolyline(String encoded) {
    List<LatLng> points = [];
    int index = 0, len = encoded.length;
    int lat = 0, lng = 0;

    while (index < len) {
      int b, shift = 0, result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lat += dlat;

      shift = 0;
      result = 0;
      do {
        b = encoded.codeUnitAt(index++) - 63;
        result |= (b & 0x1f) << shift;
        shift += 5;
      } while (b >= 0x20);
      int dlng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
      lng += dlng;

      points.add(LatLng(lat / 1E5, lng / 1E5));
    }
    return points;
  }

  Future<void> _fetchRouteDetails(
    Map<String, dynamic> originWaypoint,
    Map<String, dynamic> destinationWaypoint,
    String travelMode,
  ) async {
    const url = 'https://routes.googleapis.com/directions/v2:computeRoutes';
    final headers = {
      'Content-Type': 'application/json',
      'X-Goog-Api-Key': _googleApiKey,
      'X-Goog-FieldMask':
          'routes.duration,routes.distanceMeters,routes.polyline.encodedPolyline',
    };

    final bodyPayload = {
      "origin": originWaypoint,
      "destination": destinationWaypoint,
      "travelMode": travelMode,
      "languageCode": "en-US",
      "units": "METRIC",
    };
    final body = json.encode(bodyPayload);
    print(
      "Routes API Request for $travelMode: URL: $url, Headers: $headers, Body: $body",
    );

    try {
      final response = await http.post(
        Uri.parse(url),
        headers: headers,
        body: body,
      );
      final responseBody = response.body;
      print(
        "Routes API Response for $travelMode (HTTP ${response.statusCode}): $responseBody",
      );

      if (response.statusCode == 200) {
        final data = json.decode(responseBody);

        if (data['routes'] != null && (data['routes'] as List).isNotEmpty) {
          final route = data['routes'][0];
          String? durationText;
          String? distanceText;

          if (route['duration'] != null) {
            String durationString = route['duration'] as String;
            durationString = durationString.replaceAll('s', '');
            double seconds = double.tryParse(durationString) ?? 0;
            durationText = _formatDurationFromSeconds(seconds);
          }
          if (route['distanceMeters'] != null) {
            int meters = route['distanceMeters'] as int;
            distanceText = _formatDistanceMeters(meters);
          }

          String? encodedPolyline;
          if (route['polyline'] != null &&
              route['polyline']['encodedPolyline'] != null) {
            encodedPolyline = route['polyline']['encodedPolyline'] as String;
            print("Received encodedPolyline for $travelMode: $encodedPolyline");
          } else {
            print(
              "No encodedPolyline found for $travelMode in route object: ${route['polyline']}",
            );
          }

          if (mounted) {
            setState(() {
              if (travelMode == 'DRIVE') {
                _apiDriveDistanceText = distanceText ?? 'N/A';
                _apiDriveDurationText = durationText ?? 'N/A';
                if (encodedPolyline != null) {
                  List<LatLng> points = [];
                  try {
                    points = _decodePolyline(encodedPolyline);
                    print(
                      "Decoded polyline for DRIVE ($travelMode): ${points.length} points. First 5: ${points.take(5).toList()}",
                    );
                  } catch (e) {
                    print("Error decoding polyline for $travelMode: $e");
                  }

                  if (points.isNotEmpty) {
                    _polylines.clear();
                    _polylines.add(
                      Polyline(
                        polylineId: const PolylineId(
                          'drive_route',
                        ), // Changed ID for clarity
                        points: points,
                        color: daltiPrimary,
                        width: 6,
                      ),
                    );
                    print("Updated _polylines set for DRIVE: $_polylines");
                  } else {
                    _polylines.clear();
                    print(
                      "No points after decoding polyline for $travelMode, clearing polylines.",
                    );
                  }
                } else {
                  _polylines.clear();
                  print(
                    "encodedPolyline is null for $travelMode, clearing polylines.",
                  );
                }
              } else if (travelMode == 'WALK') {
                _apiWalkDistanceText = distanceText ?? 'N/A';
                _apiWalkDurationText = durationText ?? 'N/A';
                // Optionally, draw walk polyline too, perhaps with different color/ID
                // if (encodedPolyline != null) { ... similar logic for walk ... }
              }
            });
          }
        } else {
          String errorMessage = "No routes found";
          if (data['error'] != null && data['error']?['message'] != null) {
            errorMessage = data['error']['message'] as String;
          } else if (data['error'] != null) {
            errorMessage = data['error'].toString();
          }
          print('No routes found or API error for $travelMode: $errorMessage');
          if (mounted) {
            setState(() {
              if (travelMode == 'DRIVE') {
                _apiDriveDistanceText = 'N/A';
                _apiDriveDurationText = 'N/A';
                _polylines.clear();
              } else if (travelMode == 'WALK') {
                _apiWalkDistanceText = 'N/A';
                _apiWalkDurationText = 'N/A';
              }
              _locationError =
                  (_locationError ?? "") + " $travelMode: $errorMessage";
            });
          }
        }
      } else {
        String errorDetail = responseBody;
        try {
          final errorData = json.decode(responseBody);
          if (errorData['error'] != null &&
              errorData['error']?['message'] != null) {
            errorDetail = errorData['error']['message'] as String;
          } else if (errorData['error'] != null) {
            errorDetail = errorData['error'].toString();
          }
        } catch (e) {
          print("Could not parse error response body: $e");
        }
        print(
          'Failed to load $travelMode route (HTTP ${response.statusCode}): $errorDetail',
        );
        if (mounted) {
          setState(() {
            if (travelMode == 'DRIVE') {
              _apiDriveDistanceText = 'N/A';
              _apiDriveDurationText = 'N/A';
              _polylines.clear();
            } else if (travelMode == 'WALK') {
              _apiWalkDistanceText = 'N/A';
              _apiWalkDurationText = 'N/A';
            }
            _locationError =
                (_locationError ?? "") +
                " $travelMode Error (HTTP ${response.statusCode}): $errorDetail";
          });
        }
      }
    } catch (e) {
      print('Exception during $travelMode route fetch: $e');
      if (mounted) {
        setState(() {
          if (travelMode == 'DRIVE') {
            _apiDriveDistanceText = 'N/A';
            _apiDriveDurationText = 'N/A';
            _polylines.clear();
          } else if (travelMode == 'WALK') {
            _apiWalkDistanceText = 'N/A';
            _apiWalkDurationText = 'N/A';
          }
          _locationError =
              (_locationError ?? "") +
              " Exception processing $travelMode route.";
        });
      }
    }
  }

  String _formatDurationFromSeconds(double totalSeconds) {
    if (totalSeconds <= 0) return "N/A";
    int hours = totalSeconds ~/ 3600;
    int minutes = ((totalSeconds % 3600) ~/ 60);
    if (hours > 0) {
      return "$hours hr ${minutes > 0 ? '$minutes min' : ''}".trim();
    } else if (minutes > 0) {
      return "$minutes min";
    } else {
      return "< 1 min";
    }
  }

  String _formatDistanceMeters(int meters) {
    if (meters <= 0) return "N/A";
    if (meters < 1000) {
      return "$meters m";
    } else {
      double km = meters / 1000.0;
      return "${km.toStringAsFixed(1)} km";
    }
  }

  void _addMarkers() {
    if (!mounted) return;
    Set<Marker> tempMarkers = {};
    if (_providerLatLng != null) {
      tempMarkers.add(
        Marker(
          markerId: const MarkerId('providerLocation'),
          position: _providerLatLng!,
          infoWindow: InfoWindow(
            title: _currentAppointment.place.name ?? 'Provider Location',
          ),
          icon: BitmapDescriptor.defaultMarkerWithHue(BitmapDescriptor.hueRed),
        ),
      );
    }
    if (false && _currentUserPosition != null) {
      tempMarkers.add(
        Marker(
          markerId: const MarkerId('userLocation'),
          position: LatLng(
            _currentUserPosition!.latitude,
            _currentUserPosition!.longitude,
          ),
          infoWindow: const InfoWindow(title: 'Your Location'),
          icon: BitmapDescriptor.defaultMarkerWithHue(
            BitmapDescriptor.hueGreen,
          ),
        ),
      );
    }
    setState(() {
      _markers = tempMarkers;
    });

    if (_mapController != null) {
      if (_providerLatLng != null && _currentUserPosition != null) {
        LatLngBounds bounds = LatLngBounds(
          southwest: LatLng(
            min(_providerLatLng!.latitude, _currentUserPosition!.latitude),
            min(_providerLatLng!.longitude, _currentUserPosition!.longitude),
          ),
          northeast: LatLng(
            max(_providerLatLng!.latitude, _currentUserPosition!.latitude),
            max(_providerLatLng!.longitude, _currentUserPosition!.longitude),
          ),
        );
        _mapController!.animateCamera(
          CameraUpdate.newLatLngBounds(bounds, 100),
        );
      } else if (_providerLatLng != null) {
        _mapController!.animateCamera(
          CameraUpdate.newLatLngZoom(_providerLatLng!, 14.0),
        );
      }
    }
  }

  Future<void> _launchDirectionsInMapsApp() async {
    if (_providerLatLng == null) return;

    final lat = _providerLatLng!.latitude;
    final lng = _providerLatLng!.longitude;
    String mapsUrl = '';

    String fallbackUrl =
        'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng';

    if (Theme.of(context).platform == TargetPlatform.android) {
      mapsUrl = 'google.navigation:q=$lat,$lng';
    } else if (Theme.of(context).platform == TargetPlatform.iOS) {
      mapsUrl = 'maps://?daddr=$lat,$lng';
    }

    try {
      if (mapsUrl.isNotEmpty && await canLaunchUrl(Uri.parse(mapsUrl))) {
        await launchUrl(Uri.parse(mapsUrl));
      } else if (await canLaunchUrl(Uri.parse(fallbackUrl))) {
        await launchUrl(Uri.parse(fallbackUrl));
      } else {
        throw 'Could not launch any maps URL.';
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Could not open maps: ${e.toString()}')),
        );
      }
    }
  }

  Future<void> _processCancellation() async {
    if (!mounted) return;
    setState(() => _isCancelling = true);
    final prefs = await SharedPreferences.getInstance();
    final sessionId = prefs.getString(prefsKeySessionId);
    if (sessionId == null || sessionId.isEmpty) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Authentication error."),
            backgroundColor: Colors.red,
          ),
        );
        setState(() => _isCancelling = false);
      }
      return;
    }
    final String apiUrl =
        "https://dapi-test.adscloud.org:8443/api/auth/appointments/${_currentAppointment.id}/cancel-customer-request";
    try {
      final response = await http.put(
        Uri.parse(apiUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $sessionId',
        },
      );
      if (!mounted) return;
      if (response.statusCode == 200 || response.statusCode == 204) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text("Booking cancelled."),
            backgroundColor: success,
          ),
        );
        setState(
          () =>
              _currentAppointment = _currentAppointment.copyWith(
                status: "Cancelled",
              ),
        );
        Future.delayed(Duration(seconds: 1), () {
          if (mounted) Navigator.pop(context, true);
        });
      } else {
        String errorMessage = "Failed to cancel booking.";
        try {
          final errorData = jsonDecode(response.body);
          errorMessage = errorData['message'] as String? ?? errorMessage;
        } catch (_) {}
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(errorMessage), backgroundColor: Colors.red),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Error: $e"), backgroundColor: Colors.red),
      );
    } finally {
      if (mounted) setState(() => _isCancelling = false);
    }
  }

  void _showCancelConfirmationDialog() {
    final localizations = AppLocalizations.of(context)!;
    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
          ),
          backgroundColor: appCardBackground,
          title: Text(
            localizations.bookingDetailCancelConfirmTitle,
            style: TextStyle(
              color: appDarkText,
              fontWeight: FontWeight.w700,
              fontSize: 18,
            ),
          ),
          content: Text(
            localizations.bookingDetailCancelConfirmMessage,
            style: TextStyle(color: appMediumText, fontSize: 15),
          ),
          actionsPadding: EdgeInsets.symmetric(
            horizontal: FetchPixels.getPixelWidth(10),
            vertical: FetchPixels.getPixelHeight(8),
          ),
          actions: <Widget>[
            TextButton(
              child: Text(
                localizations.detailScreenCancel,
                style: TextStyle(
                  color: appMediumText,
                  fontWeight: FontWeight.w500,
                  fontSize: 15,
                ),
              ),
              onPressed: () => Navigator.of(dialogContext).pop(),
            ),
            TextButton(
              child:
                  _isCancelling
                      ? SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(error),
                        ),
                      )
                      : Text(
                        localizations.detailScreenConfirm,
                        style: TextStyle(
                          color: error,
                          fontWeight: FontWeight.bold,
                          fontSize: 15,
                        ),
                      ),
              onPressed: () {
                if (_isCancelling) return;
                Navigator.of(dialogContext).pop();
                _processCancellation();
              },
            ),
          ],
        );
      },
    );
  }

  Color _getStatusBackgroundColor(String? status) {
    status = status?.toLowerCase();
    switch (status) {
      case 'pending':
        return Colors.orangeAccent.shade100;
      case 'confirmed':
        return appLightTeal;
      case 'completed':
        return procced;
      case 'cancelled':
      case 'canceled':
      case 'noshow':
        return intro1Color;
      default:
        return Colors.grey.shade300;
    }
  }

  String _getStatusText(String? status) {
    status = status?.toLowerCase();
    switch (status) {
      case 'pending':
        return "Pending";
      case 'confirmed':
        return "Active";
      case 'completed':
        return "Completed";
      case 'cancelled':
      case 'canceled':
        return "Cancelled";
      case 'noshow':
        return "No Show";
      default:
        return status?.capitalize() ?? "Unknown";
    }
  }

  Color _getStatusTextColor(String? status) {
    status = status?.toLowerCase();
    switch (status) {
      case 'pending':
        return Colors.orange.shade800;
      case 'confirmed':
        return themedBlueColor;
      case 'completed':
        return completed;
      case 'cancelled':
      case 'canceled':
        return error;
      case 'noshow':
        return error;
      default:
        return Colors.black87;
    }
  }

  IconData _getStatusIcon(String? status) {
    status = status?.toLowerCase();
    switch (status) {
      case 'pending':
        return Icons.hourglass_empty_rounded;
      case 'confirmed':
        return Icons.check_circle_outline_rounded;
      case 'completed':
        return Icons.check_circle_rounded;
      case 'cancelled':
      case 'canceled':
        return Icons.cancel_rounded;
      case 'noshow':
        return Icons.visibility_off_rounded;
      default:
        return Icons.help_outline_rounded;
    }
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    FetchPixels(context);
    final double screenHeight = MediaQuery.of(context).size.height;
    final double topSafePadding = MediaQuery.of(context).padding.top;
    final double appBarHeight = kToolbarHeight;
    final double topMapPadding =
        topSafePadding + appBarHeight + FetchPixels.getPixelHeight(10);

    // Calculate the height of the DraggableScrollableSheet at its initial size
    const double initialSheetSize =
        0.6; // This should match your DraggableScrollableSheet
    final double sheetHeightAtInitial = screenHeight * initialSheetSize;

    // The bottom padding for the map should be the height of the sheet
    // to ensure the provider marker is centered in the visible map area above the sheet.
    final double mapBottomPadding = sheetHeightAtInitial;

    return WillPopScope(
      onWillPop: () async {
        Navigator.pop(context, false);
        return false;
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        appBar: PreferredSize(
          preferredSize: Size.zero,
          child: AppBar(backgroundColor: Colors.transparent, elevation: 0),
        ),
        body: Stack(
          children: <Widget>[
            Positioned.fill(
              child: _buildFullSizedMapView(
                topMapPadding,
                mapBottomPadding, // Use the calculated bottom padding
              ),
            ),
            Positioned(
              top: topSafePadding, // Use MediaQuery padding top for safety
              left: 0,
              right: 0,
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: FetchPixels.getPixelWidth(16),
                  vertical: FetchPixels.getPixelHeight(
                    10,
                  ), // Standard vertical padding for app bar items
                ),
                child: gettoolbarMenu(
                  context,
                  "back.svg",
                  () => Navigator.pop(context, false),
                  title:
                      '', // Title is handled by the sheet now or can be set if needed
                  fontsize: 20,
                  weight: FontWeight.w800,
                  textColor:
                      Theme.of(context).brightness == Brightness.dark
                          ? Colors.white
                          : Colors.black,
                  istext: true,
                ),
              ),
            ),
            DraggableScrollableSheet(
              initialChildSize: initialSheetSize, // Use the constant here
              minChildSize: 0.20,
              maxChildSize: 0.9,
              builder: (
                BuildContext context,
                ScrollController scrollController,
              ) {
                return Container(
                  padding: EdgeInsets.only(top: FetchPixels.getPixelHeight(8)),
                  decoration: BoxDecoration(
                    color: Theme.of(context).cardColor,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                    boxShadow: [
                      BoxShadow(
                        blurRadius: 20.0,
                        color: Colors.black.withOpacity(0.15),
                        offset: Offset(0, -5),
                      ),
                    ],
                  ),
                  child: ListView(
                    controller: scrollController,
                    padding: EdgeInsets.zero,
                    children: <Widget>[
                      Center(
                        child: Container(
                          width: FetchPixels.getPixelWidth(50),
                          height: 5,
                          margin: EdgeInsets.only(
                            bottom: FetchPixels.getPixelHeight(10),
                          ),
                          decoration: BoxDecoration(
                            color: Colors.grey[300],
                            borderRadius: BorderRadius.circular(10),
                          ),
                        ),
                      ),
                      _buildLocationStatusInfo(),
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: FetchPixels.getPixelWidth(20),
                          vertical: FetchPixels.getPixelHeight(10),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            getCustomFont(
                              _dateFormat.format(
                                _currentAppointment.expectedAppointmentStartTime
                                    .toLocal(),
                              ),
                              14,
                              textColor,
                              1,
                              fontWeight: FontWeight.w400,
                            ),
                            getVerSpace(FetchPixels.getPixelHeight(8)),
                            getCustomFont(
                              _currentAppointment.service.title,
                              18,
                              appDarkText,
                              2,
                              fontWeight: FontWeight.w600,
                            ),
                            getVerSpace(FetchPixels.getPixelHeight(20)),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Row(
                                  children: [
                                    Icon(
                                      _getStatusIcon(
                                        _currentAppointment.status,
                                      ),
                                      color: _getStatusTextColor(
                                        _currentAppointment.status,
                                      ),
                                      size: FetchPixels.getPixelHeight(22),
                                    ),
                                    getHorSpace(FetchPixels.getPixelWidth(8)),
                                    getCustomFont(
                                      _getStatusText(
                                        _currentAppointment.status,
                                      ),
                                      16,
                                      _getStatusTextColor(
                                        _currentAppointment.status,
                                      ),
                                      1,
                                      fontWeight: FontWeight.w600,
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            getVerSpace(FetchPixels.getPixelHeight(20)),
                            getDivider(dividerColor.withOpacity(0.5), 0, 1),
                            getVerSpace(FetchPixels.getPixelHeight(20)),
                            getCustomFont(
                              localizations.bookingDetailProviderDetails,
                              16,
                              appDarkText,
                              1,
                              fontWeight: FontWeight.w700,
                            ),
                            getVerSpace(FetchPixels.getPixelHeight(10)),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                Expanded(
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.center,
                                    children: [
                                      CircleAvatar(
                                        radius: FetchPixels.getPixelHeight(24),
                                        backgroundColor: Colors.grey.shade200,
                                        child: Icon(
                                          Icons.person_rounded,
                                          size: FetchPixels.getPixelHeight(26),
                                          color: Colors.grey.shade400,
                                        ),
                                      ),
                                      getHorSpace(
                                        FetchPixels.getPixelWidth(12),
                                      ),
                                      Expanded(
                                        child: GestureDetector(
                                          onTap: () {
                                            Navigator.push(
                                              context,
                                              MaterialPageRoute(
                                                builder:
                                                    (
                                                      context,
                                                    ) => ProviderProfileScreen(
                                                      providerId:
                                                          _currentAppointment
                                                              .customerFolder
                                                              .provider
                                                              .id
                                                              .toString(),
                                                    ),
                                              ),
                                            );
                                          },
                                          child: Column(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              getCustomFont(
                                                _currentAppointment
                                                    .customerFolder
                                                    .provider
                                                    .title,
                                                15,
                                                appDarkText,
                                                1,
                                                fontWeight: FontWeight.w600,
                                              ),
                                              if (_currentAppointment
                                                      .customerFolder
                                                      .provider
                                                      .user
                                                      .fullName !=
                                                  'N/A')
                                                getCustomFont(
                                                  _currentAppointment
                                                      .customerFolder
                                                      .provider
                                                      .user
                                                      .fullName,
                                                  13,
                                                  appMediumText,
                                                  1,
                                                  fontWeight: FontWeight.w400,
                                                ),
                                              getCustomFont(
                                                _currentAppointment
                                                    .customerFolder
                                                    .provider
                                                    .category
                                                    .title,
                                                13,
                                                appMediumText,
                                                1,
                                                fontWeight: FontWeight.w400,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    IconButton(
                                      style: IconButton.styleFrom(
                                        backgroundColor: daltiPrimary,
                                        padding: EdgeInsets.all(
                                          FetchPixels.getPixelHeight(7),
                                        ),
                                        shape: RoundedRectangleBorder(
                                          borderRadius: BorderRadius.circular(
                                            FetchPixels.getPixelHeight(50),
                                          ),
                                        ),
                                      ),
                                      icon: Icon(
                                        Icons.call,
                                        color: Colors.white,
                                      ),
                                      onPressed: _handleCallPress,
                                      padding: EdgeInsets.zero,
                                      constraints: BoxConstraints(),
                                    ),
                                    getHorSpace(FetchPixels.getPixelWidth(8)),
                                    IconButton(
                                      style: IconButton.styleFrom(
                                        backgroundColor: daltiPrimary,
                                        padding: EdgeInsets.all(
                                          FetchPixels.getPixelHeight(7),
                                        ),
                                      ),
                                      icon: Icon(
                                        Icons.message_sharp,
                                        color: Colors.white,
                                      ),
                                      onPressed: _handleMessagePress,
                                      padding: EdgeInsets.zero,
                                      constraints: BoxConstraints(),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                            getVerSpace(FetchPixels.getPixelHeight(15)),
                            if (_currentAppointment
                                    .place
                                    .detailedAddress
                                    ?.address !=
                                null)
                              Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Icon(
                                    Icons.location_on_outlined,
                                    color: appMediumText,
                                    size: FetchPixels.getPixelHeight(18),
                                  ),
                                  getHorSpace(FetchPixels.getPixelWidth(8)),
                                  Expanded(
                                    child: getCustomFont(
                                      "${_currentAppointment.place.detailedAddress!.address}, ${_currentAppointment.place.detailedAddress!.city}, ${_currentAppointment.place.detailedAddress!.country}",
                                      14,
                                      appMediumText,
                                      2,
                                      fontWeight: FontWeight.w400,
                                    ),
                                  ),
                                ],
                              ),
                            getVerSpace(FetchPixels.getPixelHeight(20)),
                            getCustomFont(
                              localizations.bookingDetailAppointmentFor,
                              16,
                              appDarkText,
                              1,
                              fontWeight: FontWeight.w700,
                            ),
                            getVerSpace(FetchPixels.getPixelHeight(10)),
                            getCustomFont(
                              "${localizations.bookingDetailServicePrefix}${_currentAppointment.service.title}",
                              14,
                              appMediumText,
                              1,
                              fontWeight: FontWeight.w500,
                            ),
                            if (_currentAppointment.queueTitle != null)
                              getCustomFont(
                                "${localizations.bookingDetailQueuePrefix}${_currentAppointment.queueTitle}",
                                14,
                                appMediumText,
                                1,
                                fontWeight: FontWeight.w500,
                              ),
                            getCustomFont(
                              "${localizations.bookingDetailTimePrefix}${_timeFormat.format(_currentAppointment.expectedAppointmentStartTime.toLocal())} - ${_timeFormat.format(_currentAppointment.expectedAppointmentEndTime.toLocal())}",
                              14,
                              appMediumText,
                              1,
                              fontWeight: FontWeight.w500,
                            ),
                            if (_currentAppointment
                                .customerFolder
                                .provider
                                .title
                                .isNotEmpty)
                              Row(
                                children: [
                                  Icon(
                                    Icons.phone_outlined,
                                    size: 18,
                                    color: appMediumText,
                                  ),
                                  getHorSpace(FetchPixels.getPixelWidth(8)),
                                  Expanded(
                                    child: getCustomFont(
                                      _currentAppointment
                                          .customerFolder
                                          .provider
                                          .title,
                                      14,
                                      appMediumText,
                                      1,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  TextButton.icon(
                                    onPressed: _handleCallPress,
                                    icon: Icon(
                                      Icons.call,
                                      color: themedBlueColor,
                                    ),
                                    label: Text(
                                      localizations.bookingDetailCallButton,
                                      style: TextStyle(color: themedBlueColor),
                                    ),
                                  ),
                                ],
                              ),
                            if (_currentAppointment
                                        .place
                                        .detailedAddress
                                        ?.latitude !=
                                    null &&
                                _currentAppointment
                                        .place
                                        .detailedAddress
                                        ?.longitude !=
                                    null)
                              Row(
                                children: [
                                  Icon(
                                    Icons.location_on_outlined,
                                    size: 18,
                                    color: appMediumText,
                                  ),
                                  getHorSpace(FetchPixels.getPixelWidth(8)),
                                  Expanded(
                                    child: getCustomFont(
                                      "${_currentAppointment.place.detailedAddress!.address}, ${_currentAppointment.place.detailedAddress!.city}, ${_currentAppointment.place.detailedAddress!.country}",
                                      14,
                                      appMediumText,
                                      2,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  TextButton.icon(
                                    onPressed: _handleMapPress,
                                    icon: Icon(
                                      Icons.map,
                                      color: themedBlueColor,
                                    ),
                                    label: Text(
                                      localizations.bookingDetailMapButton,
                                      style: TextStyle(color: themedBlueColor),
                                    ),
                                  ),
                                ],
                              ),
                          ],
                        ),
                      ),
                      Padding(
                        padding: EdgeInsets.symmetric(
                          horizontal: FetchPixels.getPixelWidth(20),
                          vertical: FetchPixels.getPixelHeight(15),
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                          children: <Widget>[
                            if (_providerLatLng != null)
                              Expanded(
                                child: ElevatedButton.icon(
                                  icon: Icon(
                                    Icons.map_outlined,
                                    color: Colors.white,
                                    size: FetchPixels.getPixelHeight(18),
                                  ),
                                  label: getCustomFont(
                                    localizations.bookingDetailMapButton,
                                    15,
                                    Colors.white,
                                    1,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  onPressed: _launchDirectionsInMapsApp,
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: daltiPrimaryLight,
                                    padding: EdgeInsets.symmetric(
                                      vertical: FetchPixels.getPixelHeight(12),
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(
                                        FetchPixels.getPixelHeight(10),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            if (_providerLatLng != null &&
                                _currentAppointment.status == "confirmed")
                              SizedBox(width: FetchPixels.getPixelWidth(10)),

                            if (_currentAppointment.status == "confirmed")
                              Expanded(
                                child: ElevatedButton.icon(
                                  icon: Icon(
                                    Icons.timer_outlined,
                                    color: Colors.white,
                                    size: FetchPixels.getPixelHeight(18),
                                  ),
                                  label: getCustomFont(
                                    "Queue",
                                    15,
                                    Colors.white,
                                    1,
                                    fontWeight: FontWeight.bold,
                                  ),
                                  onPressed:
                                      () => Constant.sendToNext(
                                        context,
                                        Routes.queueManagementRoute,
                                      ),
                                  style: ElevatedButton.styleFrom(
                                    backgroundColor: daltiPrimary,
                                    padding: EdgeInsets.symmetric(
                                      vertical: FetchPixels.getPixelHeight(12),
                                    ),
                                    shape: RoundedRectangleBorder(
                                      borderRadius: BorderRadius.circular(
                                        FetchPixels.getPixelHeight(10),
                                      ),
                                    ),
                                  ),
                                ),
                              ),
                            if (_currentAppointment.status == "confirmed" &&
                                (_currentAppointment.status.toLowerCase() ==
                                        'confirmed' ||
                                    _currentAppointment.status.toLowerCase() ==
                                        'pending'))
                              SizedBox(width: FetchPixels.getPixelWidth(10)),

                            if (_currentAppointment.status.toLowerCase() ==
                                    'confirmed' ||
                                _currentAppointment.status.toLowerCase() ==
                                    'pending')
                              Expanded(
                                child:
                                    _isCancelling
                                        ? Center(
                                          child: SizedBox(
                                            height: FetchPixels.getPixelHeight(
                                              24,
                                            ),
                                            width: FetchPixels.getPixelHeight(
                                              24,
                                            ),
                                            child: CircularProgressIndicator(
                                              color: error,
                                              strokeWidth: 2.5,
                                            ),
                                          ),
                                        )
                                        : OutlinedButton.icon(
                                          icon: Icon(
                                            Icons.cancel_outlined,
                                            color: error,
                                            size: FetchPixels.getPixelHeight(
                                              18,
                                            ),
                                          ),
                                          label: Text(
                                            localizations
                                                .bookingDetailCancelButton,
                                            style: TextStyle(
                                              fontSize: 15,
                                              fontWeight: FontWeight.w600,
                                              color: error,
                                            ),
                                          ),
                                          onPressed:
                                              _showCancelConfirmationDialog,
                                          style: OutlinedButton.styleFrom(
                                            backgroundColor: Theme.of(
                                              context,
                                            ).cardColor.withOpacity(0.9),
                                            side: BorderSide(
                                              color: error.withOpacity(0.7),
                                              width: 1.5,
                                            ),
                                            padding: EdgeInsets.symmetric(
                                              vertical:
                                                  FetchPixels.getPixelHeight(
                                                    12,
                                                  ),
                                            ),
                                            shape: RoundedRectangleBorder(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                    FetchPixels.getPixelHeight(
                                                      10,
                                                    ),
                                                  ),
                                            ),
                                          ),
                                        ),
                              ),
                          ],
                        ),
                      ),
                      if (_currentAppointment.status != 'completed' &&
                          _currentAppointment.status != 'canceled')
                        Padding(
                          padding: EdgeInsets.only(
                            top: FetchPixels.getPixelHeight(16),
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              TextButton.icon(
                                onPressed: _handleCancelPress,
                                icon: Icon(Icons.cancel_outlined, color: error),
                                label: Text(
                                  localizations.bookingDetailCancelButton,
                                  style: TextStyle(color: error),
                                ),
                                style: TextButton.styleFrom(
                                  padding: EdgeInsets.symmetric(
                                    horizontal: FetchPixels.getPixelWidth(20),
                                    vertical: FetchPixels.getPixelHeight(10),
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      SizedBox(
                        height:
                            MediaQuery.of(context).padding.bottom +
                            FetchPixels.getPixelHeight(20),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFullSizedMapView(double topPadding, double bottomPadding) {
    if (_mapInitializationError != null) {
      return Container(
        color: Colors.grey[300],
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              "Map Error: $_mapInitializationError\nPlease check API key, internet, and Play Services.",
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.red[700], fontSize: 16),
            ),
          ),
        ),
      );
    }

    try {
      return GoogleMap(
        mapType: MapType.normal,
        initialCameraPosition: CameraPosition(
          target: _providerLatLng ?? const LatLng(36.7783, 3.0588),
          zoom: _providerLatLng == null ? 5 : 13,
        ),
        onMapCreated: (GoogleMapController controller) {
          if (mounted) {
            _mapController = controller;
            _addMarkers();
            if (_mapInitializationError != null) {
              setState(() => _mapInitializationError = null);
            }
          }
        },
        markers: _markers,
        polylines: _polylines,
        myLocationButtonEnabled: false,
        myLocationEnabled: _locationPermissionGranted,
        zoomControlsEnabled: false,
        zoomGesturesEnabled: true,
        scrollGesturesEnabled: true,
        tiltGesturesEnabled: false,
        rotateGesturesEnabled: false,
        padding: EdgeInsets.only(top: topPadding, bottom: bottomPadding),
      );
    } on PlatformException catch (e) {
      print("GoogleMap PlatformException: ${e.code} - ${e.message}");
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && _mapInitializationError == null) {
          setState(() {
            _mapInitializationError = "Map service unavailable: ${e.message}.";
          });
        }
      });
      return Container(
        color: Colors.grey[300],
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              _mapInitializationError ?? "Initializing Map...",
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.red[700]),
            ),
          ),
        ),
      );
    } catch (e) {
      print("GoogleMap Unexpected Exception: $e");
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted && _mapInitializationError == null) {
          setState(() {
            _mapInitializationError =
                "An unexpected error occurred with the map: ${e.toString()}";
          });
        }
      });
      return Container(
        color: Colors.grey[300],
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Text(
              _mapInitializationError ?? "Initializing Map...",
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.red[700]),
            ),
          ),
        ),
      );
    }
  }

  Widget _buildLocationStatusInfo() {
    final localizations = AppLocalizations.of(context)!;
    TextStyle infoTextStyle = TextStyle(
      color: appDarkText,
      fontSize: FetchPixels.getPixelHeight(14),
      fontWeight: FontWeight.w500,
    );
    TextStyle N_A_TextStyle = TextStyle(
      color: appMediumText,
      fontSize: FetchPixels.getPixelHeight(13),
      fontWeight: FontWeight.normal,
    );
    double iconSize = FetchPixels.getPixelHeight(18); // Consistent icon size
    EdgeInsetsGeometry itemInnerPadding = EdgeInsets.only(
      right: FetchPixels.getPixelWidth(6),
    );

    if (_isLoadingLocation || _isLoadingDirections) {
      return Padding(
        padding: EdgeInsets.symmetric(vertical: FetchPixels.getPixelHeight(12)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: iconSize,
              height: iconSize,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                color: daltiPrimary,
              ),
            ),
            getHorSpace(FetchPixels.getPixelWidth(10)),
            Text(
              _isLoadingLocation
                  ? localizations.bookingDetailFetchingLocation
                  : localizations.bookingDetailCalculatingRoutes,
              style: infoTextStyle.copyWith(color: daltiPrimary),
            ),
          ],
        ),
      );
    }

    // Determine primary distance to display
    String? displayDistance;
    if (_apiDriveDistanceText != null && _apiDriveDistanceText != 'N/A') {
      displayDistance = _apiDriveDistanceText;
    } else if (_apiWalkDistanceText != null && _apiWalkDistanceText != 'N/A') {
      displayDistance = _apiWalkDistanceText;
    }

    if (_locationError != null && displayDistance == null) {
      // Only show primary error if no distance info is available at all
      return Padding(
        padding: EdgeInsets.symmetric(vertical: FetchPixels.getPixelHeight(8)),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, color: error, size: iconSize),
            getHorSpace(FetchPixels.getPixelWidth(8)),
            Expanded(
              child: Text(
                _locationError!, // Show the most relevant error
                style: N_A_TextStyle.copyWith(
                  color: error,
                  fontSize: FetchPixels.getPixelHeight(14),
                ),
                textAlign: TextAlign.center,
              ),
            ),
          ],
        ),
      );
    }

    List<Widget> infoSegments = [];

    // Add Distance
    if (displayDistance != null) {
      infoSegments.add(
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: itemInnerPadding,
              child: Icon(
                Icons.map_outlined,
                size: iconSize,
                color: appDarkText,
              ),
            ),
            Text(
              localizations.bookingDetailDistance(displayDistance),
              style: infoTextStyle,
            ),
          ],
        ),
      );
    }

    // Add Drive ETA
    if (_apiDriveDurationText != null && _apiDriveDurationText != 'N/A') {
      infoSegments.add(
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: itemInnerPadding,
              child: Icon(
                Icons.directions_car_rounded,
                size: iconSize,
                color: appDarkText,
              ),
            ),
            Text(
              localizations.bookingDetailDuration(_apiDriveDurationText!),
              style: infoTextStyle,
            ),
          ],
        ),
      );
    }

    // Add Walk ETA
    if (_apiWalkDurationText != null && _apiWalkDurationText != 'N/A') {
      infoSegments.add(
        Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: itemInnerPadding,
              child: Icon(
                Icons.directions_walk_rounded,
                size: iconSize,
                color: appDarkText,
              ),
            ),
            Text(
              localizations.bookingDetailDuration(_apiWalkDurationText!),
              style: infoTextStyle,
            ),
          ],
        ),
      );
    }

    if (infoSegments.isEmpty) {
      Widget fallbackContent;
      if (!_locationPermissionGranted && _mapInitializationError == null) {
        fallbackContent = Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.location_off_rounded,
              color: appMediumText,
              size: iconSize,
            ),
            getHorSpace(FetchPixels.getPixelWidth(8)),
            Expanded(
              child: Text(
                localizations.bookingDetailLocationPermissionDenied,
                style: N_A_TextStyle,
                textAlign: TextAlign.center,
              ),
            ),
          ],
        );
      } else {
        fallbackContent = Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.info_outline_rounded,
              color: appMediumText,
              size: iconSize,
            ),
            getHorSpace(FetchPixels.getPixelWidth(8)),
            Expanded(
              child: Text(
                localizations.bookingDetailLocationServiceDisabled,
                style: N_A_TextStyle,
                textAlign: TextAlign.center,
              ),
            ),
          ],
        );
      }
      return Padding(
        padding: EdgeInsets.symmetric(
          horizontal: FetchPixels.getPixelWidth(20),
          vertical: FetchPixels.getPixelHeight(12),
        ),
        child: fallbackContent,
      );
    }

    return Padding(
      padding: EdgeInsets.symmetric(
        horizontal: FetchPixels.getPixelWidth(16),
        vertical: FetchPixels.getPixelHeight(10),
      ),
      child: Wrap(
        alignment: WrapAlignment.center,
        spacing: FetchPixels.getPixelWidth(12),
        runSpacing: FetchPixels.getPixelHeight(6),
        children: infoSegments,
      ),
    );
  }

  void _handleCallPress() {
    final localizations = AppLocalizations.of(context)!;
    final phoneNumber = _currentAppointment.customerFolder.provider.title;
    if (phoneNumber.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(localizations.detailScreenNoPhoneNumber)),
      );
      return;
    }
    final Uri launchUri = Uri(scheme: 'tel', path: phoneNumber);
    launchUrl(launchUri).catchError((error) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(localizations.detailScreenCannotLaunchCall)),
      );
    });
  }

  void _handleMessagePress() {
    // ... implementation of message handling ...
  }

  void _handleMapPress() async {
    final localizations = AppLocalizations.of(context)!;
    final lat = _currentAppointment.place.detailedAddress?.latitude;
    final lng = _currentAppointment.place.detailedAddress?.longitude;

    if (lat == null || lng == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            localizations.bookingDetailLocationError('Location not available'),
          ),
        ),
      );
      return;
    }

    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(localizations.bookingDetailLocationServiceDisabled),
          ),
        );
        return;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                localizations.bookingDetailLocationPermissionDenied,
              ),
            ),
          );
          return;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(localizations.bookingDetailLocationPermissionDenied),
          ),
        );
        return;
      }

      Position position = await Geolocator.getCurrentPosition();
      final Uri mapsUri = Uri.parse(
        'https://www.google.com/maps/dir/?api=1&origin=${position.latitude},${position.longitude}&destination=$lat,$lng',
      );

      if (await canLaunchUrl(mapsUri)) {
        await launchUrl(mapsUri);
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                localizations.bookingDetailDirectionsError(
                  'Could not launch maps',
                ),
              ),
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              localizations.bookingDetailLocationError(e.toString()),
            ),
          ),
        );
      }
    }
  }

  void _handleCancelPress() async {
    final localizations = AppLocalizations.of(context)!;

    bool? shouldCancel = await showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(localizations.bookingDetailCancelConfirmTitle),
          content: Text(localizations.bookingDetailCancelConfirmMessage),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: Text(localizations.detailScreenCancel),
            ),
            TextButton(
              onPressed: () => Navigator.of(context).pop(true),
              child: Text(localizations.detailScreenConfirm),
            ),
          ],
        );
      },
    );

    if (shouldCancel != true || !mounted) return;

    try {
      final response = await http.delete(
        Uri.parse(
          'https://dapi-test.adscloud.org:8443/api/auth/customer/appointments/${_currentAppointment.id}',
        ),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer ${await getSessionId()}',
        },
      );

      if (!mounted) return;

      if (response.statusCode == 200) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(localizations.bookingDetailCancelSuccess)),
        );
        Navigator.of(context).pop(true); // Return true to indicate cancellation
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(localizations.bookingDetailCancelError)),
        );
      }
    } catch (e) {
      if (!mounted) return;
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(localizations.bookingDetailCancelError)),
      );
    }
  }
}

extension StringExtension on String {
  String capitalize() {
    if (this.isEmpty) return "";
    return "${this[0].toUpperCase()}${this.substring(1).toLowerCase()}";
  }
}
