// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for French (`fr`).
class AppLocalizationsFr extends AppLocalizations {
  AppLocalizationsFr([String locale = 'fr']) : super(locale);

  @override
  String get helloWorld => 'Bonjour le monde!';

  @override
  String get welcomeMessage => 'Bienvenue sur notre application';

  @override
  String get chooseLanguage => 'Choisissez votre langue';

  @override
  String get english => 'Anglais';

  @override
  String get arabic => 'Arabe';

  @override
  String get french => 'Français';

  @override
  String get nextButtonText => 'Suivant';

  @override
  String get skipButtonText => 'Passer';

  @override
  String get intro1Title => 'Titre de la diapositive d\'intro 1 (FR)';

  @override
  String get intro1Description =>
      'Description pour la diapositive d\'intro 1 (FR).';

  @override
  String get intro2Title => 'Titre de la diapositive d\'intro 2 (FR)';

  @override
  String get intro2Description =>
      'Description pour la diapositive d\'intro 2 (FR).';

  @override
  String get intro3Title => 'Titre de la diapositive d\'intro 3 (FR)';

  @override
  String get intro3Description =>
      'Description pour la diapositive d\'intro 3 (FR).';

  @override
  String get preferredMethodTitle => 'Choisissez votre méthode préférée';

  @override
  String get preferredMethodSubtitle =>
      'Sélectionnez comment vous souhaitez vous inscrire ou vous connecter.';

  @override
  String get useEmailButton => 'Utiliser l\'e-mail';

  @override
  String get useMobileButton => 'Utiliser le numéro de mobile';

  @override
  String get goBackButton => 'Retour';

  @override
  String get loginWithEmailTitle => 'Connexion avec e-mail';

  @override
  String get loginWithMobileTitle => 'Connexion avec mobile';

  @override
  String get loginSubtitle => 'Ravi de vous revoir !';

  @override
  String get emailLabel => 'E-mail';

  @override
  String get phoneNumberLabel => 'Numéro de mobile';

  @override
  String get passwordLabel => 'Mot de passe';

  @override
  String get forgotPasswordButton => 'Mot de passe oublié ?';

  @override
  String get loginButton => 'Connexion';

  @override
  String get dontHaveAccountText => 'Vous n\'avez pas de compte ?';

  @override
  String get signUpButtonLoginScreen => ' S\'inscrire';

  @override
  String get errorPhoneNumberEmpty =>
      'Le numéro de mobile ne peut pas être vide.';

  @override
  String get errorEmailPasswordEmpty =>
      'L\'e-mail et le mot de passe ne peuvent pas être vides.';

  @override
  String get errorPhonePasswordEmpty =>
      'Le numéro de mobile et le mot de passe ne peuvent pas être vides.';

  @override
  String errorLoginInvalidResponse(Object statusCode) {
    return 'Échec de la connexion : réponse du serveur invalide. Statut : $statusCode';
  }

  @override
  String get loginSuccess => 'Connexion réussie !';

  @override
  String get errorLoginNoSessionId =>
      'Échec de la connexion : ID de session non reçu.';

  @override
  String errorLoginWithMessageStatus(Object message, Object statusCode) {
    return 'Échec de la connexion : $message (Statut : $statusCode)';
  }

  @override
  String errorLoginGeneric(Object errorDetails) {
    return 'Une erreur s\'est produite lors de la connexion : $errorDetails';
  }

  @override
  String get signUpTitle => 'S\'inscrire';

  @override
  String get signUpSubtitleEmail => 'Inscrivez-vous avec votre e-mail.';

  @override
  String get signUpSubtitleMobile =>
      'Inscrivez-vous avec votre numéro de mobile.';

  @override
  String get signUpSubtitleGeneric =>
      'Entrez vos informations pour vous inscrire !';

  @override
  String get firstNameLabel => 'Prénom';

  @override
  String get lastNameLabel => 'Nom de famille';

  @override
  String get agreeTermsPrivacyText =>
      'J\'accepte les Termes et la Politique de confidentialité';

  @override
  String get signUpButton => 'S\'inscrire';

  @override
  String get alreadyHaveAccountText => 'Vous avez déjà un compte ?';

  @override
  String get loginButtonSignUpScreen => ' Se connecter';

  @override
  String get errorAgreeTerms =>
      'Veuillez accepter les Termes et la Politique de confidentialité.';

  @override
  String get errorEmailEmptyPreferred =>
      'L\'e-mail ne peut pas être vide car c\'est votre méthode préférée.';

  @override
  String get errorMobileEmptyPreferred =>
      'Le numéro de mobile ne peut pas être vide car c\'est votre méthode préférée.';

  @override
  String get errorProvideEmailOrPhone =>
      'Veuillez fournir un e-mail ou un numéro de mobile.';

  @override
  String get errorProvideSignUpDetails =>
      'Veuillez fournir des informations pour l\'inscription.';

  @override
  String errorUserExists(Object fieldInUse) {
    return '$fieldInUse est déjà utilisé(e).';
  }

  @override
  String errorUserExistsConflict(Object fieldInUse) {
    return '$fieldInUse est déjà utilisé(e) (conflit).';
  }

  @override
  String errorCheckingUser(Object serverMessage, Object statusCode) {
    return 'Erreur lors de la vérification de l\'utilisateur : $statusCode $serverMessage';
  }

  @override
  String errorFailedCheckUserExists(Object errorDetails) {
    return 'Échec de la vérification de l\'existence de l\'utilisateur : $errorDetails';
  }

  @override
  String get errorNoValidOtpIdentifier =>
      'Impossible de continuer sans e-mail ou téléphone valide pour l\'OTP.';

  @override
  String get forgotPasswordTitle => 'Mot de passe oublié ?';

  @override
  String get forgotPasswordSubtitleEmail =>
      'Entrez votre e-mail pour réinitialiser votre mot de passe.';

  @override
  String get forgotPasswordSubtitleMobile =>
      'Entrez votre numéro de téléphone pour réinitialiser votre mot de passe.';

  @override
  String get phoneNumberWithCodeLabel =>
      'Numéro de téléphone (code pays requis)';

  @override
  String get submitButton => 'Soumettre';

  @override
  String get resetPasswordTitle => 'Réinitialiser le mot de passe';

  @override
  String get resetPasswordSubtitle =>
      'Entrez les détails de votre nouveau mot de passe ci-dessous.';

  @override
  String get oldPasswordLabel => 'Ancien mot de passe';

  @override
  String get newPasswordLabel => 'Nouveau mot de passe';

  @override
  String get confirmPasswordLabel => 'Confirmer le mot de passe';

  @override
  String get setLocationDefault => 'Définir l\'emplacement';

  @override
  String get searchHintText => 'Rechercher...';

  @override
  String get homeBannerTitle => 'Service de peinture murale';

  @override
  String get homeBannerSubtitle => 'Rendez votre mur élégant';

  @override
  String get bookNowButton => 'Réserver';

  @override
  String get categoriesTitle => 'Catégories';

  @override
  String get seeAllButton => 'Voir tout';

  @override
  String errorLoadingCategories(Object errorMessage) {
    return 'Erreur : $errorMessage';
  }

  @override
  String get noCategoriesFound => 'Aucune catégorie trouvée.';

  @override
  String get noParentCategoriesFound => 'Aucune catégorie parente trouvée.';

  @override
  String get profileLoading => 'Chargement...';

  @override
  String get profileUserFallback => 'Utilisateur';

  @override
  String get profileErrorDisplayDetails => 'Erreur d\'affichage des détails';

  @override
  String get profileGuestUser => 'Invité';

  @override
  String get profilePleaseLogIn => 'Veuillez vous connecter';

  @override
  String get profileTabTitle => 'Profil';

  @override
  String get profileLogoutButton => 'Déconnexion';

  @override
  String get profileSettingsButton => 'Paramètres';

  @override
  String get profileMyAddressButton => 'Mon adresse';

  @override
  String get profileMyCardsButton => 'Mes cartes';

  @override
  String get profileMyProfileButton => 'Mon profil';

  @override
  String get bookingsTabTitle => 'Réservations';

  @override
  String get bookingsTabAll => 'Toutes';

  @override
  String get bookingsTabActive => 'Actives';

  @override
  String get bookingsTabCompleted => 'Terminées';

  @override
  String get bookingsTabCancelled => 'Annulées';

  @override
  String get messagesTabTitle => 'Messages';

  @override
  String get messagesSearchHint => 'Rechercher des messages...';

  @override
  String get messagesEmptyTitle => 'Aucun message';

  @override
  String get messagesEmptySubtitle =>
      'Votre liste de messages est actuellement vide.';

  @override
  String get doctorPrefix => 'Dr ';

  @override
  String get unnamedProvider => 'Prestataire non nommé';

  @override
  String get defaultSpecialization => 'Général';

  @override
  String get naHospital => 'Hôpital N/A';

  @override
  String get slotInfoUnavailable => 'Informations sur le créneau indisponibles';

  @override
  String errorFailedToFetchProviders(Object statusCode) {
    return 'Échec de la récupération des prestataires (Statut : $statusCode)';
  }

  @override
  String errorFetchingData(Object errorDetails) {
    return 'Erreur lors de la récupération des données : $errorDetails';
  }

  @override
  String searchResultCountForQuery(num count, Object query) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count résultats',
      one: '$count résultat',
      zero: 'Aucun résultat',
    );
    return '$_temp0 pour \'\'$query\'\'';
  }

  @override
  String searchResultCountForQueryInCategory(
    Object category,
    num count,
    Object query,
  ) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count résultats',
      one: '$count résultat',
      zero: 'Aucun résultat',
    );
    return '$_temp0 pour \'\'$query\'\' dans $category';
  }

  @override
  String searchResultCountForQueryInCity(Object city, num count, Object query) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count résultats',
      one: '$count résultat',
      zero: 'Aucun résultat',
    );
    return '$_temp0 pour \'\'$query\'\' à $city';
  }

  @override
  String searchResultCountForQueryInCategoryInCity(
    Object category,
    Object city,
    num count,
    Object query,
  ) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count résultats',
      one: '$count résultat',
      zero: 'Aucun résultat',
    );
    return '$_temp0 pour \'\'$query\'\' dans $category à $city';
  }

  @override
  String noProvidersFoundForQuery(Object query) {
    return 'Aucun prestataire trouvé pour \'\'$query\'\'.';
  }

  @override
  String noProvidersFoundForQueryWithCategory(Object category, Object query) {
    return 'Aucun prestataire trouvé pour \'\'$query\'\'\nCatégorie : $category';
  }

  @override
  String noProvidersFoundForQueryWithCity(Object city, Object query) {
    return 'Aucun prestataire trouvé pour \'\'$query\'\'\nVille : $city';
  }

  @override
  String noProvidersFoundForQueryWithCategoryAndCity(
    Object category,
    Object city,
    Object query,
  ) {
    return 'Aucun prestataire trouvé pour \'\'$query\'\'\nCatégorie : $category\nVille : $city';
  }

  @override
  String get searchScreenTitle => 'Recherche';

  @override
  String get searchByKeywordHint => 'Recherche par mot-clé';

  @override
  String get hospitalPrefix => 'à ';

  @override
  String experienceInYears(Object years) {
    return 'Exp. $years ans';
  }

  @override
  String feesDisplay(Object amount, Object currencySymbol) {
    return 'Frais $currencySymbol$amount';
  }

  @override
  String reviewCountDisplay(Object count) {
    return '($count avis)';
  }

  @override
  String get nextAvailableSlotTitle => 'Prochain créneau disponible';

  @override
  String get filtersModalTitle => 'Filtres';

  @override
  String get filtersCategorySectionTitle => 'Type de service (Catégorie)';

  @override
  String get filtersSelectCategoryPlaceholder => 'Sélectionner une catégorie';

  @override
  String get filtersCitySectionTitle => 'Ville (Wilaya)';

  @override
  String get filtersEnterCityHint => 'Entrer la ville ou Wilaya';

  @override
  String get filtersClearButton => 'Effacer les filtres';

  @override
  String get filtersApplyButton => 'Appliquer les filtres';

  @override
  String get filtersAllCategoriesTitle => 'Toutes les catégories';

  @override
  String get filtersNoSubcategories => 'Aucune sous-catégorie.';

  @override
  String get filtersNoCategoriesAvailable => 'Aucune catégorie disponible.';

  @override
  String errorFetchingCategories(Object errorDetails) {
    return 'Erreur lors de la récupération des catégories : $errorDetails';
  }

  @override
  String get detailScreenCannotSelectPastDate =>
      'Impossible de sélectionner une date passée.';

  @override
  String get detailScreenBookAppointment => 'Prendre un rendez-vous';

  @override
  String get detailScreenServiceLabel => 'Service :';

  @override
  String get detailScreenNoServices => 'Aucun service disponible.';

  @override
  String get detailScreenQueueLabel => 'File d\'attente :';

  @override
  String get detailScreenGeneralAvailability =>
      'Disponibilité générale pour ce service.';

  @override
  String get detailScreenSelectDate => 'Sélectionner une date';

  @override
  String get detailScreenSelectTime => 'Sélectionner une heure';

  @override
  String get detailScreenPleaseSelectDate => 'Veuillez sélectionner une date.';

  @override
  String detailScreenNoSlotsFound(Object date) {
    return 'Aucun créneau trouvé pour le $date.';
  }

  @override
  String detailScreenAllSlotsBooked(Object date) {
    return 'Tous les créneaux sont réservés pour le $date.';
  }

  @override
  String get detailScreenConfirmBooking => 'Confirmer la réservation';

  @override
  String get detailScreenCancel => 'Annuler';

  @override
  String get detailScreenConfirm => 'Confirmer';

  @override
  String get detailScreenIncompleteDetails =>
      'Les détails de la réservation sont incomplets.';

  @override
  String get detailScreenPleaseLogin =>
      'Veuillez vous connecter pour effectuer une réservation.';

  @override
  String get detailScreenBookingSuccess => 'Réservation réussie !';

  @override
  String get detailScreenBookingFailed =>
      'Échec de la réservation. Veuillez réessayer.';

  @override
  String detailScreenErrorOccurred(Object error) {
    return 'Une erreur s\'est produite : $error';
  }

  @override
  String detailScreenPointsRequired(Object points) {
    return 'Ce service nécessite $points points pour réserver.';
  }

  @override
  String get detailScreenNoPhoneNumber => 'Numéro de téléphone non disponible.';

  @override
  String get detailScreenCannotLaunchCall =>
      'Impossible de lancer l\'appel téléphonique.';

  @override
  String get detailScreenServiceDetail => 'Service :';

  @override
  String get detailScreenQueueDetail => 'File d\'attente :';

  @override
  String get detailScreenDateDetail => 'Date :';

  @override
  String get detailScreenTimeDetail => 'Heure :';

  @override
  String detailScreenBook(Object date, Object time) {
    return 'Réserver : $time le $date';
  }

  @override
  String get settingsSecurityTitle => 'Sécurité';

  @override
  String get settingsHelpTitle => 'Aide et Support';

  @override
  String get settingsPrivacyTitle => 'Politique de Confidentialité';

  @override
  String get settingsTermsTitle => 'Conditions d\'Utilisation';

  @override
  String get settingsSecurityContent =>
      'Votre sécurité est notre priorité absolue. Nous mettons en œuvre des mesures de sécurité conformes aux normes de l\'industrie pour protéger vos données et garantir une expérience sûre lors de l\'utilisation de nos services.';

  @override
  String get settingsHelpContent =>
      'Besoin d\'aide ? Notre équipe de support est là pour vous aider avec toutes vos questions ou préoccupations concernant nos services.';

  @override
  String get settingsPrivacyContent =>
      'Nous valorisons votre vie privée. Cette politique décrit comment nous collectons, utilisons et protégeons vos informations personnelles lorsque vous utilisez nos services.';

  @override
  String get settingsTermsContent =>
      'En utilisant nos services, vous acceptez ces conditions. Veuillez les lire attentivement car elles régissent votre utilisation de notre plateforme et de nos services.';

  @override
  String get notificationsTitle => 'Notifications';

  @override
  String get notificationsToday => 'Aujourd\'hui';

  @override
  String get notificationsYesterday => 'Hier';

  @override
  String get notificationsRetry => 'Réessayer';

  @override
  String get notificationsEmpty => 'Pas encore de notifications !';

  @override
  String get notificationsEmptyDesc =>
      'Nous vous informerons quand quelque chose arrive.';

  @override
  String get queueStatusUpcoming => 'À venir';

  @override
  String get queueStatusCheckedIn => 'Enregistré';

  @override
  String get queueStatusWaitingRoom => 'Veuillez patienter confortablement';

  @override
  String get queueStatusCalledIn => 'On vous appelle !';

  @override
  String get queueStatusInProgress => 'Service en cours';

  @override
  String get queueStatusCompleted => 'Terminé';

  @override
  String get queueStatusSkipped => 'Manqué';

  @override
  String get queueStatusRequeued => 'Remis en file d\'attente';

  @override
  String get queueStatusCanceled => 'Annulé';

  @override
  String get queueStatusUnknown => 'Inconnu';

  @override
  String queueMessageUpcoming(int position) {
    return 'Vous êtes n°$position dans la file. Soyez prêt.';
  }

  @override
  String get queueMessageCheckedIn => 'Prévenez-nous à votre arrivée !';

  @override
  String queueMessageWaitingRoom(int position) {
    return 'Votre prestataire vous appellera bientôt. Vous êtes n°$position dans la file.';
  }

  @override
  String get queueMessageCalledIn =>
      'Veuillez vous diriger vers la zone de service.';

  @override
  String get queueMessageInProgress => 'Votre service est en cours.';

  @override
  String get queueMessageCompleted => 'Merci d\'avoir utilisé notre service !';

  @override
  String get queueMessageSkipped => 'Vous avez manqué votre tour.';

  @override
  String get queueMessageRequeued => 'Vous avez été remis en file d\'attente.';

  @override
  String get queueMessageCanceled => 'Votre réservation a été annulée.';

  @override
  String get bookingDetailProviderDetails => 'Détails du prestataire :';

  @override
  String get bookingDetailAppointmentFor => 'Rendez-vous pour :';

  @override
  String get bookingDetailServicePrefix => 'Service : ';

  @override
  String get bookingDetailQueuePrefix => 'File d\'attente : ';

  @override
  String get bookingDetailTimePrefix => 'Heure : ';

  @override
  String get bookingDetailMapButton => 'Carte';

  @override
  String get bookingDetailCallButton => 'Appeler';

  @override
  String get bookingDetailCancelButton => 'Annuler';

  @override
  String get bookingDetailCancelConfirmTitle => 'Annuler le rendez-vous ?';

  @override
  String get bookingDetailCancelConfirmMessage =>
      'Êtes-vous sûr de vouloir annuler ce rendez-vous ? Cette action ne peut pas être annulée.';

  @override
  String get bookingDetailCancelSuccess => 'Rendez-vous annulé avec succès.';

  @override
  String get bookingDetailCancelError =>
      'Échec de l\'annulation du rendez-vous. Veuillez réessayer.';

  @override
  String bookingDetailLocationError(Object error) {
    return 'Erreur lors de l\'obtention de la localisation : $error';
  }

  @override
  String get bookingDetailLocationPermissionDenied =>
      'Permission de localisation refusée';

  @override
  String get bookingDetailLocationServiceDisabled =>
      'Les services de localisation sont désactivés';

  @override
  String bookingDetailDirectionsError(Object error) {
    return 'Erreur lors de l\'obtention des directions : $error';
  }

  @override
  String bookingDetailDistance(Object distance) {
    return 'Distance : $distance';
  }

  @override
  String bookingDetailDuration(Object duration) {
    return 'Durée : $duration';
  }

  @override
  String get bookingDetailOpenInMaps => 'Ouvrir dans Maps';

  @override
  String get bookingDetailGetDirections => 'Obtenir les directions';

  @override
  String get bookingDetailFetchingLocation =>
      'Récupération de la localisation...';

  @override
  String get bookingDetailCalculatingRoutes => 'Calcul des itinéraires...';

  @override
  String get queueLiveStatusTitle => 'Statut de la file d\'attente en direct';

  @override
  String get queueTimerPaused => 'En pause';

  @override
  String get queueLoadingService => 'Chargement du service...';

  @override
  String get queueLoadingDate => 'Chargement de la date...';

  @override
  String get queueLoadingTime => 'Chargement de l\'heure...';

  @override
  String get queueFindingProfessional => 'Recherche d’un professionnel...';

  @override
  String get queueYouAreNext => 'Vous êtes le prochain';

  @override
  String get queueYourTurn => 'C\'est votre tour';

  @override
  String queueYourPosition(Object position) {
    return 'Votre position $position';
  }

  @override
  String get queueTimerPausedMessage =>
      'Minuteur en pause : en attente du début du service.';

  @override
  String get queueViewQRCode => 'Voir le code QR';

  @override
  String get queueCheckInDetails => 'Détails de l\'enregistrement';

  @override
  String get queueDone => 'Terminé';

  @override
  String get queueEmptyOrNotListed =>
      'La file est actuellement vide ou votre place n\'est pas répertoriée.';

  @override
  String get queueIncomingSwapRequests => 'Demandes d\'échange entrantes';

  @override
  String queueSwapRequestsFor(Object time) {
    return 'pour votre place à $time';
  }

  @override
  String queueSwapStatus(Object status) {
    return 'Statut : $status';
  }

  @override
  String queueSwapRequested(Object dateTime) {
    return 'Demandé : $dateTime';
  }

  @override
  String get queueReject => 'Refuser';

  @override
  String get queueAccept => 'Accepter';

  @override
  String queueSwapResponseSent(Object action) {
    return 'Réponse à l\'échange envoyée : $action';
  }

  @override
  String get queueSwapResponseAccepted => 'Acceptée';

  @override
  String get queueSwapResponseRejected => 'Refusée';

  @override
  String get queueNotConnected =>
      'Non connecté. Impossible de répondre à l\'échange.';

  @override
  String queueMemberLabelYouInProgress(Object position) {
    return 'Votre tour (En cours) - Pos $position';
  }

  @override
  String queueMemberLabelYouCompleted(Object position) {
    return 'Vous - Service terminé - Pos $position';
  }

  @override
  String queueMemberLabelYouStartingSoon(Object position) {
    return 'Vous - Commence bientôt - Pos $position';
  }

  @override
  String queueMemberLabelYouPosition(Object position) {
    return 'Vous - Position $position';
  }

  @override
  String queueMemberLabelOtherStartingSoon(Object name, Object position) {
    return '$name - Commence bientôt - Pos $position';
  }

  @override
  String queueMemberLabelOtherPosition(Object name, Object position) {
    return '$name - Pos $position';
  }

  @override
  String get queueMemberLabelSuffixInProgress => ' (En cours)';

  @override
  String get queueMemberLabelSuffixCompleted => ' (Terminé)';
}
