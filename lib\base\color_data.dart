import 'dart:ui';

// Original Colors (can be kept for reference or gradually replaced)
Color backGroundColor =
    "#F9FAFC".toColor(); // Will be updated to appPageBackground
Color blueColor = "#23408F".toColor(); // Will be updated to appPrimaryTeal

// Updated intro colors to use Dalti theme variants
Color intro1Color = daltiPrimaryLight; // Was #FFC8CF
Color intro2Color = daltiAccent.withOpacity(0.2); // Was #E5ECFF
Color intro3Color = daltiPrimary.withOpacity(0.3); // Was #F7FBCD

Color dividerColor = "#E5E8F1".toColor();
Color textColor =
    "#7F889E".toColor(); // Can be replaced by appMediumText or appDarkText
Color deatilColor = "#D3DFFF".toColor();
Color listColor = "#EEF1F9".toColor();
Color procced = "#E2EAFF".toColor();
Color success = "#04B155".toColor();
Color completed = "#0085FF".toColor();
Color error = "#FF2323".toColor();

// New Theme Colors from Image
Color appPrimaryTeal = "#2596BE".toColor();
Color appLightTeal = "#E1F5FE".toColor();
Color appCardBackground = "#FFFFFF".toColor();
Color appPageBackground = "#F4F7F9".toColor();
Color appButtonBorder = "#CED4DA".toColor();
Color appSelectedButtonBorder =
    appPrimaryTeal; // Selected border is the primary teal

Color appDarkText = "#343A40".toColor();
Color appMediumText = "#495057".toColor();
Color appLightText = "#6C757D".toColor();

Color appUnselectedButtonText = appMediumText;
Color appSelectedButtonText =
    "#FFFFFF"
        .toColor(); // White text for selected teal buttons, using toColor()

// Update main colors to use the new theme
// You might want to do this gradually or decide which old colors map to new ones.
// For now, let's update the most relevant ones for DetailScreen.
Color themedBlueColor = appPrimaryTeal; // Replacing old blueColor concept
Color themedBackgroundColor =
    appPageBackground; // Replacing old backGroundColor

// Dalti App Theme Colors (NEW - DO NOT REMOVE/UPDATE EXISTING COLORS ABOVE)
const Color daltiPrimary = Color(0xFF19727F); // App's main brand color
const Color daltiPrimaryLight = Color(0xFFFBB946); // Lighter shade of primary
const Color daltiAccent = Color(
  0xFFFFC107,
); // Accent color (e.g., for highlights, warnings)
const Color daltiBackground = Color(0xFFF4F7F9); // Default background for pages
const Color daltiCard = Color(0xFFFFFFFF); // Background for card elements
const Color daltiTextHeadline = Color(
  0xFF343A40,
); // For main titles and headlines
const Color daltiTextBody = Color(
  0xFF495057,
); // For general body text, subtitles
const Color daltiTextMuted = Color(
  0xFF6C757D,
); // For less important text, hints
const Color daltiTextOnPrimary = Color(
  0xFFFFFFFF,
); // Text color for on daltiPrimary backgrounds
const Color daltiIconDefault = Color(
  0xFF19727F,
); // Default color for icons - Used for category icons
const Color daltiIconActive = Color(
  0xFFFFFFFF,
); // Color for active or selected icons
const Color daltiIconBg = Color(0xFFFBF7EF);
const Color daltiDividerLine = Color(
  0xFFE0E0E0,
); // Color for dividers and borders
const Color daltiSuccessGreen = Color(
  0xFF28A745,
); // For success states/messages
const Color daltiWarningYellow = Color(
  0xFFFFC107,
); // For warning states/messages (can be same as accent)
const Color daltiErrorRed = Color(0xFFDC3545); // For error states/messages
const Color daltiInfoBlue = Color(0xFF17A2B8); // For info states/messages

// SUNSET PALETTE (2024)
// #773701 (Brown), #F49005 (Orange), #FBB946 (Yellow), #C1AD8C (Beige), #FFFFFF (White)
const Color appPrimary = Color(0xFFF49005); // Orange
const Color appSecondary = Color(0xFFFBB946); // Yellow
const Color appDeep = Color.fromARGB(255, 13, 90, 100); // Brown
const Color appMuted = Color(0xFF19727f); // Beige
const Color appBackground = Color(0xFFFFFFFF); // White

// Aliases for clarity
const Color appCard = Color.fromARGB(255, 255, 255, 255);
const Color appDivider = appMuted;
const Color appTextHeadline = appDeep;
const Color appTextBody = Color(
  0xFF4B3F2F,
); // Slightly lighter brown for body text
const Color appTextMuted = appMuted;
const Color appDebugBlack = Color(
  0xFF000000,
); // Temporary for debugging visibility

extension ColorExtension on String {
  Color toColor() {
    var hexColor = replaceAll("#", "").toUpperCase();
    if (hexColor.length == 6) {
      hexColor = "FF" + hexColor;
    }
    if (hexColor.length == 8) {
      try {
        return Color(int.parse("0x$hexColor"));
      } catch (e) {
        print("Error parsing color string '$this': $e. Defaulting to black.");
        return const Color(0xFF000000);
      }
    }
    print("Invalid hex color string format for '$this'. Defaulting to black.");
    return const Color(0xFF000000);
  }
}
