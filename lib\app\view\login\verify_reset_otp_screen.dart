import 'dart:async';
import 'package:dalti/app/routes/app_routes.dart';
import 'package:dalti/base/color_data.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:dalti/services/password_reset_service.dart';
import 'package:flutter/material.dart';
import 'package:pinput/pinput.dart';
import 'package:dalti/l10n/app_localizations.dart';

import '../../../base/constant.dart';

class VerifyResetOtpScreen extends StatefulWidget {
  final String email;

  const VerifyResetOtpScreen({
    Key? key,
    required this.email,
  }) : super(key: key);

  @override
  State<VerifyResetOtpScreen> createState() => _VerifyResetOtpScreenState();
}

class _VerifyResetOtpScreenState extends State<VerifyResetOtpScreen> {
  String _enteredOtp = "";
  bool _isLoading = false;
  bool _isResendEnabled = false;
  Timer? _resendTimer;
  Timer? _expiryTimer;
  int _resendCountdown = 60;
  int _expiryCountdown = 600; // 10 minutes

  @override
  void initState() {
    super.initState();
    _startResendTimer();
    _startExpiryTimer();
  }

  @override
  void dispose() {
    _resendTimer?.cancel();
    _expiryTimer?.cancel();
    super.dispose();
  }

  void _startResendTimer() {
    _resendCountdown = 60;
    _isResendEnabled = false;
    
    _resendTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _resendCountdown--;
          if (_resendCountdown <= 0) {
            _isResendEnabled = true;
            timer.cancel();
          }
        });
      } else {
        timer.cancel();
      }
    });
  }

  void _startExpiryTimer() {
    _expiryCountdown = 600; // 10 minutes
    
    _expiryTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (mounted) {
        setState(() {
          _expiryCountdown--;
          if (_expiryCountdown <= 0) {
            timer.cancel();
            _showOtpExpiredDialog();
          }
        });
      } else {
        timer.cancel();
      }
    });
  }

  void _showOtpExpiredDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(20)),
        ),
        backgroundColor: daltiCard,
        title: getCustomFont(
          "Code Expired",
          18,
          daltiTextHeadline,
          1,
          fontWeight: FontWeight.w700,
        ),
        content: getMultilineCustomFont(
          "Your verification code has expired. Please request a new code to continue.",
          14,
          daltiTextBody,
          fontWeight: FontWeight.w400,
          txtHeight: 1.3,
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Constant.backToPrev(context);
            },
            child: getCustomFont(
              "Request New Code",
              14,
              daltiPrimary,
              1,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  String _formatTime(int seconds) {
    int minutes = seconds ~/ 60;
    int remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Future<void> _verifyOtp() async {
    if (_enteredOtp.length != 6) {
      showCustomSnackBar(
        context,
        "Please enter the complete 6-digit code",
        CustomSnackBarType.warning,
        backgroundColor: daltiWarningYellow,
        textColor: daltiTextHeadline,
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      String resetToken = await PasswordResetService.verifyPasswordResetOtp(
        widget.email,
        _enteredOtp,
      );
      
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        // Navigate to new password screen with reset token
        Constant.sendToNext(
          context,
          Routes.newPasswordRoute,
          arguments: {
            'resetToken': resetToken,
            'email': widget.email,
          },
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        String errorMessage = e.toString();
        CustomSnackBarType errorType = CustomSnackBarType.error;
        Color backgroundColor = daltiErrorRed;
        
        if (e is PasswordResetException) {
          switch (e.type) {
            case PasswordResetErrorType.otpExpired:
              errorType = CustomSnackBarType.warning;
              backgroundColor = daltiWarningYellow;
              // Show expired dialog after a short delay
              Future.delayed(const Duration(milliseconds: 500), () {
                if (mounted) _showOtpExpiredDialog();
              });
              break;
            case PasswordResetErrorType.invalidOtp:
              errorType = CustomSnackBarType.warning;
              backgroundColor = daltiWarningYellow;
              break;
            case PasswordResetErrorType.userNotFound:
              errorType = CustomSnackBarType.warning;
              backgroundColor = daltiWarningYellow;
              break;
            default:
              break;
          }
        }
        
        showCustomSnackBar(
          context,
          errorMessage,
          errorType,
          backgroundColor: backgroundColor,
          textColor: daltiTextOnPrimary,
        );
      }
    }
  }

  Future<void> _resendOtp() async {
    if (!_isResendEnabled) return;

    setState(() {
      _isLoading = true;
    });

    try {
      String message = await PasswordResetService.requestPasswordResetOtp(widget.email);
      
      if (mounted) {
        setState(() {
          _isLoading = false;
          _enteredOtp = ""; // Clear current OTP
        });
        
        _startResendTimer();
        _startExpiryTimer(); // Restart expiry timer
        
        showCustomSnackBar(
          context,
          message,
          CustomSnackBarType.success,
          backgroundColor: daltiSuccessGreen,
          textColor: daltiTextOnPrimary,
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
        
        showCustomSnackBar(
          context,
          e.toString(),
          CustomSnackBarType.error,
          backgroundColor: daltiErrorRed,
          textColor: daltiTextOnPrimary,
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    final localizations = AppLocalizations.of(context)!;

    final defaultPinTheme = PinTheme(
      width: FetchPixels.getPixelWidth(60),
      height: FetchPixels.getPixelHeight(68),
      textStyle: TextStyle(
        fontSize: FetchPixels.getPixelHeight(24),
        color: daltiPrimary,
        fontWeight: FontWeight.w800,
      ),
      decoration: BoxDecoration(
        color: daltiCard,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0.0, 4.0),
          ),
        ],
        borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(15)),
      ),
    );

    return WillPopScope(
      onWillPop: () async {
        Constant.backToPrev(context);
        return false;
      },
      child: Scaffold(
        resizeToAvoidBottomInset: false,
        backgroundColor: daltiBackground,
        body: SafeArea(
          child: Container(
            width: double.infinity,
            height: double.infinity,
            padding: EdgeInsets.symmetric(
              horizontal: FetchPixels.getDefaultHorSpace(context),
            ),
            child: ListView(
              children: [
                getVerSpace(FetchPixels.getPixelHeight(20)),
                Align(
                  alignment: Alignment.topLeft,
                  child: IconButton(
                    icon: Icon(Icons.arrow_back, color: daltiIconDefault),
                    onPressed: () {
                      Constant.backToPrev(context);
                    },
                  ),
                ),
                getVerSpace(FetchPixels.getPixelHeight(20)),
                Align(
                  alignment: Alignment.topCenter,
                  child: getCustomFont(
                    "Verify Code",
                    24,
                    daltiTextHeadline,
                    1,
                    fontWeight: FontWeight.w800,
                  ),
                ),
                getVerSpace(FetchPixels.getPixelHeight(10)),
                Align(
                  alignment: Alignment.topCenter,
                  child: getMultilineCustomFont(
                    "Enter the 6-digit verification code sent to:\n${widget.email}",
                    16,
                    daltiTextBody,
                    fontWeight: FontWeight.w400,
                    txtHeight: 1.3,
                    textAlign: TextAlign.center,
                  ),
                ),
                getVerSpace(FetchPixels.getPixelHeight(40)),
                
                // OTP Input
                Pinput(
                  length: 6,
                  defaultPinTheme: defaultPinTheme,
                  pinputAutovalidateMode: PinputAutovalidateMode.onSubmit,
                  showCursor: true,
                  onChanged: (pin) {
                    _enteredOtp = pin;
                  },
                  onCompleted: (pin) {
                    _enteredOtp = pin;
                    _verifyOtp();
                  },
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                ),
                getVerSpace(FetchPixels.getPixelHeight(30)),
                
                // Expiry countdown
                Container(
                  padding: EdgeInsets.all(FetchPixels.getPixelHeight(16)),
                  decoration: BoxDecoration(
                    color: _expiryCountdown <= 60 
                      ? daltiWarningYellow.withOpacity(0.1)
                      : daltiInfoBlue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
                    border: Border.all(
                      color: _expiryCountdown <= 60 
                        ? daltiWarningYellow.withOpacity(0.3)
                        : daltiInfoBlue.withOpacity(0.3),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.timer,
                        size: FetchPixels.getPixelHeight(18),
                        color: _expiryCountdown <= 60 ? daltiWarningYellow : daltiInfoBlue,
                      ),
                      getHorSpace(FetchPixels.getPixelWidth(8)),
                      getCustomFont(
                        "Code expires in ${_formatTime(_expiryCountdown)}",
                        14,
                        _expiryCountdown <= 60 ? daltiWarningYellow : daltiInfoBlue,
                        1,
                        fontWeight: FontWeight.w600,
                      ),
                    ],
                  ),
                ),
                getVerSpace(FetchPixels.getPixelHeight(30)),
                
                // Verify button
                _isLoading
                    ? Center(child: CircularProgressIndicator(color: daltiPrimary))
                    : getButton(
                      context,
                      daltiPrimary,
                      "Verify Code",
                      daltiTextOnPrimary,
                      _verifyOtp,
                      18,
                      weight: FontWeight.w600,
                      buttonHeight: FetchPixels.getPixelHeight(60),
                      borderRadius: BorderRadius.circular(
                        FetchPixels.getPixelHeight(15),
                      ),
                    ),
                getVerSpace(FetchPixels.getPixelHeight(30)),
                
                // Resend section
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    getCustomFont(
                      "Didn't receive the code? ",
                      14,
                      daltiTextBody,
                      1,
                      fontWeight: FontWeight.w400,
                    ),
                    GestureDetector(
                      onTap: _isResendEnabled ? _resendOtp : null,
                      child: getCustomFont(
                        _isResendEnabled 
                          ? "Resend"
                          : "Resend in ${_resendCountdown}s",
                        14,
                        _isResendEnabled ? daltiPrimary : daltiTextMuted,
                        1,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                getVerSpace(FetchPixels.getPixelHeight(20)),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
