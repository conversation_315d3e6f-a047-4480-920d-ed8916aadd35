import './model_place.dart';

class ModelAppointmentService {
  final int id;
  final String title;
  final int duration;

  ModelAppointmentService({
    required this.id,
    required this.title,
    required this.duration,
  });

  factory ModelAppointmentService.fromJson(Map<String, dynamic> json) {
    return ModelAppointmentService(
      id: json['id'] as int,
      title: json['title'] as String? ?? 'N/A Service',
      duration: json['duration'] as int? ?? 30,
    );
  }
}

class ModelAppointmentPlace {
  final int id;
  final String name;

  ModelAppointmentPlace({required this.id, required this.name});

  factory ModelAppointmentPlace.fromJson(Map<String, dynamic> json) {
    return ModelAppointmentPlace(
      id: json['id'] as int,
      name: json['name'] as String? ?? 'N/A Place',
    );
  }
}

class ModelAppointmentProviderUser {
  final String? firstName;
  final String? lastName;

  ModelAppointmentProviderUser({this.firstName, this.lastName});

  factory ModelAppointmentProviderUser.fromJson(Map<String, dynamic> json) {
    return ModelAppointmentProviderUser(
      firstName: json['firstName'] as String?,
      lastName: json['lastName'] as String?,
    );
  }

  String get fullName {
    if (firstName != null && lastName != null) {
      return '$firstName $lastName';
    } else if (firstName != null) {
      return firstName!;
    } else if (lastName != null) {
      return lastName!;
    }
    return 'N/A';
  }
}

class ModelAppointmentProviderCategory {
  final int id;
  final String title;

  ModelAppointmentProviderCategory({required this.id, required this.title});

  factory ModelAppointmentProviderCategory.fromJson(Map<String, dynamic> json) {
    return ModelAppointmentProviderCategory(
      id: json['id'] as int? ?? 0,
      title: json['title'] as String? ?? 'N/A Category',
    );
  }
}

class ModelAppointmentProvider {
  final int id;
  final String title;
  final ModelAppointmentProviderUser user;
  final ModelAppointmentProviderCategory category;

  ModelAppointmentProvider({
    required this.id,
    required this.title,
    required this.user,
    required this.category,
  });

  factory ModelAppointmentProvider.fromJson(Map<String, dynamic> json) {
    return ModelAppointmentProvider(
      id: json['id'] as int,
      title: json['title'] as String? ?? 'N/A Provider',
      user: ModelAppointmentProviderUser.fromJson(
        json['user'] as Map<String, dynamic>? ?? {},
      ),
      category: ModelAppointmentProviderCategory.fromJson(
        json['category'] as Map<String, dynamic>? ?? {},
      ),
    );
  }
}

class ModelAppointmentCustomerFolder {
  final ModelAppointmentProvider provider;

  ModelAppointmentCustomerFolder({required this.provider});

  factory ModelAppointmentCustomerFolder.fromJson(Map<String, dynamic> json) {
    return ModelAppointmentCustomerFolder(
      provider: ModelAppointmentProvider.fromJson(
        json['provider'] as Map<String, dynamic>? ?? {},
      ),
    );
  }
}

class ModelAppointment {
  final int id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? canceledAt;
  final int? customerFolderId;
  final String? typeEvent;
  final String status;
  final int? serviceId;
  final int? serviceDuration;
  final DateTime expectedAppointmentStartTime;
  final DateTime expectedAppointmentEndTime;
  final DateTime? realAppointmentStartTime;
  final DateTime? realAppointmentEndTime;
  final String? notes;
  final int? openingHoursId;
  final int? queueId;
  final String? realTimeStatus;
  final int? slots;
  final bool? isOverflowed;
  final String? overflowReason;
  final String? overflowProcessingStatus;
  final DateTime? overflowDetectedAt;
  final DateTime? overflowProcessedAt;

  final ModelAppointmentService service;
  final ModelPlace place;
  final ModelAppointmentCustomerFolder customerFolder;
  final String? queueTitle;

  ModelAppointment({
    required this.id,
    this.createdAt,
    this.updatedAt,
    this.canceledAt,
    this.customerFolderId,
    this.typeEvent,
    required this.status,
    this.serviceId,
    this.serviceDuration,
    required this.expectedAppointmentStartTime,
    required this.expectedAppointmentEndTime,
    this.realAppointmentStartTime,
    this.realAppointmentEndTime,
    this.notes,
    this.openingHoursId,
    this.queueId,
    this.realTimeStatus,
    this.slots,
    this.isOverflowed,
    this.overflowReason,
    this.overflowProcessingStatus,
    this.overflowDetectedAt,
    this.overflowProcessedAt,
    required this.service,
    required this.place,
    required this.customerFolder,
    this.queueTitle,
  });

  factory ModelAppointment.fromJson(Map<String, dynamic> json) {
    return ModelAppointment(
      id: json['id'] as int,
      createdAt:
          json['createdAt'] == null
              ? null
              : DateTime.tryParse(json['createdAt'] as String),
      updatedAt:
          json['updatedAt'] == null
              ? null
              : DateTime.tryParse(json['updatedAt'] as String),
      canceledAt:
          json['canceledAt'] == null
              ? null
              : DateTime.tryParse(json['canceledAt'] as String),
      customerFolderId: json['customerFolderId'] as int?,
      typeEvent: json['typeEvent'] as String?,
      status: json['status'] as String? ?? 'unknown',
      serviceId: json['serviceId'] as int?,
      serviceDuration: json['serviceDuration'] as int?,
      expectedAppointmentStartTime: DateTime.parse(
        json['expectedAppointmentStartTime'] as String,
      ),
      expectedAppointmentEndTime: DateTime.parse(
        json['expectedAppointmentEndTime'] as String,
      ),
      realAppointmentStartTime:
          json['realAppointmentStartTime'] == null
              ? null
              : DateTime.tryParse(json['realAppointmentStartTime'] as String),
      realAppointmentEndTime:
          json['realAppointmentEndTime'] == null
              ? null
              : DateTime.tryParse(json['realAppointmentEndTime'] as String),
      notes: json['notes'] as String?,
      openingHoursId: json['openingHoursId'] as int?,
      queueId: json['queueId'] as int?,
      realTimeStatus: json['realTimeStatus'] as String?,
      slots: json['slots'] as int?,
      isOverflowed: json['isOverflowed'] as bool?,
      overflowReason: json['overflowReason'] as String?,
      overflowProcessingStatus: json['overflowProcessingStatus'] as String?,
      overflowDetectedAt:
          json['overflowDetectedAt'] == null
              ? null
              : DateTime.tryParse(json['overflowDetectedAt'] as String),
      overflowProcessedAt:
          json['overflowProcessedAt'] == null
              ? null
              : DateTime.tryParse(json['overflowProcessedAt'] as String),
      service: ModelAppointmentService.fromJson(
        json['service'] as Map<String, dynamic>? ?? {},
      ),
      place: ModelPlace.fromJson(json['place'] as Map<String, dynamic>? ?? {}),
      customerFolder: ModelAppointmentCustomerFolder.fromJson(
        json['customerFolder'] as Map<String, dynamic>? ?? {},
      ),
      queueTitle: (json['queue'] as Map<String, dynamic>?)?['title'] as String?,
    );
  }

  ModelAppointment copyWith({
    int? id,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? canceledAt,
    int? customerFolderId,
    String? typeEvent,
    String? status,
    int? serviceId,
    int? serviceDuration,
    DateTime? expectedAppointmentStartTime,
    DateTime? expectedAppointmentEndTime,
    DateTime? realAppointmentStartTime,
    DateTime? realAppointmentEndTime,
    String? notes,
    int? openingHoursId,
    int? queueId,
    String? realTimeStatus,
    int? slots,
    bool? isOverflowed,
    String? overflowReason,
    String? overflowProcessingStatus,
    DateTime? overflowDetectedAt,
    DateTime? overflowProcessedAt,
    ModelAppointmentService? service,
    ModelPlace? place,
    ModelAppointmentCustomerFolder? customerFolder,
    String? queueTitle,
  }) {
    return ModelAppointment(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      canceledAt: canceledAt ?? this.canceledAt,
      customerFolderId: customerFolderId ?? this.customerFolderId,
      typeEvent: typeEvent ?? this.typeEvent,
      status: status ?? this.status,
      serviceId: serviceId ?? this.serviceId,
      serviceDuration: serviceDuration ?? this.serviceDuration,
      expectedAppointmentStartTime:
          expectedAppointmentStartTime ?? this.expectedAppointmentStartTime,
      expectedAppointmentEndTime:
          expectedAppointmentEndTime ?? this.expectedAppointmentEndTime,
      realAppointmentStartTime:
          realAppointmentStartTime ?? this.realAppointmentStartTime,
      realAppointmentEndTime:
          realAppointmentEndTime ?? this.realAppointmentEndTime,
      notes: notes ?? this.notes,
      openingHoursId: openingHoursId ?? this.openingHoursId,
      queueId: queueId ?? this.queueId,
      realTimeStatus: realTimeStatus ?? this.realTimeStatus,
      slots: slots ?? this.slots,
      isOverflowed: isOverflowed ?? this.isOverflowed,
      overflowReason: overflowReason ?? this.overflowReason,
      overflowProcessingStatus:
          overflowProcessingStatus ?? this.overflowProcessingStatus,
      overflowDetectedAt: overflowDetectedAt ?? this.overflowDetectedAt,
      overflowProcessedAt: overflowProcessedAt ?? this.overflowProcessedAt,
      service: service ?? this.service,
      place: place ?? this.place,
      customerFolder: customerFolder ?? this.customerFolder,
      queueTitle: queueTitle ?? this.queueTitle,
    );
  }
}
