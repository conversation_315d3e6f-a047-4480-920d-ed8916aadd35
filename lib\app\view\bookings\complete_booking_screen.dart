import 'dart:convert';

import 'package:dalti/app/models/model_appointment.dart';
import 'package:dalti/app/models/model_category.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:flutter/material.dart';
import 'package:http/http.dart' as http;
import 'package:intl/intl.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../../base/color_data.dart';
import '../../../base/constant.dart';
import '../../../base/widget_utils.dart';
import '../../routes/app_routes.dart';

const String prefsKeySessionId = 'session_id';

class CompleteBookingScreen extends StatefulWidget {
  const CompleteBookingScreen({Key? key}) : super(key: key);

  @override
  State<CompleteBookingScreen> createState() => _CompleteBookingScreenState();
}

class _CompleteBookingScreenState extends State<CompleteBookingScreen> {
  List<ModelAppointment> _appointments = [];
  List<ModelAppointment> _completedAppointments = [];
  bool _isLoading = true;
  String? _error;

  final DateFormat _dateFormat = DateFormat('dd MMMM, yyyy, hh:mm a');

  @override
  void initState() {
    super.initState();
    _fetchAppointments();
  }

  Future<void> _fetchAppointments() async {
    if (!mounted) return;
    setState(() {
      _isLoading = true;
      _error = null;
    });

    final prefs = await SharedPreferences.getInstance();
    final sessionId = prefs.getString(prefsKeySessionId);

    if (sessionId == null || sessionId.isEmpty) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _error = "Please log in to view your bookings.";
        });
      }
      return;
    }

    const String apiUrl =
        "https://dapi-test.adscloud.org:8443/api/auth/customer/appointments";

    try {
      final response = await http.get(
        Uri.parse(apiUrl),
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': 'Bearer $sessionId',
        },
      );

      if (!mounted) return;

      if (response.statusCode == 200) {
        final List<dynamic> responseData = jsonDecode(response.body);
        _appointments =
            responseData
                .map(
                  (data) =>
                      ModelAppointment.fromJson(data as Map<String, dynamic>),
                )
                .toList();
        _filterCompletedAppointments();
        setState(() {
          _isLoading = false;
        });
      } else {
        String errorMessage = "Failed to fetch appointments.";
        try {
          final errorData = jsonDecode(response.body);
          errorMessage = errorData['message'] as String? ?? errorMessage;
        } catch (_) {}
        setState(() {
          _isLoading = false;
          _error = errorMessage;
        });
      }
    } catch (e) {
      if (!mounted) return;
      setState(() {
        _isLoading = false;
        _error = "An error occurred: $e";
      });
    }
  }

  void _filterCompletedAppointments() {
    _completedAppointments =
        _appointments.where((appointment) {
          String status = appointment.status.toLowerCase();
          return status == 'completed';
        }).toList();
  }

  Color _getStatusColor(String? status) {
    status = status?.toLowerCase();
    if (status == 'completed') {
      return Color(0xFF22C55E).withOpacity(0.2); // Vibrant green with increased opacity
    }
    return Colors.grey.shade200;
  }

  String _getStatusText(String? status) {
    status = status?.toLowerCase();
    if (status == 'completed') {
      return "Completed";
    }
    return status?.capitalize() ?? "Unknown";
  }

  Color _getStatusTextColor(String? status) {
    status = status?.toLowerCase();
    if (status == 'completed') {
      return Color(0xFF15803D); // Darker green for better contrast
    }
    return Colors.grey.shade700;
  }

  @override
  Widget build(BuildContext context) {
    FetchPixels(context);
    if (_isLoading) {
      return Center(child: CircularProgressIndicator(color: themedBlueColor));
    }

    if (_error != null) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: SingleChildScrollView(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  _error!,
                  style: TextStyle(color: Colors.red, fontSize: 16),
                  textAlign: TextAlign.center,
                ),
                getVerSpace(FetchPixels.getPixelHeight(20)),
                ElevatedButton(
                  onPressed: _fetchAppointments,
                  child: Text("Retry"),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: themedBlueColor,
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return Container(
      color: themedBackgroundColor,
      child:
          _completedAppointments.isEmpty
              ? _buildEmptyBookingsView(context)
              : _buildCompletedAppointmentListView(),
    );
  }

  ListView _buildCompletedAppointmentListView() {
    return ListView.builder(
      physics: const BouncingScrollPhysics(),
      padding: EdgeInsets.symmetric(
        horizontal: FetchPixels.getPixelWidth(20),
        vertical: FetchPixels.getPixelHeight(10),
      ),
      itemCount: _completedAppointments.length,
      itemBuilder: (context, index) {
        ModelAppointment appointment = _completedAppointments[index];
        final statusColor = _getStatusColor(appointment.status);
        final statusTextColor = _getStatusTextColor(appointment.status);
        
        return GestureDetector(
          onTap: () {
            Constant.sendToNext(
              context,
              Routes.bookingRoute,
              arguments: appointment,
            );
          },
          child: Container(
            margin: EdgeInsets.only(bottom: FetchPixels.getPixelHeight(20)),
            decoration: BoxDecoration(
              color: appCardBackground,
              borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
              border: Border.all(color: Colors.grey.shade100),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.05),
                  blurRadius: 10,
                  offset: Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              children: [
                // Status indicator at the top
                Container(
                  width: double.infinity,
                  padding: EdgeInsets.symmetric(
                    vertical: FetchPixels.getPixelHeight(8),
                    horizontal: FetchPixels.getPixelWidth(16),
                  ),
                  decoration: BoxDecoration(
                    color: statusColor.withOpacity(0.1),
                    borderRadius: BorderRadius.only(
                      topLeft: Radius.circular(FetchPixels.getPixelHeight(12)),
                      topRight: Radius.circular(FetchPixels.getPixelHeight(12)),
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        width: 8,
                        height: 8,
                        decoration: BoxDecoration(
                          color: statusTextColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                      getHorSpace(FetchPixels.getPixelWidth(8)),
                      Text(
                        _getStatusText(appointment.status),
                        style: TextStyle(
                          color: statusTextColor,
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                // Main content
                Padding(
                  padding: EdgeInsets.all(FetchPixels.getPixelHeight(16)),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Service image or placeholder
                          Container(
                            height: FetchPixels.getPixelHeight(80),
                            width: FetchPixels.getPixelHeight(80),
                            decoration: BoxDecoration(
                              color: statusColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(
                                FetchPixels.getPixelHeight(10),
                              ),
                            ),
                            child: Icon(
                              Icons.category_outlined,
                              size: 32,
                              color: statusTextColor,
                            ),
                          ),
                          getHorSpace(FetchPixels.getPixelWidth(16)),
                          // Service details
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                getCustomFont(
                                  appointment.customerFolder.provider.category.title,
                                  16,
                                  appDarkText,
                                  1,
                                  fontWeight: FontWeight.w700,
                                ),
                                getVerSpace(FetchPixels.getPixelHeight(4)),
                                getCustomFont(
                                  appointment.service.title,
                                  14,
                                  appMediumText,
                                  2,
                                  fontWeight: FontWeight.w500,
                                ),
                                getVerSpace(FetchPixels.getPixelHeight(8)),
                                Row(
                                  children: [
                                    Icon(
                                      Icons.access_time_rounded,
                                      size: 16,
                                      color: appMediumText,
                                    ),
                                    getHorSpace(FetchPixels.getPixelWidth(6)),
                                    Expanded(
                                      child: getCustomFont(
                                        _dateFormat.format(
                                          appointment.expectedAppointmentStartTime.toLocal(),
                                        ),
                                        13,
                                        appMediumText,
                                        1,
                                        fontWeight: FontWeight.w400,
                                      ),
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      getVerSpace(FetchPixels.getPixelHeight(16)),
                      // Provider info and actions
                      Row(
                        children: [
                          Expanded(
                            child: Row(
                              children: [
                                Icon(
                                  Icons.person_outline_rounded,
                                  size: 16,
                                  color: appMediumText,
                                ),
                                getHorSpace(FetchPixels.getPixelWidth(6)),
                                Expanded(
                                  child: getCustomFont(
                                    "By ${appointment.customerFolder.provider.title}",
                                    13,
                                    appMediumText,
                                    1,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyBookingsView(BuildContext context) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        getSvgImage(
          "clipboard.svg",
          height: FetchPixels.getPixelHeight(124),
          width: FetchPixels.getPixelHeight(124),
        ),
        getVerSpace(FetchPixels.getPixelHeight(40)),
        getCustomFont(
          "No Completed Bookings!",
          20,
          Colors.black,
          1,
          fontWeight: FontWeight.w800,
        ),
        getVerSpace(FetchPixels.getPixelHeight(10)),
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: FetchPixels.getPixelWidth(40),
          ),
          child: getCustomFont(
            "You have no past completed appointments.",
            15,
            appMediumText,
            2,
            fontWeight: FontWeight.w400,
            textAlign: TextAlign.center,
          ),
        ),
        getVerSpace(FetchPixels.getPixelHeight(30)),
        Padding(
          padding: EdgeInsets.symmetric(
            horizontal: FetchPixels.getPixelWidth(60),
          ),
          child: getButton(
            context,
            themedBlueColor,
            "Explore Services",
            Colors.white,
            () async {
              final selectedCategory = await Navigator.pushNamed(
                context,
                Routes.categoryRoute,
              );
              if (selectedCategory is ModelCategory) {
                Constant.sendToNext(
                  context,
                  Routes.searchRoute,
                  arguments: {'category': selectedCategory},
                );
              }
            },
            16,
            weight: FontWeight.w600,
            buttonHeight: FetchPixels.getPixelHeight(50),
            borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(25)),
            insetsGeometrypadding: EdgeInsets.symmetric(
              vertical: FetchPixels.getPixelHeight(10),
              horizontal: FetchPixels.getPixelWidth(20),
            ),
          ),
        ),
      ],
    );
  }
}

extension StringExtension on String {
  String capitalize() {
    if (isEmpty) return "";
    return "${this[0].toUpperCase()}${substring(1).toLowerCase()}";
  }
}
