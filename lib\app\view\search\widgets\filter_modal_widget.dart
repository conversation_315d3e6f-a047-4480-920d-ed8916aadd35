import 'package:dalti/app/models/model_category.dart';
import 'package:dalti/app/view/home/<USER>';
import 'package:dalti/base/color_data.dart';
import 'package:dalti/base/constant.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:dalti/base/widget_utils.dart';
import 'package:flutter/material.dart';
import 'package:dalti/app/utils/category_utils.dart';
import 'package:dalti/l10n/app_localizations.dart';

// Helper class to pass filter results from the modal
class FilterResult {
  final int? categoryId;
  final String? categoryName;
  final String? city;

  FilterResult({this.categoryId, this.categoryName, this.city});
}

// Define FilterModalWidget as a separate StatefulWidget
class FilterModalWidget extends StatefulWidget {
  final int? initialCategoryId;
  final String? initialCategoryName;
  final String? initialCity;

  const FilterModalWidget({
    Key? key,
    this.initialCategoryId,
    this.initialCategoryName,
    this.initialCity,
  }) : super(key: key);

  @override
  State<FilterModalWidget> createState() => _FilterModalWidgetState();
}

class _FilterModalWidgetState extends State<FilterModalWidget> {
  int? _tempSelectedCategoryId;
  String? _tempSelectedCategoryName;
  TextEditingController _cityController = TextEditingController();

  bool _isLoadingCategoriesModal = false;
  List<ModelCategory> _allCategoriesModal = [];
  List<ModelCategory> _topLevelCategoriesModal = [];
  ModelCategory? _currentParentCategoryModal;
  bool _showCategorySelectionInModal = false;

  @override
  void initState() {
    super.initState();
    _tempSelectedCategoryId = widget.initialCategoryId;
    _tempSelectedCategoryName = widget.initialCategoryName;
    _cityController.text = widget.initialCity ?? "";
  }

  Future<void> _fetchCategoriesForModal() async {
    final localizations = AppLocalizations.of(context)!;
    if (_allCategoriesModal.isNotEmpty) return;

    setState(() {
      _isLoadingCategoriesModal = true;
    });
    try {
      final fetchedCategories = await fetchCategories();
      _allCategoriesModal = fetchedCategories;
      _topLevelCategoriesModal = ModelCategory.buildHierarchy(
        _allCategoriesModal,
      );
    } catch (e) {
      print("Error fetching categories in modal: $e");
      if (mounted) {
        showCustomSnackBar(
          context,
          localizations.errorFetchingCategories(e.toString()),
          CustomSnackBarType.error,
          backgroundColor: daltiErrorRed,
          textColor: daltiTextOnPrimary,
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoadingCategoriesModal = false;
        });
      }
    }
  }

  void _handleCategoryTapInModal(ModelCategory category) {
    setState(() {
      if (_currentParentCategoryModal == null) {
        if (category.children.isNotEmpty) {
          _currentParentCategoryModal = category;
        } else {
          _tempSelectedCategoryId = category.id;
          _tempSelectedCategoryName = category.title;
          _showCategorySelectionInModal = false;
          _currentParentCategoryModal = null;
        }
      } else {
        _tempSelectedCategoryId = category.id;
        _tempSelectedCategoryName =
            "${_currentParentCategoryModal!.title} > ${category.title}";
        _showCategorySelectionInModal = false;
        _currentParentCategoryModal = null;
      }
    });
  }

  void _toggleCategorySelectionView() {
    setState(() {
      _showCategorySelectionInModal = !_showCategorySelectionInModal;
      if (_showCategorySelectionInModal && _allCategoriesModal.isEmpty) {
        _fetchCategoriesForModal();
      }
      if (_showCategorySelectionInModal) {
        _currentParentCategoryModal = null;
      }
    });
  }

  void _goBackInCategorySelection() {
    setState(() {
      if (_currentParentCategoryModal != null) {
        _currentParentCategoryModal = null;
      } else {
        _showCategorySelectionInModal = false;
      }
    });
  }

  @override
  void dispose() {
    _cityController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final localizations = AppLocalizations.of(context)!;
    return Container(
      color: daltiBackground,
      padding: EdgeInsets.only(
        left: FetchPixels.getPixelWidth(20),
        right: FetchPixels.getPixelWidth(20),
        top: FetchPixels.getPixelHeight(20),
        bottom:
            MediaQuery.of(context).viewInsets.bottom +
            FetchPixels.getPixelHeight(20),
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Padding(
              padding: EdgeInsets.only(bottom: FetchPixels.getPixelHeight(24)),
              child: Center(
                child: getCustomFont(
                  localizations.filtersModalTitle,
                  22,
                  daltiTextHeadline,
                  1,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            getVerSpace(FetchPixels.getPixelHeight(10)),

            _buildSectionTitle(
              localizations.filtersCategorySectionTitle,
              localizations,
            ),
            getVerSpace(FetchPixels.getPixelHeight(10)),
            InkWell(
              onTap: _toggleCategorySelectionView,
              borderRadius: BorderRadius.circular(
                FetchPixels.getPixelHeight(12),
              ),
              child: Container(
                padding: EdgeInsets.symmetric(
                  horizontal: FetchPixels.getPixelWidth(16),
                  vertical: FetchPixels.getPixelHeight(16),
                ),
                decoration: BoxDecoration(
                  color: daltiCard,
                  border: Border.all(color: daltiDividerLine),
                  borderRadius: BorderRadius.circular(
                    FetchPixels.getPixelHeight(12),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        _tempSelectedCategoryName ??
                            localizations.filtersSelectCategoryPlaceholder,
                        style: TextStyle(
                          fontSize: FetchPixels.getPixelHeight(16),
                          color:
                              _tempSelectedCategoryName == null
                                  ? daltiTextMuted
                                  : daltiTextBody,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    Icon(
                      _showCategorySelectionInModal
                          ? Icons.keyboard_arrow_up_rounded
                          : Icons.keyboard_arrow_down_rounded,
                      color: daltiIconDefault,
                      size: FetchPixels.getPixelHeight(28),
                    ),
                  ],
                ),
              ),
            ),
            if (_showCategorySelectionInModal)
              _buildCategorySelectionView(localizations),
            getVerSpace(FetchPixels.getPixelHeight(24)),

            _buildSectionTitle(
              localizations.filtersCitySectionTitle,
              localizations,
            ),
            getVerSpace(FetchPixels.getPixelHeight(10)),
            TextFormField(
              controller: _cityController,
              style: TextStyle(
                fontSize: FetchPixels.getPixelHeight(16),
                color: daltiTextBody,
              ),
              decoration: InputDecoration(
                hintText: localizations.filtersEnterCityHint,
                hintStyle: TextStyle(color: daltiTextMuted),
                filled: true,
                fillColor: daltiCard,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    FetchPixels.getPixelHeight(12),
                  ),
                  borderSide: BorderSide.none,
                ),
                enabledBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    FetchPixels.getPixelHeight(12),
                  ),
                  borderSide: BorderSide(color: daltiDividerLine),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(
                    FetchPixels.getPixelHeight(12),
                  ),
                  borderSide: BorderSide(color: daltiPrimary, width: 1.5),
                ),
                contentPadding: EdgeInsets.symmetric(
                  horizontal: FetchPixels.getPixelWidth(16),
                  vertical: FetchPixels.getPixelHeight(16),
                ),
              ),
            ),
            getVerSpace(FetchPixels.getPixelHeight(30)),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () {
                      setState(() {
                        _tempSelectedCategoryId = null;
                        _tempSelectedCategoryName = null;
                        _cityController.clear();
                        _currentParentCategoryModal = null;
                        _showCategorySelectionInModal = false;
                      });
                    },
                    style: OutlinedButton.styleFrom(
                      foregroundColor: daltiPrimary,
                      side: BorderSide(color: daltiPrimary, width: 1.5),
                      padding: EdgeInsets.symmetric(
                        vertical: FetchPixels.getPixelHeight(15),
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          FetchPixels.getPixelHeight(10),
                        ),
                      ),
                    ),
                    child: Text(
                      localizations.filtersClearButton,
                      style: TextStyle(
                        fontSize: FetchPixels.getPixelHeight(16),
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
                getHorSpace(FetchPixels.getPixelWidth(16)),
                Expanded(
                  child: ElevatedButton(
                    onPressed: () {
                      Navigator.pop(
                        context,
                        FilterResult(
                          categoryId: _tempSelectedCategoryId,
                          categoryName: _tempSelectedCategoryName,
                          city:
                              _cityController.text.trim().isNotEmpty
                                  ? _cityController.text.trim()
                                  : null,
                        ),
                      );
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: daltiPrimary,
                      padding: EdgeInsets.symmetric(
                        vertical: FetchPixels.getPixelHeight(15),
                      ),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(
                          FetchPixels.getPixelHeight(10),
                        ),
                      ),
                      elevation: 2,
                    ),
                    child: Text(
                      localizations.filtersApplyButton,
                      style: TextStyle(
                        fontSize: FetchPixels.getPixelHeight(16),
                        color: daltiTextOnPrimary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title, AppLocalizations localizations) {
    return getCustomFont(
      title,
      17,
      daltiTextHeadline,
      1,
      fontWeight: FontWeight.w600,
    );
  }

  Widget _buildCategorySelectionView(AppLocalizations localizations) {
    if (_isLoadingCategoriesModal) {
      return Center(
        child: Padding(
          padding: EdgeInsets.symmetric(
            vertical: FetchPixels.getPixelHeight(20),
          ),
          child: CircularProgressIndicator(color: daltiPrimary),
        ),
      );
    }

    List<ModelCategory> categoriesToShow;
    String currentTitleText;

    if (_currentParentCategoryModal != null) {
      categoriesToShow = _currentParentCategoryModal!.children;
      currentTitleText = _currentParentCategoryModal!.title;
    } else {
      categoriesToShow = _topLevelCategoriesModal;
      currentTitleText = localizations.filtersAllCategoriesTitle;
    }

    return Container(
      margin: EdgeInsets.only(top: FetchPixels.getPixelHeight(10)),
      decoration: BoxDecoration(
        color: daltiCard,
        borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
        border: Border.all(color: daltiDividerLine.withOpacity(0.7)),
      ),
      constraints: BoxConstraints(maxHeight: FetchPixels.getPixelHeight(300)),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(
              horizontal: FetchPixels.getPixelWidth(12),
              vertical: FetchPixels.getPixelHeight(10),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                if (_currentParentCategoryModal != null)
                  IconButton(
                    icon: Icon(
                      Icons.arrow_back_ios_new_rounded,
                      color: daltiIconDefault,
                      size: 20,
                    ),
                    onPressed: _goBackInCategorySelection,
                    padding: EdgeInsets.zero,
                    constraints: BoxConstraints(),
                  )
                else
                  SizedBox(width: FetchPixels.getPixelWidth(40)),

                Expanded(
                  child: Text(
                    currentTitleText,
                    textAlign: TextAlign.center,
                    style: TextStyle(
                      fontSize: FetchPixels.getPixelHeight(16),
                      fontWeight: FontWeight.w600,
                      color: daltiTextHeadline,
                    ),
                  ),
                ),
                IconButton(
                  icon: Icon(
                    Icons.close_rounded,
                    color: daltiIconDefault,
                    size: 22,
                  ),
                  onPressed: () {
                    setState(() {
                      _showCategorySelectionInModal = false;
                      _currentParentCategoryModal = null;
                    });
                  },
                  padding: EdgeInsets.zero,
                  constraints: BoxConstraints(),
                ),
              ],
            ),
          ),
          Divider(color: daltiDividerLine, height: 1),
          if (categoriesToShow.isEmpty && _currentParentCategoryModal != null)
            Padding(
              padding: EdgeInsets.all(FetchPixels.getPixelHeight(20)),
              child: getCustomFont(
                localizations.filtersNoSubcategories,
                14,
                daltiTextMuted,
                1,
                textAlign: TextAlign.center,
              ),
            )
          else if (categoriesToShow.isEmpty)
            Padding(
              padding: EdgeInsets.all(FetchPixels.getPixelHeight(20)),
              child: getCustomFont(
                localizations.filtersNoCategoriesAvailable,
                14,
                daltiTextMuted,
                1,
                textAlign: TextAlign.center,
              ),
            )
          else
            Flexible(
              child: ListView.separated(
                shrinkWrap: true,
                itemCount: categoriesToShow.length,
                itemBuilder: (context, index) {
                  final category = categoriesToShow[index];
                  bool isSelected =
                      _tempSelectedCategoryId == category.id &&
                      (_currentParentCategoryModal != null ||
                          category.children.isEmpty);

                  return ListTile(
                    dense: true,
                    tileColor:
                        isSelected ? daltiPrimaryLight.withOpacity(0.5) : null,
                    title: Text(
                      category.title,
                      style: TextStyle(
                        fontSize: FetchPixels.getPixelHeight(15),
                        color: isSelected ? daltiPrimary : daltiTextBody,
                        fontWeight:
                            isSelected ? FontWeight.w600 : FontWeight.normal,
                      ),
                    ),
                    trailing:
                        category.children.isNotEmpty &&
                                _currentParentCategoryModal == null
                            ? Icon(
                              Icons.keyboard_arrow_right_rounded,
                              color: daltiIconDefault,
                              size: 22,
                            )
                            : null,
                    onTap: () => _handleCategoryTapInModal(category),
                  );
                },
                separatorBuilder:
                    (context, index) => Divider(
                      color: daltiDividerLine.withOpacity(0.5),
                      height: 0.5,
                    ),
              ),
            ),
        ],
      ),
    );
  }
}
