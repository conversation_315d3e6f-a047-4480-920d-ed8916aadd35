# Firebase Push Notifications Setup Guide

This document provides a comprehensive step-by-step guide for setting up Firebase Cloud Messaging (FCM) push notifications in the DALTI Flutter application.

## Table of Contents
1. [Project Overview](#project-overview)
2. [Firebase Project Configuration](#firebase-project-configuration)
3. [Dependencies](#dependencies)
4. [Android Configuration](#android-configuration)
5. [iOS Configuration](#ios-configuration)
6. [Web Configuration](#web-configuration)
7. [Flutter Implementation](#flutter-implementation)
8. [Backend Integration](#backend-integration)
9. [Testing](#testing)
10. [Troubleshooting](#troubleshooting)

## Project Overview

**Firebase Project ID**: `dalti-prod`
**Package Name**: `org.adscloud.dalti.customer`
**Supported Platforms**: Android, iOS, Web

## Firebase Project Configuration

### 1. Firebase Console Setup
- Project created in Firebase Console with ID: `dalti-prod`
- Cloud Messaging enabled
- Project configured for multiple platforms (Android, iOS, Web)

### 2. Platform Registration
- **Android App**: `1:1060372851323:android:97fbdd30154b22130690de`
- **iOS App**: Not configured yet
- **Web App**: `1:1060372851323:web:customer_web_app_id` (needs to be configured in Firebase Console)

## Dependencies

### pubspec.yaml
```yaml
dependencies:
  firebase_core: ^2.32.0
  firebase_messaging: ^14.9.3
  flutter_local_notifications: ^17.2.1
  shared_preferences: any
  http: any
```

### Android Dependencies (android/app/build.gradle)
```gradle
dependencies {
  implementation(platform("com.google.firebase:firebase-bom:33.13.0"))
  implementation("com.google.firebase:firebase-analytics")
  implementation("com.google.firebase:firebase-messaging")
}
```

## Android Configuration

### 1. Google Services Configuration
**File**: `android/app/google-services.json`
- Downloaded from Firebase Console
- Contains project configuration and API keys
- Must be placed in `android/app/` directory

### 2. AndroidManifest.xml Configuration
**File**: `android/app/src/main/AndroidManifest.xml`

#### Required Permissions
```xml
<uses-permission android:name="android.permission.INTERNET"/>
<uses-permission android:name="android.permission.WAKE_LOCK"/>
```

#### Firebase Messaging Service
```xml
<service
    android:name="com.google.firebase.messaging.FirebaseMessagingService"
    android:exported="false">
    <intent-filter>
        <action android:name="com.google.firebase.MESSAGING_EVENT"/>
    </intent-filter>
</service>
```

#### Notification Configuration
```xml
<!-- Default notification icon -->
<meta-data
    android:name="com.google.firebase.messaging.default_notification_icon"
    android:resource="@mipmap/ic_launcher" />

<!-- Default notification color -->
<meta-data
    android:name="com.google.firebase.messaging.default_notification_color"
    android:resource="@android:color/holo_blue_light" />

<!-- Default notification channel -->
<meta-data
    android:name="com.google.firebase.messaging.default_notification_channel_id"
    android:value="default_channel_id"/>
```

### 3. Build Configuration
- `minSdk`: Uses Flutter's default
- `targetSdk`: Uses Flutter's default
- `multiDexEnabled`: true (required for Firebase)

## iOS Configuration

### 1. GoogleService-Info.plist
**File**: `ios/Runner/GoogleService-Info.plist`
- Downloaded from Firebase Console
- Contains iOS-specific configuration
- **Bundle ID**: `com.example.dalti2`

### 2. iOS Capabilities
- Push Notifications capability must be enabled in Xcode
- Background Modes capability for background processing

## Web Configuration

### 1. Firebase Configuration
**File**: `web/firebase-messaging-sw.js`
```javascript
const firebaseConfig = {
  apiKey: "AIzaSyDDcsbxchQIzUvlxyr_vddecMVPhxbd6Lg",
  authDomain: "dalti-prod.firebaseapp.com",
  projectId: "dalti-prod",
  storageBucket: "dalti-prod.firebasestorage.app",
  messagingSenderId: "1060372851323",
  appId: "1:1060372851323:web:customer_web_app_id"
};
```

### 2. Service Worker
- Handles background messages when web app is not in focus
- Displays notifications using Web Notifications API
- Located in `web/` directory for proper service worker registration

## Flutter Implementation

### 1. Firebase Initialization
**File**: `lib/main.dart`
```dart
Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  runApp(const MyApp());
}
```

### 2. Firebase Options Configuration
**File**: `lib/firebase_options.dart`
- Auto-generated by FlutterFire CLI
- Contains platform-specific Firebase configurations
- Supports Android and Web platforms

### 3. FCM Service Implementation
**File**: `lib/services/fcm_service.dart`

#### Key Features:
- **Permission Handling**: Requests notification permissions
- **Token Management**: Retrieves and stores FCM tokens locally
- **Server Integration**: Sends tokens to backend API
- **Foreground Notifications**: Displays notifications when app is active
- **Message Handling**: Processes different notification states

#### Core Methods:
```dart
class FcmService {
  Future<void> init() async {
    await _requestPermissions();
    await _getAndRegisterFcmToken();
    _initForegroundNotifications();
    _setupMessageHandlers();
    _createAndroidNotificationChannel();
  }
}
```

### 4. Notification Channel Setup
- **Channel ID**: `default_channel_id`
- **Importance**: High
- **Platform**: Android 8.0+ (API 26+)

### 5. Message Handlers
- **Foreground**: Custom notification display using flutter_local_notifications
- **Background**: Automatic system notifications
- **Terminated**: Handled via getInitialMessage (commented out)

## Backend Integration

### API Endpoint
**URL**: `https://dapi-test.adscloud.org:8443/api/auth/notifications/mobile/save-fcm-token`

### Request Format
```json
{
  "token": "FCM_TOKEN_STRING",
  "deviceType": "android|ios|web"
}
```

### Authentication
- Bearer token authentication using session ID
- Session ID stored in SharedPreferences with key: `session_id`

### Token Storage
- FCM tokens stored locally with key: `fcm_token`
- Prevents unnecessary token regeneration
- Automatic re-registration on app updates

## Testing

### 1. Token Generation Testing
- Check debug console for FCM token logs
- Verify token is sent to backend successfully
- Test token persistence across app restarts

### 2. Notification Testing
- **Foreground**: App active, custom notification display
- **Background**: App in background, system notification
- **Terminated**: App closed, system notification with app launch

### 3. Platform Testing
- Test on Android devices (API 26+ for channels)
- Test on iOS devices (with proper certificates)
- Test on web browsers (Chrome, Firefox, Safari)

## Troubleshooting

### Common Issues

1. **No FCM Token Generated**
   - Check internet connectivity
   - Verify Firebase project configuration
   - Ensure google-services.json/GoogleService-Info.plist are correct

2. **Notifications Not Appearing**
   - Check notification permissions
   - Verify notification channel creation (Android)
   - Test with Firebase Console test messages

3. **Background Notifications Not Working**
   - Ensure service worker is properly registered (Web)
   - Check background app refresh settings (iOS)
   - Verify FirebaseMessagingService in AndroidManifest (Android)

4. **Token Not Sent to Server**
   - Check session ID availability
   - Verify API endpoint accessibility
   - Check network connectivity and authentication

### Debug Steps
1. Enable debug mode and check console logs
2. Test with Firebase Console's "Test on device" feature
3. Verify platform-specific configurations
4. Check notification settings on device
5. Test API endpoints independently

## Future Enhancements

### Planned Features (Currently TODO)
- Notification tap handling for navigation
- Background message processing
- iOS foreground notification customization
- Notification action buttons
- Rich media notifications

### Code Improvements Needed
- Implement `onNotificationTapped` callback
- Add `_firebaseMessagingBackgroundHandler` for data-only messages
- Handle app termination notification taps
- Add retry logic for failed token uploads
- Implement notification categories and actions
