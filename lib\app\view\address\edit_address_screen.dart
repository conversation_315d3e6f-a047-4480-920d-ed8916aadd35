import 'package:dalti/app/utils/address_constants.dart';
import 'package:dalti/base/resizer/fetch_pixels.dart';
import 'package:flutter/material.dart';

import '../../../base/color_data.dart';
import '../../../base/constant.dart';
import '../../../base/widget_utils.dart';
import '../../models/model_address.dart';
import '../../../../services/address_service.dart';
// Import for location services (to be added if implementing actual location fetching)
// import 'package:geolocator/geolocator.dart';

class EditAddressScreen extends StatefulWidget {
  final ModelAddress? address;

  const EditAddressScreen({Key? key, this.address}) : super(key: key);

  @override
  State<EditAddressScreen> createState() => _EditAddressScreenState();
}

class _EditAddressScreenState extends State<EditAddressScreen> {
  final _formKey = GlobalKey<FormState>();
  // TextEditingController nameController = TextEditingController(); // Removed
  TextEditingController streetController = TextEditingController();
  // TextEditingController cityController = TextEditingController(); // Removed
  TextEditingController pincodeController = TextEditingController();
  // TextEditingController countryController = TextEditingController(); // Removed
  // TextEditingController phoneController = TextEditingController(); // Removed
  // TextEditingController landmarkController = TextEditingController(); // Removed
  // TextEditingController latitudeController = TextEditingController(); // Removed
  // TextEditingController longitudeController = TextEditingController(); // Removed
  bool isPrimaryController = false;

  String? _selectedCountry;
  String? _selectedCity;
  List<String> _availableCities = [];

  ModelAddress? _internalAddressData;
  bool _isEditMode = false;
  bool _isLoading = false;
  final AddressService _addressService = AddressService();

  // State variables for fetched location (to be used later)
  double? _currentLatitude;
  double? _currentLongitude;
  bool _isInitialized = false;

  @override
  void initState() {
    super.initState();
    print(
      "EditAddressScreen initState: widget.address is ${widget.address == null ? 'null' : 'not null'}",
    );
    if (widget.address != null) {
      _initializeForm(widget.address);
    } else {
      // If navigating to add a new address, widget.address will be null.
      // Call _initializeForm with null to set up for a new address.
      _initializeForm(null);
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    print(
      "EditAddressScreen didChangeDependencies: _isInitialized = $_isInitialized, widget.address is ${widget.address == null ? 'null' : 'not null'}",
    );
    // This logic is now more of a fallback if initState didn't cover it,
    // or if dependencies change in a way that requires re-evaluating route args (less common for this screen's setup)
    if (!_isInitialized && widget.address == null) {
      final routeArgs = ModalRoute.of(context)?.settings.arguments;
      print(
        "EditAddressScreen didChangeDependencies: routeArgs type is ${routeArgs?.runtimeType}, value is $routeArgs",
      );
      if (routeArgs is ModelAddress) {
        _initializeForm(routeArgs);
      } else if (routeArgs != null) {
        print(
          "EditAddressScreen didChangeDependencies: routeArgs is not null BUT not ModelAddress. Type: ${routeArgs.runtimeType}",
        );
        _initializeForm(null);
      } else {
        // If still not initialized and no constructor/route args, ensure it's set for new.
        // This path might be redundant if initState always calls _initializeForm(null) when widget.address is null.
        if (!_isInitialized) _initializeForm(null);
      }
    }
  }

  void _initializeForm(ModelAddress? addressData) {
    print(
      "EditAddressScreen _initializeForm: called with addressData ${addressData == null ? 'null' : 'not null'}",
    );
    if (addressData == null) {
      _isEditMode = false;
      _selectedCountry = "Algeria"; // Default for new address
      // Directly populate _availableCities for the default country
      _availableCities = _getCitiesForCountry(_selectedCountry);
      _selectedCity = null;
      isPrimaryController = false;
      streetController.text = '';
      pincodeController.text = '';
      _currentLatitude = null;
      _currentLongitude = null;
    } else {
      _internalAddressData = addressData;
      _isEditMode = true;
      streetController.text =
          _internalAddressData!.street ?? (_internalAddressData!.address ?? '');
      pincodeController.text = _internalAddressData!.postalCode ?? '';
      isPrimaryController = _internalAddressData!.isPrimary ?? false;
      _currentLatitude = _internalAddressData!.latitude;
      _currentLongitude = _internalAddressData!.longitude;

      if (_internalAddressData!.country != null &&
          countries.any((c) => c['value'] == _internalAddressData!.country)) {
        _selectedCountry = _internalAddressData!.country;
        // Directly populate _availableCities based on the address's country
        _availableCities = _getCitiesForCountry(_selectedCountry);

        if (_internalAddressData!.city != null &&
            _availableCities.contains(_internalAddressData!.city)) {
          _selectedCity = _internalAddressData!.city;
        } else {
          _selectedCity = null;
          print(
            "EditAddressScreen _initializeForm: City '${_internalAddressData!.city}' from address data not found in or did not match available cities for country '${_selectedCountry}'. _availableCities: ${_availableCities.take(5).join(', ')}...",
          );
        }
      } else {
        _selectedCountry = null;
        _availableCities = [];
        _selectedCity = null;
        print(
          "EditAddressScreen _initializeForm: Country '${_internalAddressData!.country}' from address data is null or not found in predefined countries list.",
        );
      }
    }
    _isInitialized = true;
    print(
      "EditAddressScreen _initializeForm: _isEditMode set to $_isEditMode, _isInitialized set to $_isInitialized. _selectedCountry: $_selectedCountry, _selectedCity: $_selectedCity",
    );

    if (mounted) {
      setState(() {
        print(
          "EditAddressScreen _initializeForm: setState called. _isEditMode is $_isEditMode. City is $_selectedCity",
        );
      });
    }
  }

  List<String> _getCitiesForCountry(String? country) {
    List<String> cities = [];
    if (country == "Algeria") {
      cities = List.from(algerianWilayas);
    } else if (country == "France") {
      cities = List.from(frenchCities);
    } // Add other countries as needed
    cities.sort();
    return cities;
  }

  void _updateAvailableCities(String? country) {
    final newCities = _getCitiesForCountry(country);

    bool hasChanged =
        _availableCities.length != newCities.length ||
        !_availableCities.every((item) => newCities.contains(item));

    if (hasChanged) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (mounted) {
          setState(() {
            _availableCities = newCities;
            print(
              "EditAddressScreen _updateAvailableCities: setState called for cities. New count: ${newCities.length}",
            );
            if (_selectedCity != null &&
                !_availableCities.contains(_selectedCity)) {
              _selectedCity = null;
              print(
                "EditAddressScreen _updateAvailableCities: _selectedCity reset to null as it's not in the new list.",
              );
            }
          });
        }
      });
    }
  }

  @override
  void dispose() {
    // nameController.dispose(); // Removed
    streetController.dispose();
    // cityController.dispose(); // Removed
    pincodeController.dispose();
    // countryController.dispose(); // Removed
    // phoneController.dispose(); // Removed
    // landmarkController.dispose(); // Removed
    // latitudeController.dispose(); // Removed
    // longitudeController.dispose(); // Removed
    super.dispose();
  }

  // Placeholder for location fetching logic
  // Future<void> _getCurrentLocation() async {
  //   // ... (implementation to be added)
  //   // This would involve using a package like geolocator
  //   // For now, this is a placeholder.
  //   // Upon fetching, you would:
  //   // setState(() {
  //   //   _currentLatitude = fetchedLat;
  //   //   _currentLongitude = fetchedLong;
  //   // });
  //   print("Attempting to get current location (not implemented yet)");
  // }

  Future<void> _saveAddress() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }
    if (mounted) {
      setState(() {
        _isLoading = true;
      });
    }

    final double latitudeForApi =
        _currentLatitude ?? _internalAddressData?.latitude ?? 0.0;
    final double longitudeForApi =
        _currentLongitude ?? _internalAddressData?.longitude ?? 0.0;

    Map<String, dynamic> apiPayload = {
      "address": streetController.text,
      "city": _selectedCity,
      "postalCode": pincodeController.text,
      "country": _selectedCountry ?? "Algeria",
      "latitude": latitudeForApi,
      "longitude": longitudeForApi,
      "isPrimary": isPrimaryController,
    };

    String? originalDescription = _internalAddressData?.description;
    if (_isEditMode && originalDescription != null) {
      apiPayload["description"] = originalDescription;
    }

    ModelAddress comprehensiveAddressData = ModelAddress(
      street: streetController.text,
      address: streetController.text,
      city: _selectedCity,
      postalCode: pincodeController.text,
      country: _selectedCountry,
      latitude: latitudeForApi,
      longitude: longitudeForApi,
      isPrimary: isPrimaryController,
      description: _isEditMode ? _internalAddressData?.description : null,
      name: _isEditMode ? _internalAddressData?.name : null,
      phone: _isEditMode ? _internalAddressData?.phone : null,
    );

    try {
      if (_isEditMode) {
        String? addressId;
        if (_internalAddressData?.id != null) {
          addressId = _internalAddressData!.id.toString();
        } else if (_internalAddressData?.name != null &&
            _internalAddressData!.name!.isNotEmpty) {
          // Fallback to name if ID is null, assuming name was a temporary ID placeholder
          // This fallback might need to be removed if 'name' is not a valid ID source
          addressId = _internalAddressData!.name;
          print(
            "Warning: Using name as addressId fallback. Actual ID is preferred.",
          );
        }

        if (addressId == null || addressId.isEmpty) {
          throw Exception("Address ID is missing for update.");
        }
        await _addressService.updateCustomerAddress(
          addressId,
          comprehensiveAddressData,
        );
        if (mounted)
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Address updated successfully')),
          );
      } else {
        await _addressService.createCustomerAddress(comprehensiveAddressData);
        if (mounted)
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Address added successfully')),
          );
      }
      if (mounted) Navigator.of(context).pop(true);
    } catch (e) {
      if (mounted)
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to save address: ${e.toString()}')),
        );
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  // Helper to build TextFormField with consistent styling
  Widget _buildTextFormField({
    required TextEditingController controller,
    required String label,
    String? prefixIconPath, // For SVG icons
    TextInputType? keyboardType,
    String? Function(String?)? validator,
    bool isEnabled = true,
    int maxLines = 1,
    bool isDense = false,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 10,
            offset: Offset(0.0, 4.0),
          ),
        ],
        borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(12)),
      ),
      child: Padding(
        padding: EdgeInsets.symmetric(
          horizontal: FetchPixels.getPixelWidth(16),
          vertical: FetchPixels.getPixelHeight(maxLines > 1 ? 10 : 0),
        ),
        child: TextFormField(
          controller: controller,
          enabled: isEnabled,
          keyboardType: keyboardType,
          validator: validator,
          maxLines: maxLines,
          style: const TextStyle(
            color: Colors.black,
            fontWeight: FontWeight.w400,
            fontSize: 16,
            fontFamily: Constant.fontsFamily,
          ),
          decoration: InputDecoration(
            icon:
                prefixIconPath != null
                    ? Padding(
                      padding: EdgeInsets.only(
                        left: FetchPixels.getPixelWidth(2),
                        right: FetchPixels.getPixelWidth(12),
                      ),
                      child: getSvgImage(
                        prefixIconPath,
                        height: FetchPixels.getPixelHeight(24),
                        width: FetchPixels.getPixelHeight(24),
                      ),
                    )
                    : null,
            border: InputBorder.none,
            hintText: label,
            hintStyle: TextStyle(
              color: daltiTextMuted,
              fontWeight: FontWeight.w400,
              fontSize: 16,
              fontFamily: Constant.fontsFamily,
            ),
            isDense: isDense,
            contentPadding:
                isDense
                    ? EdgeInsets.symmetric(
                      vertical: FetchPixels.getPixelHeight(10),
                    )
                    : null,
          ),
        ),
      ),
    );
  }

  // New helper for Modal Bottom Sheet Select Field
  Widget _buildModalSelectField({
    required BuildContext context,
    required String label,
    required String? currentValue,
    required List<({String value, String display})> items,
    required Function(String?) onItemSelected,
    String? Function(String?)? validator,
    bool isEnabled = true,
  }) {
    return FormField<String>(
      key: ValueKey(
        label +
            (currentValue ?? '') +
            items.length.toString() +
            isEnabled.toString(),
      ), // Added isEnabled to key
      initialValue: currentValue,
      validator: validator,
      builder: (FormFieldState<String> fieldState) {
        return InkWell(
          onTap:
              isEnabled
                  ? () async {
                    final selectedValue = await showModalBottomSheet<String>(
                      context: context,
                      backgroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.vertical(
                          top: Radius.circular(FetchPixels.getPixelHeight(20)),
                        ),
                      ),
                      builder: (BuildContext bContext) {
                        return SafeArea(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: <Widget>[
                              Padding(
                                padding: EdgeInsets.all(
                                  FetchPixels.getPixelHeight(16),
                                ),
                                child: Text(
                                  label,
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.black,
                                  ),
                                ),
                              ),
                              Expanded(
                                child: ListView.builder(
                                  shrinkWrap: true,
                                  itemCount: items.length,
                                  itemBuilder: (
                                    BuildContext itemBuilderContext,
                                    int index,
                                  ) {
                                    return ListTile(
                                      title: Text(
                                        items[index].display,
                                        style: const TextStyle(
                                          color: Colors.black,
                                        ),
                                      ),
                                      onTap: () {
                                        Navigator.pop(
                                          bContext,
                                          items[index].value,
                                        );
                                      },
                                    );
                                  },
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    );
                    if (selectedValue != null) {
                      onItemSelected(selectedValue);
                      fieldState.didChange(selectedValue);
                    } else {
                      // Only validate if nothing was selected, to allow clearing a field if needed (though current setup doesn't clear)
                      // fieldState.validate(); // Commented out: validation should occur on form save or field change.
                    }
                  }
                  : null,
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: FetchPixels.getPixelWidth(16),
              vertical: FetchPixels.getPixelHeight(
                15,
              ), // Adjusted padding for tap target
            ),
            decoration: BoxDecoration(
              color:
                  isEnabled
                      ? Colors.white
                      : Colors.grey[200], // Visual cue for disabled
              boxShadow: const [
                BoxShadow(
                  color: Colors.black12,
                  blurRadius: 10,
                  offset: Offset(0.0, 4.0),
                ),
              ],
              borderRadius: BorderRadius.circular(
                FetchPixels.getPixelHeight(12),
              ),
              border:
                  fieldState.hasError
                      ? Border.all(
                        color: Theme.of(context).colorScheme.error,
                        width: 1.5,
                      )
                      : Border.all(color: Colors.transparent, width: 0),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                Text(
                  currentValue ??
                      label, // Show label as placeholder if no value
                  style: TextStyle(
                    color:
                        currentValue != null
                            ? (isEnabled ? Colors.black : Colors.grey[600])
                            : (isEnabled ? daltiTextMuted : Colors.grey[500]),
                    fontWeight: FontWeight.w400,
                    fontSize: 16,
                    fontFamily: Constant.fontsFamily,
                  ),
                ),
                Icon(
                  Icons.arrow_drop_down,
                  color: isEnabled ? daltiTextMuted : Colors.grey[400],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    print(
      "EditAddressScreen build: _isEditMode is $_isEditMode. _selectedCountry: $_selectedCountry, _selectedCity: $_selectedCity",
    );
    FetchPixels(context);
    return WillPopScope(
      child: Scaffold(
        resizeToAvoidBottomInset: true,
        backgroundColor: daltiBackground,
        bottomNavigationBar: _bottomSaveButton(context),
        body: SafeArea(
          child: Container(
            padding: EdgeInsets.symmetric(
              horizontal: FetchPixels.getPixelWidth(20),
            ),
            child: Column(
              children: [
                getVerSpace(FetchPixels.getPixelHeight(20)),
                gettoolbarMenu(
                  context,
                  "back.svg",
                  () {
                    Navigator.of(context).pop(false);
                  },
                  istext: true,
                  title: _isEditMode ? "Edit My Address" : "Add New Address",
                  weight: FontWeight.w800,
                  fontsize: 24,
                  textColor: daltiTextHeadline,
                ),
                getVerSpace(FetchPixels.getPixelHeight(30)),
                Expanded(
                  flex: 1,
                  child: Form(
                    key: _formKey,
                    child: ListView(
                      primary: true,
                      shrinkWrap: true,
                      physics: const BouncingScrollPhysics(),
                      children: [
                        // Name field removed
                        // getVerSpace(FetchPixels.getPixelHeight(20)), // Remove if Name was first
                        _buildTextFormField(
                          controller: streetController,
                          label: "Address Line",
                          validator:
                              (value) =>
                                  value == null || value.isEmpty
                                      ? 'Please enter address line'
                                      : null,
                          isEnabled: true,
                          maxLines:
                              2, // Example for slightly taller field for address
                          isDense: true,
                        ),
                        getVerSpace(FetchPixels.getPixelHeight(20)),
                        // Country Select
                        _buildModalSelectField(
                          context: context,
                          label: "Country",
                          currentValue:
                              _selectedCountry != null
                                  ? countries.firstWhere(
                                    (c) => c['value'] == _selectedCountry,
                                    orElse:
                                        () => {
                                          // Provide a default Map structure for orElse
                                          'value': _selectedCountry!,
                                          'label':
                                              _selectedCountry!, // Display the value if not found in labels
                                        },
                                  )['label']
                                  : "Select Country",
                          items:
                              countries
                                  .map(
                                    (c) => (
                                      value: c['value']!,
                                      display: c['label']!,
                                    ),
                                  )
                                  .toList(),
                          onItemSelected: (newValue) {
                            // setState is crucial here to update _selectedCountry and rebuild dependent UI
                            setState(() {
                              _selectedCountry = newValue;
                              _selectedCity =
                                  null; // Reset city when country changes
                              _updateAvailableCities(
                                newValue,
                              ); // This will update _availableCities and call setState internally
                            });
                          },
                          validator:
                              (value) =>
                                  _selectedCountry == null
                                      ? 'Please select a country'
                                      : null,
                        ),
                        getVerSpace(FetchPixels.getPixelHeight(20)),
                        // City Select
                        _buildModalSelectField(
                          context: context,
                          label: "City",
                          currentValue: _selectedCity ?? "Select City",
                          items:
                              _availableCities
                                  .map((city) => (value: city, display: city))
                                  .toList(),
                          onItemSelected: (newValue) {
                            // setState is crucial here to update _selectedCity and rebuild dependent UI
                            setState(() {
                              _selectedCity = newValue;
                            });
                          },
                          validator:
                              (value) =>
                                  _selectedCity == null
                                      ? 'Please select a city'
                                      : null,
                          isEnabled:
                              _selectedCountry != null &&
                              _availableCities.isNotEmpty,
                        ),
                        getVerSpace(FetchPixels.getPixelHeight(20)),
                        _buildTextFormField(
                          controller: pincodeController,
                          label: "PinCode / Postal Code",
                          keyboardType: TextInputType.number,
                          validator:
                              (value) =>
                                  value == null || value.isEmpty
                                      ? 'Please enter pincode'
                                      : null,
                          isEnabled: true,
                        ),
                        getVerSpace(FetchPixels.getPixelHeight(20)),
                        // Phone field removed
                        // getVerSpace(FetchPixels.getPixelHeight(20)),
                        // Landmark / Description field removed
                        // getVerSpace(FetchPixels.getPixelHeight(20)),
                        // Latitude field removed
                        // getVerSpace(FetchPixels.getPixelHeight(20)),
                        // Longitude field removed
                        getVerSpace(FetchPixels.getPixelHeight(20)),
                        // Display current location if available (read-only)
                        // This is a placeholder, actual display might differ
                        if (_currentLatitude != null &&
                            _currentLongitude != null)
                          Padding(
                            padding: EdgeInsets.symmetric(
                              vertical: FetchPixels.getPixelHeight(10),
                            ),
                            child: Text(
                              "Location: Lat: ${_currentLatitude!.toStringAsFixed(4)}, Lon: ${_currentLongitude!.toStringAsFixed(4)} (auto-detected)",
                              style: TextStyle(color: daltiTextMuted),
                            ),
                          )
                        else
                          Padding(
                            padding: EdgeInsets.symmetric(
                              vertical: FetchPixels.getPixelHeight(10),
                            ),
                            child: Text(
                              "Location will be auto-detected (defaulting to 0,0 for now).",
                              style: TextStyle(color: daltiTextMuted),
                            ),
                          ),

                        getVerSpace(FetchPixels.getPixelHeight(20)),
                        Row(
                          children: [
                            Checkbox(
                              value: isPrimaryController,
                              onChanged: (bool? value) {
                                setState(() {
                                  isPrimaryController = value ?? false;
                                });
                              },
                              activeColor: daltiPrimary,
                            ),
                            getCustomFont(
                              "Set as primary address",
                              16,
                              Colors.black,
                              1,
                            ),
                          ],
                        ),
                        getVerSpace(FetchPixels.getPixelHeight(20)),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      onWillPop: () async {
        Navigator.of(context).pop(false);
        return false;
      },
    );
  }

  Widget _bottomSaveButton(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: daltiCard,
        boxShadow: [
          BoxShadow(
            color: daltiDividerLine.withOpacity(0.3),
            spreadRadius: 1,
            blurRadius: 5,
            offset: Offset(0, -2),
          ),
        ],
      ),
      padding: EdgeInsets.only(
        left: FetchPixels.getPixelWidth(20),
        right: FetchPixels.getPixelWidth(20),
        bottom:
            FetchPixels.getPixelHeight(30) +
            MediaQuery.of(context).viewInsets.bottom,
        top: FetchPixels.getPixelHeight(15),
      ),
      child: getButton(
        context,
        daltiPrimary,
        _isLoading
            ? 'Saving...'
            : (_isEditMode ? "Save Changes" : "Add New Address"),
        daltiTextOnPrimary,
        _isLoading
            ? () {} // Pass a no-op function if loading
            : () => _saveAddress(), // Pass the call to _saveAddress otherwise
        18,
        weight: FontWeight.w600,
        buttonHeight: FetchPixels.getPixelHeight(60),
        borderRadius: BorderRadius.circular(FetchPixels.getPixelHeight(14)),
      ),
    );
  }
}
