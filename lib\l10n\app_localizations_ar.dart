// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Arabic (`ar`).
class AppLocalizationsAr extends AppLocalizations {
  AppLocalizationsAr([String locale = 'ar']) : super(locale);

  @override
  String get helloWorld => 'مرحباً بالعالم!';

  @override
  String get welcomeMessage => 'أهلاً بك في تطبيقنا';

  @override
  String get chooseLanguage => 'اختر لغتك';

  @override
  String get english => 'الإنجليزية';

  @override
  String get arabic => 'العربية';

  @override
  String get french => 'الفرنسية';

  @override
  String get nextButtonText => 'التالي';

  @override
  String get skipButtonText => 'تخطى';

  @override
  String get intro1Title => 'عنوان الشريحة التعريفية 1 (AR)';

  @override
  String get intro1Description => 'وصف الشريحة التعريفية 1 (AR).';

  @override
  String get intro2Title => 'عنوان الشريحة التعريفية 2 (AR)';

  @override
  String get intro2Description => 'وصف الشريحة التعريفية 2 (AR).';

  @override
  String get intro3Title => 'عنوان الشريحة التعريفية 3 (AR)';

  @override
  String get intro3Description => 'وصف الشريحة التعريفية 3 (AR).';

  @override
  String get preferredMethodTitle => 'اختر طريقتك المفضلة';

  @override
  String get preferredMethodSubtitle =>
      'حدد كيف ترغب في التسجيل أو تسجيل الدخول.';

  @override
  String get useEmailButton => 'استخدام البريد الإلكتروني';

  @override
  String get useMobileButton => 'استخدام رقم الجوال';

  @override
  String get goBackButton => 'العودة';

  @override
  String get loginWithEmailTitle => 'تسجيل الدخول بالبريد الإلكتروني';

  @override
  String get loginWithMobileTitle => 'تسجيل الدخول برقم الجوال';

  @override
  String get loginSubtitle => 'سعداء بلقائك مرة أخرى!';

  @override
  String get emailLabel => 'البريد الإلكتروني';

  @override
  String get phoneNumberLabel => 'رقم الجوال';

  @override
  String get passwordLabel => 'كلمة المرور';

  @override
  String get forgotPasswordButton => 'هل نسيت كلمة المرور؟';

  @override
  String get loginButton => 'تسجيل الدخول';

  @override
  String get dontHaveAccountText => 'ليس لديك حساب؟';

  @override
  String get signUpButtonLoginScreen => ' إنشاء حساب';

  @override
  String get errorPhoneNumberEmpty => 'لا يمكن ترك رقم الجوال فارغًا.';

  @override
  String get errorEmailPasswordEmpty =>
      'لا يمكن ترك البريد الإلكتروني وكلمة المرور فارغين.';

  @override
  String get errorPhonePasswordEmpty =>
      'لا يمكن ترك رقم الجوال وكلمة المرور فارغين.';

  @override
  String errorLoginInvalidResponse(Object statusCode) {
    return 'فشل تسجيل الدخول: استجابة الخادم غير صالحة. الحالة: $statusCode';
  }

  @override
  String get loginSuccess => 'تم تسجيل الدخول بنجاح!';

  @override
  String get errorLoginNoSessionId =>
      'فشل تسجيل الدخول: لم يتم استلام معرف الجلسة.';

  @override
  String errorLoginWithMessageStatus(Object message, Object statusCode) {
    return 'فشل تسجيل الدخول: $message (الحالة: $statusCode)';
  }

  @override
  String errorLoginGeneric(Object errorDetails) {
    return 'حدث خطأ أثناء تسجيل الدخول: $errorDetails';
  }

  @override
  String get signUpTitle => 'إنشاء حساب';

  @override
  String get signUpSubtitleEmail => 'قم بإنشاء حساب باستخدام بريدك الإلكتروني.';

  @override
  String get signUpSubtitleMobile => 'قم بإنشاء حساب باستخدام رقم جوالك.';

  @override
  String get signUpSubtitleGeneric => 'أدخل بياناتك لإنشاء الحساب!';

  @override
  String get firstNameLabel => 'الاسم الأول';

  @override
  String get lastNameLabel => 'الاسم الأخير';

  @override
  String get agreeTermsPrivacyText => 'أوافق على الشروط والخصوصية';

  @override
  String get signUpButton => 'إنشاء حساب';

  @override
  String get alreadyHaveAccountText => 'هل لديك حساب بالفعل؟';

  @override
  String get loginButtonSignUpScreen => ' تسجيل الدخول';

  @override
  String get errorAgreeTerms => 'يرجى الموافقة على الشروط والخصوصية.';

  @override
  String get errorEmailEmptyPreferred =>
      'لا يمكن ترك البريد الإلكتروني فارغًا لأنه طريقتك المفضلة.';

  @override
  String get errorMobileEmptyPreferred =>
      'لا يمكن ترك رقم الجوال فارغًا لأنه طريقتك المفضلة.';

  @override
  String get errorProvideEmailOrPhone =>
      'يرجى تقديم بريد إلكتروني أو رقم جوال.';

  @override
  String get errorProvideSignUpDetails => 'يرجى تقديم تفاصيل لإنشاء الحساب.';

  @override
  String errorUserExists(Object fieldInUse) {
    return '$fieldInUse مستخدم بالفعل.';
  }

  @override
  String errorUserExistsConflict(Object fieldInUse) {
    return '$fieldInUse مستخدم بالفعل (تعارض).';
  }

  @override
  String errorCheckingUser(Object serverMessage, Object statusCode) {
    return 'خطأ في التحقق من المستخدم: $statusCode $serverMessage';
  }

  @override
  String errorFailedCheckUserExists(Object errorDetails) {
    return 'فشل التحقق من وجود المستخدم: $errorDetails';
  }

  @override
  String get errorNoValidOtpIdentifier =>
      'لا يمكن المتابعة بدون بريد إلكتروني أو هاتف صالح لـ OTP.';

  @override
  String get forgotPasswordTitle => 'هل نسيت كلمة المرور؟';

  @override
  String get forgotPasswordSubtitleEmail =>
      'أدخل بريدك الإلكتروني لإعادة تعيين كلمة المرور الخاصة بك.';

  @override
  String get forgotPasswordSubtitleMobile =>
      'أدخل رقم هاتفك لإعادة تعيين كلمة المرور الخاصة بك.';

  @override
  String get phoneNumberWithCodeLabel => 'رقم الجوال (يتطلب رمز الدولة)';

  @override
  String get submitButton => 'إرسال';

  @override
  String get resetPasswordTitle => 'إعادة تعيين كلمة المرور';

  @override
  String get resetPasswordSubtitle => 'أدخل تفاصيل كلمة المرور الجديدة أدناه.';

  @override
  String get oldPasswordLabel => 'كلمة المرور القديمة';

  @override
  String get newPasswordLabel => 'كلمة المرور الجديدة';

  @override
  String get confirmPasswordLabel => 'تأكيد كلمة المرور';

  @override
  String get setLocationDefault => 'تحديد الموقع';

  @override
  String get searchHintText => 'بحث...';

  @override
  String get homeBannerTitle => 'خدمة طلاء الجدران';

  @override
  String get homeBannerSubtitle => 'اجعل جدارك أنيقًا';

  @override
  String get bookNowButton => 'احجز الآن';

  @override
  String get categoriesTitle => 'الفئات';

  @override
  String get seeAllButton => 'عرض الكل';

  @override
  String errorLoadingCategories(Object errorMessage) {
    return 'خطأ: $errorMessage';
  }

  @override
  String get noCategoriesFound => 'لم يتم العثور على فئات.';

  @override
  String get noParentCategoriesFound => 'لم يتم العثور على فئات رئيسية.';

  @override
  String get profileLoading => 'جار التحميل...';

  @override
  String get profileUserFallback => 'مستخدم';

  @override
  String get profileErrorDisplayDetails => 'خطأ في عرض التفاصيل';

  @override
  String get profileGuestUser => 'زائر';

  @override
  String get profilePleaseLogIn => 'الرجاء تسجيل الدخول';

  @override
  String get profileTabTitle => 'الملف الشخصي';

  @override
  String get profileLogoutButton => 'تسجيل الخروج';

  @override
  String get profileSettingsButton => 'الإعدادات';

  @override
  String get profileMyAddressButton => 'عنواني';

  @override
  String get profileMyCardsButton => 'بطاقاتي';

  @override
  String get profileMyProfileButton => 'ملفي الشخصي';

  @override
  String get bookingsTabTitle => 'الحجوزات';

  @override
  String get bookingsTabAll => 'الكل';

  @override
  String get bookingsTabActive => 'نشط';

  @override
  String get bookingsTabCompleted => 'مكتمل';

  @override
  String get bookingsTabCancelled => 'ملغى';

  @override
  String get messagesTabTitle => 'الرسائل';

  @override
  String get messagesSearchHint => 'ابحث في الرسائل...';

  @override
  String get messagesEmptyTitle => 'لا توجد رسائل';

  @override
  String get messagesEmptySubtitle => 'قائمة رسائلك فارغة حاليًا.';

  @override
  String get doctorPrefix => 'د. ';

  @override
  String get unnamedProvider => 'مزود خدمة غير مسمى';

  @override
  String get defaultSpecialization => 'عام';

  @override
  String get naHospital => 'مستشفى غير متوفر';

  @override
  String get slotInfoUnavailable => 'معلومات الفتحة غير متوفرة';

  @override
  String errorFailedToFetchProviders(Object statusCode) {
    return 'فشل جلب مزودي الخدمة (الحالة: $statusCode)';
  }

  @override
  String errorFetchingData(Object errorDetails) {
    return 'خطأ في جلب البيانات: $errorDetails';
  }

  @override
  String searchResultCountForQuery(num count, Object query) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count نتائج',
      one: 'نتيجة واحدة',
      zero: 'لا توجد نتائج',
    );
    return '$_temp0 لـ \'\'$query\'\'';
  }

  @override
  String searchResultCountForQueryInCategory(
    Object category,
    num count,
    Object query,
  ) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count نتائج',
      one: 'نتيجة واحدة',
      zero: 'لا توجد نتائج',
    );
    return '$_temp0 لـ \'\'$query\'\' في $category';
  }

  @override
  String searchResultCountForQueryInCity(Object city, num count, Object query) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count نتائج',
      one: 'نتيجة واحدة',
      zero: 'لا توجد نتائج',
    );
    return '$_temp0 لـ \'\'$query\'\' في $city';
  }

  @override
  String searchResultCountForQueryInCategoryInCity(
    Object category,
    Object city,
    num count,
    Object query,
  ) {
    String _temp0 = intl.Intl.pluralLogic(
      count,
      locale: localeName,
      other: '$count نتائج',
      one: 'نتيجة واحدة',
      zero: 'لا توجد نتائج',
    );
    return '$_temp0 لـ \'\'$query\'\' في $category في $city';
  }

  @override
  String noProvidersFoundForQuery(Object query) {
    return 'لم يتم العثور على مزودي خدمة لـ \'\'$query\'\'.';
  }

  @override
  String noProvidersFoundForQueryWithCategory(Object category, Object query) {
    return 'لم يتم العثور على مزودي خدمة لـ \'\'$query\'\'\nالفئة: $category';
  }

  @override
  String noProvidersFoundForQueryWithCity(Object city, Object query) {
    return 'لم يتم العثور على مزودي خدمة لـ \'\'$query\'\'\nالمدينة: $city';
  }

  @override
  String noProvidersFoundForQueryWithCategoryAndCity(
    Object category,
    Object city,
    Object query,
  ) {
    return 'لم يتم العثور على مزودي خدمة لـ \'\'$query\'\'\nالفئة: $category\nالمدينة: $city';
  }

  @override
  String get searchScreenTitle => 'بحث';

  @override
  String get searchByKeywordHint => 'البحث بالكلمة المفتاحية';

  @override
  String get hospitalPrefix => 'في ';

  @override
  String experienceInYears(Object years) {
    return 'خبرة $years سنوات';
  }

  @override
  String feesDisplay(Object amount, Object currencySymbol) {
    return 'الرسوم $currencySymbol$amount';
  }

  @override
  String reviewCountDisplay(Object count) {
    return '($count تقييمات)';
  }

  @override
  String get nextAvailableSlotTitle => 'الفتحة المتاحة التالية';

  @override
  String get filtersModalTitle => 'عوامل التصفية';

  @override
  String get filtersCategorySectionTitle => 'نوع الخدمة (الفئة)';

  @override
  String get filtersSelectCategoryPlaceholder => 'اختر الفئة';

  @override
  String get filtersCitySectionTitle => 'المدينة (الولاية)';

  @override
  String get filtersEnterCityHint => 'أدخل المدينة أو الولاية';

  @override
  String get filtersClearButton => 'مسح عوامل التصفية';

  @override
  String get filtersApplyButton => 'تطبيق عوامل التصفية';

  @override
  String get filtersAllCategoriesTitle => 'جميع الفئات';

  @override
  String get filtersNoSubcategories => 'لا توجد فئات فرعية.';

  @override
  String get filtersNoCategoriesAvailable => 'لا توجد فئات متاحة.';

  @override
  String errorFetchingCategories(Object errorDetails) {
    return 'خطأ في جلب الفئات: $errorDetails';
  }

  @override
  String get detailScreenCannotSelectPastDate => 'لا يمكن اختيار تاريخ سابق.';

  @override
  String get detailScreenBookAppointment => 'حجز موعد';

  @override
  String get detailScreenServiceLabel => 'الخدمة:';

  @override
  String get detailScreenNoServices => 'لا توجد خدمات متاحة.';

  @override
  String get detailScreenQueueLabel => 'القائمة:';

  @override
  String get detailScreenGeneralAvailability => 'التوفر العام لهذه الخدمة.';

  @override
  String get detailScreenSelectDate => 'اختر التاريخ';

  @override
  String get detailScreenSelectTime => 'اختر الوقت';

  @override
  String get detailScreenPleaseSelectDate => 'الرجاء اختيار تاريخ.';

  @override
  String detailScreenNoSlotsFound(Object date) {
    return 'لا توجد مواعيد متاحة في $date.';
  }

  @override
  String detailScreenAllSlotsBooked(Object date) {
    return 'جميع المواعيد محجوزة في $date.';
  }

  @override
  String get detailScreenConfirmBooking => 'تأكيد الحجز';

  @override
  String get detailScreenCancel => 'إلغاء';

  @override
  String get detailScreenConfirm => 'تأكيد';

  @override
  String get detailScreenIncompleteDetails => 'تفاصيل الحجز غير مكتملة.';

  @override
  String get detailScreenPleaseLogin => 'الرجاء تسجيل الدخول لإجراء الحجز.';

  @override
  String get detailScreenBookingSuccess => 'تم الحجز بنجاح!';

  @override
  String get detailScreenBookingFailed =>
      'فشل الحجز. الرجاء المحاولة مرة أخرى.';

  @override
  String detailScreenErrorOccurred(Object error) {
    return 'حدث خطأ: $error';
  }

  @override
  String detailScreenPointsRequired(Object points) {
    return 'تتطلب هذه الخدمة $points نقطة للحجز.';
  }

  @override
  String get detailScreenNoPhoneNumber => 'رقم الهاتف غير متوفر.';

  @override
  String get detailScreenCannotLaunchCall => 'لا يمكن بدء المكالمة الهاتفية.';

  @override
  String get detailScreenServiceDetail => 'الخدمة:';

  @override
  String get detailScreenQueueDetail => 'القائمة:';

  @override
  String get detailScreenDateDetail => 'التاريخ:';

  @override
  String get detailScreenTimeDetail => 'الوقت:';

  @override
  String detailScreenBook(Object date, Object time) {
    return 'حجز: $time في $date';
  }

  @override
  String get settingsSecurityTitle => 'الأمان';

  @override
  String get settingsHelpTitle => 'المساعدة والدعم';

  @override
  String get settingsPrivacyTitle => 'سياسة الخصوصية';

  @override
  String get settingsTermsTitle => 'شروط الخدمة';

  @override
  String get settingsSecurityContent =>
      'أمانك هو أولويتنا القصوى. نحن نطبق إجراءات أمنية وفقاً لمعايير الصناعة لحماية بياناتك وضمان تجربة آمنة أثناء استخدام خدماتنا.';

  @override
  String get settingsHelpContent =>
      'هل تحتاج إلى مساعدة؟ فريق الدعم لدينا موجود لمساعدتك في أي أسئلة أو استفسارات قد تكون لديك حول خدماتنا.';

  @override
  String get settingsPrivacyContent =>
      'نحن نقدر خصوصيتك. توضح هذه السياسة كيفية جمع واستخدام وحماية معلوماتك الشخصية عند استخدام خدماتنا.';

  @override
  String get settingsTermsContent =>
      'باستخدام خدماتنا، فإنك توافق على هذه الشروط. يرجى قراءتها بعناية حيث أنها تحكم استخدامك لمنصتنا وخدماتنا.';

  @override
  String get notificationsTitle => 'الإشعارات';

  @override
  String get notificationsToday => 'اليوم';

  @override
  String get notificationsYesterday => 'الأمس';

  @override
  String get notificationsRetry => 'إعادة المحاولة';

  @override
  String get notificationsEmpty => 'لا توجد إشعارات حتى الآن!';

  @override
  String get notificationsEmptyDesc => 'سنخبرك عندما يصل شيء ما.';

  @override
  String get queueStatusUpcoming => 'قادم';

  @override
  String get queueStatusCheckedIn => 'تم تسجيل الوصول';

  @override
  String get queueStatusWaitingRoom => 'يرجى الانتظار بشكل مريح';

  @override
  String get queueStatusCalledIn => 'يتم استدعاؤك!';

  @override
  String get queueStatusInProgress => 'الخدمة قيد التنفيذ';

  @override
  String get queueStatusCompleted => 'مكتمل';

  @override
  String get queueStatusSkipped => 'تم التخطي';

  @override
  String get queueStatusRequeued => 'تمت إعادة الإضافة للقائمة';

  @override
  String get queueStatusCanceled => 'ملغى';

  @override
  String get queueStatusUnknown => 'غير معروف';

  @override
  String queueMessageUpcoming(int position) {
    return 'أنت رقم $position في القائمة. يرجى الاستعداد.';
  }

  @override
  String get queueMessageCheckedIn => 'أخبرنا عند وصولك!';

  @override
  String queueMessageWaitingRoom(int position) {
    return 'سيتصل بك مقدم الخدمة قريباً. أنت رقم $position في القائمة.';
  }

  @override
  String get queueMessageCalledIn => 'يرجى التوجه إلى منطقة الخدمة.';

  @override
  String get queueMessageInProgress => 'خدمتك قيد التنفيذ.';

  @override
  String get queueMessageCompleted => 'شكراً لاستخدام خدمتنا!';

  @override
  String get queueMessageSkipped => 'لقد فاتك دورك.';

  @override
  String get queueMessageRequeued => 'تمت إعادة إضافتك إلى القائمة.';

  @override
  String get queueMessageCanceled => 'تم إلغاء حجزك.';

  @override
  String get bookingDetailProviderDetails => 'تفاصيل مقدم الخدمة:';

  @override
  String get bookingDetailAppointmentFor => 'الموعد لـ:';

  @override
  String get bookingDetailServicePrefix => 'الخدمة: ';

  @override
  String get bookingDetailQueuePrefix => 'القائمة: ';

  @override
  String get bookingDetailTimePrefix => 'الوقت: ';

  @override
  String get bookingDetailMapButton => 'الخريطة';

  @override
  String get bookingDetailCallButton => 'اتصال';

  @override
  String get bookingDetailCancelButton => 'إلغاء';

  @override
  String get bookingDetailCancelConfirmTitle => 'إلغاء الموعد؟';

  @override
  String get bookingDetailCancelConfirmMessage =>
      'هل أنت متأكد من رغبتك في إلغاء هذا الموعد؟ لا يمكن التراجع عن هذا الإجراء.';

  @override
  String get bookingDetailCancelSuccess => 'تم إلغاء الموعد بنجاح.';

  @override
  String get bookingDetailCancelError =>
      'فشل إلغاء الموعد. يرجى المحاولة مرة أخرى.';

  @override
  String bookingDetailLocationError(Object error) {
    return 'خطأ في الحصول على الموقع: $error';
  }

  @override
  String get bookingDetailLocationPermissionDenied => 'تم رفض إذن الموقع';

  @override
  String get bookingDetailLocationServiceDisabled => 'خدمات الموقع معطلة';

  @override
  String bookingDetailDirectionsError(Object error) {
    return 'خطأ في الحصول على الاتجاهات: $error';
  }

  @override
  String bookingDetailDistance(Object distance) {
    return 'المسافة: $distance';
  }

  @override
  String bookingDetailDuration(Object duration) {
    return 'المدة: $duration';
  }

  @override
  String get bookingDetailOpenInMaps => 'فتح في الخرائط';

  @override
  String get bookingDetailGetDirections => 'الحصول على الاتجاهات';

  @override
  String get bookingDetailFetchingLocation => 'جارٍ جلب الموقع...';

  @override
  String get bookingDetailCalculatingRoutes => 'جارٍ حساب المسارات...';

  @override
  String get queueLiveStatusTitle => 'حالة الطابور المباشرة';

  @override
  String get queueTimerPaused => 'متوقف مؤقتًا';

  @override
  String get queueLoadingService => 'جارٍ تحميل الخدمة...';

  @override
  String get queueLoadingDate => 'جارٍ تحميل التاريخ...';

  @override
  String get queueLoadingTime => 'جارٍ تحميل الوقت...';

  @override
  String get queueFindingProfessional => 'جارٍ البحث عن متخصص...';

  @override
  String get queueYouAreNext => 'أنت التالي';

  @override
  String get queueYourTurn => 'دورك الآن';

  @override
  String queueYourPosition(Object position) {
    return 'موقعك في الطابور $position';
  }

  @override
  String get queueTimerPausedMessage => 'المؤقت متوقف: في انتظار بدء الخدمة.';

  @override
  String get queueViewQRCode => 'عرض رمز الاستجابة السريعة';

  @override
  String get queueCheckInDetails => 'تفاصيل تسجيل الدخول';

  @override
  String get queueDone => 'تم';

  @override
  String get queueEmptyOrNotListed =>
      'الطابور فارغ حالياً أو لم يتم تسجيل دورك.';

  @override
  String get queueIncomingSwapRequests => 'طلبات التبديل الواردة';

  @override
  String queueSwapRequestsFor(Object time) {
    return 'لمكانك في الساعة $time';
  }

  @override
  String queueSwapStatus(Object status) {
    return 'الحالة: $status';
  }

  @override
  String queueSwapRequested(Object dateTime) {
    return 'تم الطلب في: $dateTime';
  }

  @override
  String get queueReject => 'رفض';

  @override
  String get queueAccept => 'قبول';

  @override
  String queueSwapResponseSent(Object action) {
    return 'تم إرسال رد التبديل: $action';
  }

  @override
  String get queueSwapResponseAccepted => 'تم القبول';

  @override
  String get queueSwapResponseRejected => 'تم الرفض';

  @override
  String get queueNotConnected => 'غير متصل. لا يمكن الرد على التبديل.';

  @override
  String queueMemberLabelYouInProgress(Object position) {
    return 'دورك (قيد التنفيذ) - الموضع $position';
  }

  @override
  String queueMemberLabelYouCompleted(Object position) {
    return 'أنت - تم الانتهاء من الخدمة - الموضع $position';
  }

  @override
  String queueMemberLabelYouStartingSoon(Object position) {
    return 'أنت - سيبدأ قريباً - الموضع $position';
  }

  @override
  String queueMemberLabelYouPosition(Object position) {
    return 'أنت - الموضع $position';
  }

  @override
  String queueMemberLabelOtherStartingSoon(Object name, Object position) {
    return '$name - سيبدأ قريباً - الموضع $position';
  }

  @override
  String queueMemberLabelOtherPosition(Object name, Object position) {
    return '$name - الموضع $position';
  }

  @override
  String get queueMemberLabelSuffixInProgress => ' (قيد التنفيذ)';

  @override
  String get queueMemberLabelSuffixCompleted => ' (تم الانتهاء)';
}
