class ModelDetailedAddress {
  final int? id;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? address;
  final String? city;
  final String? state;
  final String? postalCode;
  final String? country;
  final double? latitude;
  final double? longitude;
  final String? description;
  final bool? isPrimary;
  final String? userId;

  ModelDetailedAddress({
    this.id,
    this.createdAt,
    this.updatedAt,
    this.address,
    this.city,
    this.state,
    this.postalCode,
    this.country,
    this.latitude,
    this.longitude,
    this.description,
    this.isPrimary,
    this.userId,
  });

  factory ModelDetailedAddress.fromJson(Map<String, dynamic> json) {
    return ModelDetailedAddress(
      id: json['id'] as int?,
      createdAt:
          json['createdAt'] == null
              ? null
              : DateTime.tryParse(json['createdAt'] as String),
      updatedAt:
          json['updatedAt'] == null
              ? null
              : DateTime.tryParse(json['updatedAt'] as String),
      address: json['address'] as String?,
      city: json['city'] as String?,
      state: json['state'] as String?,
      postalCode: json['postalCode'] as String?,
      country: json['country'] as String?,
      latitude: (json['latitude'] as num?)?.toDouble(),
      longitude: (json['longitude'] as num?)?.toDouble(),
      description: json['description'] as String?,
      isPrimary: json['isPrimary'] as bool?,
      userId: json['userId'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'createdAt': createdAt?.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'address': address,
      'city': city,
      'state': state,
      'postalCode': postalCode,
      'country': country,
      'latitude': latitude,
      'longitude': longitude,
      'description': description,
      'isPrimary': isPrimary,
      'userId': userId,
    };
  }
}
